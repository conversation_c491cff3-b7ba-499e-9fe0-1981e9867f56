"""
رسوم بيانية محسنة وواضحة
"""
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QPainter, QColor, QBrush, QPen, QFont, QLinearGradient
import math
import random

from accounting_app.ui.styles import AppStyles


class EnhancedPieChart(QWidget):
    """رسم بياني دائري محسن"""
    
    def __init__(self, title="رسم دائري", data=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.data = data or [
            ("منتجات إلكترونية", 35, "#FF6B6B"),
            ("ملابس", 25, "#4ECDC4"), 
            ("أدوات منزلية", 20, "#45B7D1"),
            ("كتب", 15, "#96CEB4"),
            ("أخرى", 5, "#FFEAA7")
        ]
        self.setMinimumSize(400, 350)
        self.animation_angle = 0
        
        # إعداد الأنيميشن
        self.setup_animation()
    
    def setup_animation(self):
        """إعداد الأنيميشن"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_animation)
        self.timer.start(50)  # تحديث كل 50ms
        
    def update_animation(self):
        """تحديث الأنيميشن"""
        self.animation_angle += 1
        if self.animation_angle >= 360:
            self.animation_angle = 0
        self.update()
    
    def paintEvent(self, event):
        """رسم الرسم البياني الدائري"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setRenderHint(QPainter.SmoothPixmapTransform)
        
        # خلفية متدرجة
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(255, 255, 255))
        gradient.setColorAt(1, QColor(248, 249, 250))
        painter.fillRect(self.rect(), gradient)
        
        # رسم العنوان
        painter.setPen(QColor(AppStyles.PRIMARY_COLOR))
        painter.setFont(QFont("Arial", 16, QFont.Bold))
        title_rect = painter.fontMetrics().boundingRect(self.title)
        painter.drawText((self.width() - title_rect.width()) // 2, 30, self.title)
        
        # حساب المجموع الكلي
        total = sum(item[1] for item in self.data)
        
        # معلومات الدائرة
        circle_size = 200
        center_x = self.width() // 2
        center_y = self.height() // 2 + 20
        rect_x = center_x - circle_size // 2
        rect_y = center_y - circle_size // 2
        
        # رسم ظل خفيف
        shadow_offset = 3
        painter.setBrush(QBrush(QColor(0, 0, 0, 30)))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(rect_x + shadow_offset, rect_y + shadow_offset, circle_size, circle_size)
        
        # رسم القطاعات
        start_angle = 0
        for i, (label, value, color) in enumerate(self.data):
            span_angle = int((value / total) * 360 * 16)  # Qt uses 1/16 degrees
            
            # تدرج لوني للقطاع
            segment_gradient = QLinearGradient(rect_x, rect_y, rect_x + circle_size, rect_y + circle_size)
            base_color = QColor(color)
            segment_gradient.setColorAt(0, base_color.lighter(120))
            segment_gradient.setColorAt(1, base_color.darker(110))
            
            painter.setBrush(QBrush(segment_gradient))
            painter.setPen(QPen(QColor("white"), 3))
            painter.drawPie(rect_x, rect_y, circle_size, circle_size, start_angle, span_angle)
            
            # رسم النسبة المئوية في وسط القطاع
            if value > 5:  # فقط للقطاعات الكبيرة
                mid_angle = (start_angle + span_angle // 2) / 16 / 180 * math.pi
                text_radius = circle_size // 3
                text_x = center_x + text_radius * math.cos(mid_angle)
                text_y = center_y + text_radius * math.sin(mid_angle)
                
                percentage = f"{(value/total)*100:.1f}%"
                painter.setPen(QColor("white"))
                painter.setFont(QFont("Arial", 10, QFont.Bold))
                text_rect = painter.fontMetrics().boundingRect(percentage)
                painter.drawText(int(text_x - text_rect.width()//2), 
                               int(text_y + text_rect.height()//2), percentage)
            
            start_angle += span_angle
        
        # رسم الأسطورة
        self.draw_legend(painter)
    
    def draw_legend(self, painter):
        """رسم الأسطورة"""
        legend_x = 20
        legend_y = self.height() - 150
        
        painter.setPen(QColor(AppStyles.TEXT_PRIMARY))
        painter.setFont(QFont("Arial", 10))
        
        for i, (label, value, color) in enumerate(self.data):
            y_pos = legend_y + i * 25
            
            # رسم المربع الملون
            painter.setBrush(QBrush(QColor(color)))
            painter.setPen(QPen(QColor(color)))
            painter.drawRoundedRect(legend_x, y_pos, 15, 15, 3, 3)
            
            # رسم النص
            painter.setPen(QColor(AppStyles.TEXT_PRIMARY))
            text = f"{label}: {value}"
            painter.drawText(legend_x + 25, y_pos + 12, text)


class EnhancedBarChart(QWidget):
    """رسم بياني عمودي محسن"""
    
    def __init__(self, title="رسم عمودي", data=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.data = data or [
            ("يناير", 12000), ("فبراير", 15000), ("مارس", 18000),
            ("أبريل", 14000), ("مايو", 22000), ("يونيو", 19000)
        ]
        self.setMinimumSize(450, 350)
        self.animation_progress = 0
        
        # إعداد الأنيميشن
        self.setup_animation()
    
    def setup_animation(self):
        """إعداد الأنيميشن"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_animation)
        self.timer.start(30)
        
    def update_animation(self):
        """تحديث الأنيميشن"""
        if self.animation_progress < 100:
            self.animation_progress += 2
            self.update()
    
    def paintEvent(self, event):
        """رسم الرسم البياني العمودي"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # خلفية متدرجة
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(255, 255, 255))
        gradient.setColorAt(1, QColor(248, 249, 250))
        painter.fillRect(self.rect(), gradient)
        
        # رسم العنوان
        painter.setPen(QColor(AppStyles.PRIMARY_COLOR))
        painter.setFont(QFont("Arial", 16, QFont.Bold))
        title_rect = painter.fontMetrics().boundingRect(self.title)
        painter.drawText((self.width() - title_rect.width()) // 2, 30, self.title)
        
        # معلومات الرسم
        margin = 50
        chart_width = self.width() - 2 * margin
        chart_height = self.height() - 120
        chart_bottom = self.height() - 50
        
        # العثور على القيمة القصوى
        max_value = max(item[1] for item in self.data)
        
        # رسم الشبكة
        self.draw_grid(painter, margin, 50, chart_width, chart_height, max_value)
        
        # رسم الأعمدة
        bar_width = chart_width // len(self.data) * 0.7
        bar_spacing = chart_width // len(self.data)
        
        colors = ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", "#DDA0DD"]
        
        for i, (label, value) in enumerate(self.data):
            x = margin + i * bar_spacing + (bar_spacing - bar_width) // 2
            bar_height = (value / max_value) * chart_height * (self.animation_progress / 100)
            y = chart_bottom - bar_height
            
            # تدرج لوني للعمود
            bar_gradient = QLinearGradient(x, y, x, chart_bottom)
            color = QColor(colors[i % len(colors)])
            bar_gradient.setColorAt(0, color.lighter(130))
            bar_gradient.setColorAt(1, color.darker(110))
            
            painter.setBrush(QBrush(bar_gradient))
            painter.setPen(QPen(QColor("white"), 2))
            painter.drawRoundedRect(int(x), int(y), int(bar_width), int(bar_height), 5, 5)
            
            # رسم القيمة أعلى العمود
            if self.animation_progress > 80:
                painter.setPen(QColor(AppStyles.TEXT_PRIMARY))
                painter.setFont(QFont("Arial", 10, QFont.Bold))
                value_text = f"{value:,.0f}"
                text_rect = painter.fontMetrics().boundingRect(value_text)
                painter.drawText(int(x + bar_width//2 - text_rect.width()//2), 
                               int(y - 5), value_text)
            
            # رسم التسمية
            painter.setPen(QColor(AppStyles.TEXT_SECONDARY))
            painter.setFont(QFont("Arial", 9))
            label_rect = painter.fontMetrics().boundingRect(label)
            painter.drawText(int(x + bar_width//2 - label_rect.width()//2), 
                           chart_bottom + 20, label)
    
    def draw_grid(self, painter, x, y, width, height, max_value):
        """رسم الشبكة"""
        painter.setPen(QPen(QColor(200, 200, 200), 1))
        
        # خطوط أفقية
        for i in range(5):
            line_y = y + (height * i / 4)
            painter.drawLine(x, int(line_y), x + width, int(line_y))
            
            # قيم المحور الصادي
            value = max_value * (4 - i) / 4
            painter.setPen(QColor(AppStyles.TEXT_SECONDARY))
            painter.setFont(QFont("Arial", 8))
            painter.drawText(x - 40, int(line_y + 5), f"{value:,.0f}")
            painter.setPen(QPen(QColor(200, 200, 200), 1))


class EnhancedLineChart(QWidget):
    """رسم بياني خطي محسن"""
    
    def __init__(self, title="رسم خطي", data=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.data = data or [
            (1, 5000), (2, 7000), (3, 6500), (4, 8000),
            (5, 9500), (6, 11000), (7, 10500), (8, 12000)
        ]
        self.setMinimumSize(450, 350)
        self.animation_progress = 0
        
        # إعداد الأنيميشن
        self.setup_animation()
    
    def setup_animation(self):
        """إعداد الأنيميشن"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_animation)
        self.timer.start(30)
        
    def update_animation(self):
        """تحديث الأنيميشن"""
        if self.animation_progress < 100:
            self.animation_progress += 1.5
            self.update()
    
    def paintEvent(self, event):
        """رسم الرسم البياني الخطي"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # خلفية متدرجة
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(255, 255, 255))
        gradient.setColorAt(1, QColor(248, 249, 250))
        painter.fillRect(self.rect(), gradient)
        
        # رسم العنوان
        painter.setPen(QColor(AppStyles.PRIMARY_COLOR))
        painter.setFont(QFont("Arial", 16, QFont.Bold))
        title_rect = painter.fontMetrics().boundingRect(self.title)
        painter.drawText((self.width() - title_rect.width()) // 2, 30, self.title)
        
        # معلومات الرسم
        margin = 60
        chart_width = self.width() - 2 * margin
        chart_height = self.height() - 120
        chart_bottom = self.height() - 50
        chart_left = margin
        
        # العثور على القيم القصوى والدنيا
        max_value = max(item[1] for item in self.data)
        min_value = min(item[1] for item in self.data)
        max_x = max(item[0] for item in self.data)
        min_x = min(item[0] for item in self.data)
        
        # رسم الشبكة
        self.draw_grid(painter, chart_left, 50, chart_width, chart_height, 
                      min_value, max_value, min_x, max_x)
        
        # تحويل البيانات إلى إحداثيات الشاشة
        points = []
        for x, y in self.data:
            screen_x = chart_left + ((x - min_x) / (max_x - min_x)) * chart_width
            screen_y = chart_bottom - ((y - min_value) / (max_value - min_value)) * chart_height
            points.append((screen_x, screen_y))
        
        # رسم منطقة تحت الخط (تدرج)
        if len(points) > 1 and self.animation_progress > 50:
            area_gradient = QLinearGradient(0, 50, 0, chart_bottom)
            area_gradient.setColorAt(0, QColor(AppStyles.PRIMARY_COLOR).lighter(150))
            area_gradient.setColorAt(1, QColor(AppStyles.PRIMARY_COLOR).lighter(200))
            
            painter.setBrush(QBrush(area_gradient))
            painter.setPen(Qt.NoPen)
            
            # إنشاء مضلع للمنطقة
            from PySide6.QtGui import QPolygon
            from PySide6.QtCore import QPoint
            
            area_points = [QPoint(int(chart_left), chart_bottom)]
            animated_count = int(len(points) * self.animation_progress / 100)
            for i in range(min(animated_count, len(points))):
                area_points.append(QPoint(int(points[i][0]), int(points[i][1])))
            area_points.append(QPoint(int(points[min(animated_count-1, len(points)-1)][0]), chart_bottom))
            
            polygon = QPolygon(area_points)
            painter.drawPolygon(polygon)
        
        # رسم الخط الرئيسي
        painter.setPen(QPen(QColor(AppStyles.PRIMARY_COLOR), 3))
        animated_count = int(len(points) * self.animation_progress / 100)
        
        for i in range(1, min(animated_count, len(points))):
            painter.drawLine(int(points[i-1][0]), int(points[i-1][1]),
                           int(points[i][0]), int(points[i][1]))
        
        # رسم النقاط
        if self.animation_progress > 70:
            for i, (screen_x, screen_y) in enumerate(points[:animated_count]):
                if i < animated_count:
                    # دائرة خارجية
                    painter.setBrush(QBrush(QColor("white")))
                    painter.setPen(QPen(QColor(AppStyles.PRIMARY_COLOR), 2))
                    painter.drawEllipse(int(screen_x - 6), int(screen_y - 6), 12, 12)
                    
                    # دائرة داخلية
                    painter.setBrush(QBrush(QColor(AppStyles.PRIMARY_COLOR)))
                    painter.setPen(Qt.NoPen)
                    painter.drawEllipse(int(screen_x - 3), int(screen_y - 3), 6, 6)
                    
                    # قيمة النقطة
                    painter.setPen(QColor(AppStyles.TEXT_PRIMARY))
                    painter.setFont(QFont("Arial", 9))
                    value_text = f"{self.data[i][1]:,.0f}"
                    text_rect = painter.fontMetrics().boundingRect(value_text)
                    painter.drawText(int(screen_x - text_rect.width()//2), 
                                   int(screen_y - 15), value_text)
    
    def draw_grid(self, painter, x, y, width, height, min_val, max_val, min_x, max_x):
        """رسم الشبكة"""
        painter.setPen(QPen(QColor(200, 200, 200), 1))
        
        # خطوط أفقية
        for i in range(5):
            line_y = y + (height * i / 4)
            painter.drawLine(x, int(line_y), x + width, int(line_y))
            
            # قيم المحور الصادي
            value = max_val - (max_val - min_val) * i / 4
            painter.setPen(QColor(AppStyles.TEXT_SECONDARY))
            painter.setFont(QFont("Arial", 8))
            painter.drawText(x - 50, int(line_y + 5), f"{value:,.0f}")
            painter.setPen(QPen(QColor(200, 200, 200), 1))
        
        # خطوط عمودية
        for i in range(len(self.data)):
            line_x = x + (width * i / (len(self.data) - 1))
            painter.drawLine(int(line_x), y, int(line_x), y + height)
            
            # قيم المحور السيني
            painter.setPen(QColor(AppStyles.TEXT_SECONDARY))
            painter.setFont(QFont("Arial", 8))
            label = str(self.data[i][0]) if i < len(self.data) else ""
            painter.drawText(int(line_x - 10), y + height + 20, label)
            painter.setPen(QPen(QColor(200, 200, 200), 1))


class ChartsShowcaseWidget(QWidget):
    """ودجت عرض مجموعة من الرسوم البيانية المحسنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان
        title_label = QLabel("الرسوم البيانية التفاعلية")
        title_label.setStyleSheet(f"""
            color: {AppStyles.PRIMARY_COLOR};
            font-size: {AppStyles.FONT_SIZE_XLARGE}px;
            font-weight: bold;
            padding: 20px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f8f9fa, stop:1 #e9ecef);
            border-radius: {AppStyles.BORDER_RADIUS}px;
            margin-bottom: 20px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # صف الرسوم البيانية
        charts_row = QHBoxLayout()
        charts_row.setSpacing(15)
        
        # الرسم الدائري
        pie_chart = EnhancedPieChart("توزيع المبيعات حسب الفئة")
        pie_frame = self.create_chart_frame(pie_chart)
        
        # الرسم العمودي
        bar_chart = EnhancedBarChart("المبيعات الشهرية")
        bar_frame = self.create_chart_frame(bar_chart)
        
        charts_row.addWidget(pie_frame)
        charts_row.addWidget(bar_frame)
        
        # الرسم الخطي (الصف الثاني)
        line_chart = EnhancedLineChart("نمو الأرباح")
        line_frame = self.create_chart_frame(line_chart)
        
        layout.addWidget(title_label)
        layout.addLayout(charts_row)
        layout.addWidget(line_frame)
    
    def create_chart_frame(self, chart_widget):
        """إنشاء إطار للرسم البياني"""
        frame = QFrame()
        frame.setStyleSheet(f"""
            QFrame {{
                background: white;
                border-radius: {AppStyles.BORDER_RADIUS}px;
                border: 2px solid {AppStyles.BORDER_COLOR};
            }}
        """)
        frame_layout = QVBoxLayout(frame)
        frame_layout.addWidget(chart_widget)
        return frame