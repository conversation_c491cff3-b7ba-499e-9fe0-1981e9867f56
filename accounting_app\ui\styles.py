"""
أنماط وألوان التطبيق
"""


class AppStyles:
    """فئة تحتوي على أنماط وألوان التطبيق"""
    
    # الألوان الرئيسية - تصميم عصري ومتطور
    PRIMARY_COLOR = "#6366F1"  # بنفسجي عصري
    SECONDARY_COLOR = "#8B5CF6"  # بنفسجي فاتح
    ACCENT_COLOR = "#F59E0B"  # ذهبي عصري
    BACKGROUND_COLOR = "#F8FAFC"  # أبيض مائل للرمادي
    CARD_BACKGROUND = "#FFFFFF"  # أبيض
    SIDEBAR_GRADIENT = "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #667EEA, stop:1 #764BA2)"
    HEADER_GRADIENT = "qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #6366F1, stop:1 #8B5CF6)"
    TEXT_PRIMARY = "#1F2937"  # رمادي داكن عصري
    TEXT_SECONDARY = "#6B7280"  # رمادي متوسط
    ERROR_COLOR = "#EF4444"  # أحمر عصري
    SUCCESS_COLOR = "#10B981"  # أخضر زمردي
    WARNING_COLOR = "#F59E0B"  # ذهبي
    INFO_COLOR = "#3B82F6"  # أزرق عصري
    HIGHLIGHT_COLOR = "#4F46E5"  # بنفسجي للتمييز
    
    # أنماط الخطوط
    FONT_FAMILY = "Arial, Tahoma, sans-serif"
    FONT_SIZE_SMALL = 11
    FONT_SIZE_NORMAL = 13
    FONT_SIZE_LARGE = 15
    FONT_SIZE_XLARGE = 20
    FONT_SIZE_XXLARGE = 26
    
    # أنماط الحدود - تصميم عصري
    BORDER_RADIUS = 12
    BORDER_WIDTH = 1
    BORDER_COLOR = "#E5E7EB"
    
    # أنماط الظلال (استخدام border بدلاً من box-shadow لتوافق أفضل مع Qt)
    SHADOW_SMALL = "1px solid rgba(0, 0, 0, 0.1)"
    SHADOW_MEDIUM = "1px solid rgba(0, 0, 0, 0.12)"
    SHADOW_LARGE = "2px solid rgba(0, 0, 0, 0.15)"
    SHADOW = SHADOW_MEDIUM  # للتوافق مع الكود القديم
    
    # أنماط الأزرار
    BUTTON_STYLE = f"""
        QPushButton {{
            background-color: {PRIMARY_COLOR};
            color: white;
            border: none;
            border-radius: {BORDER_RADIUS}px;
            padding: 10px 20px;
            font-size: {FONT_SIZE_NORMAL}px;
            font-weight: bold;
        }}
        
        QPushButton:hover {{
            background-color: {HIGHLIGHT_COLOR};
        }}
        
        QPushButton:pressed {{
            background-color: {SECONDARY_COLOR};
            padding: 11px 19px 9px 21px;
        }}
        
        QPushButton:disabled {{
            background-color: #BDBDBD;
            color: #E0E0E0;
        }}
    """
    
    BUTTON_SECONDARY_STYLE = f"""
        QPushButton {{
            background-color: white;
            color: {PRIMARY_COLOR};
            border: 1px solid {PRIMARY_COLOR};
            border-radius: {BORDER_RADIUS}px;
            padding: 10px 20px;
            font-size: {FONT_SIZE_NORMAL}px;
            font-weight: bold;
        }}
        
        QPushButton:hover {{
            background-color: rgba(46, 80, 144, 0.1);
            border-color: {HIGHLIGHT_COLOR};
            color: {HIGHLIGHT_COLOR};
        }}
        
        QPushButton:pressed {{
            background-color: rgba(46, 80, 144, 0.2);
            border-color: {SECONDARY_COLOR};
            color: {SECONDARY_COLOR};
            padding: 11px 19px 9px 21px;
        }}
        
        QPushButton:disabled {{
            border-color: #BDBDBD;
            color: #BDBDBD;
        }}
    """
    
    BUTTON_ACCENT_STYLE = f"""
        QPushButton {{
            background-color: {ACCENT_COLOR};
            color: white;
            border: none;
            border-radius: {BORDER_RADIUS}px;
            padding: 8px 16px;
            font-size: {FONT_SIZE_NORMAL}px;
        }}
        
        QPushButton:hover {{
            background-color: #E64A19;
        }}
        
        QPushButton:pressed {{
            background-color: #BF360C;
        }}
        
        QPushButton:disabled {{
            background-color: #BDBDBD;
            color: #757575;
        }}
    """
    
    # أنماط حقول الإدخال
    INPUT_STYLE = f"""
        QLineEdit, QTextEdit, QComboBox {{
            background-color: white;
            border: {BORDER_WIDTH}px solid {BORDER_COLOR};
            border-radius: {BORDER_RADIUS}px;
            padding: 10px;
            font-size: {FONT_SIZE_NORMAL}px;
            color: {TEXT_PRIMARY};
            selection-background-color: {PRIMARY_COLOR};
            selection-color: white;
        }}
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
            border: 2px solid {PRIMARY_COLOR};
            background-color: #FAFAFA;
        }}
        
        QLineEdit:hover, QTextEdit:hover, QComboBox:hover {{
            border: {BORDER_WIDTH}px solid {HIGHLIGHT_COLOR};
            background-color: #FAFAFA;
        }}
        
        QLineEdit:disabled, QTextEdit:disabled, QComboBox:disabled {{
            background-color: #F5F5F5;
            color: {TEXT_SECONDARY};
            border: {BORDER_WIDTH}px solid #E0E0E0;
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}
        
        QComboBox::down-arrow {{
            image: url(icons/dropdown_arrow.png);
            width: 12px;
            height: 12px;
        }}
    """
    
    # أنماط التسميات
    LABEL_STYLE = f"""
        QLabel {{
            color: {TEXT_PRIMARY};
            font-size: {FONT_SIZE_NORMAL}px;
            margin-bottom: 5px;
        }}
    """
    
    LABEL_HEADER_STYLE = f"""
        QLabel {{
            color: {PRIMARY_COLOR};
            font-size: {FONT_SIZE_XLARGE}px;
            font-weight: bold;
            margin-bottom: 15px;
        }}
    """
    
    LABEL_SUBHEADER_STYLE = f"""
        QLabel {{
            color: {TEXT_SECONDARY};
            font-size: {FONT_SIZE_LARGE}px;
            font-weight: bold;
            margin-bottom: 10px;
        }}
    """
    
    LABEL_ACCENT_STYLE = f"""
        QLabel {{
            color: {ACCENT_COLOR};
            font-size: {FONT_SIZE_NORMAL}px;
            font-weight: bold;
        }}
    """
    
    # أنماط الجداول
    TABLE_STYLE = f"""
        QTableView {{
            background-color: white;
            border: {BORDER_WIDTH}px solid {BORDER_COLOR};
            border-radius: {BORDER_RADIUS}px;
            gridline-color: #E0E0E0;
            alternate-background-color: #F8F9FA;
            selection-background-color: rgba(46, 80, 144, 0.2);
            selection-color: {TEXT_PRIMARY};
        }}
        
        QTableView::item {{
            padding: 8px;
            border-bottom: 1px solid #F0F0F0;
        }}
        
        QTableView::item:selected {{
            background-color: rgba(46, 80, 144, 0.2);
            color: {TEXT_PRIMARY};
        }}
        
        QTableView::item:hover {{
            background-color: rgba(46, 80, 144, 0.1);
        }}
        
        QHeaderView::section {{
            background-color: {PRIMARY_COLOR};
            color: white;
            padding: 10px;
            border: none;
            font-weight: bold;
        }}
        
        QHeaderView::section:hover {{
            background-color: {HIGHLIGHT_COLOR};
        }}
        
        QTableView QTableCornerButton::section {{
            background-color: {PRIMARY_COLOR};
            border: none;
        }}
    """
    
    # أنماط القوائم
    MENU_STYLE = f"""
        QMenu {{
            background-color: white;
            border: {BORDER_WIDTH}px solid {BORDER_COLOR};
            border-radius: {BORDER_RADIUS}px;
            padding: 5px;
        }}
        
        QMenu::item {{
            padding: 10px 20px;
            border-radius: {BORDER_RADIUS}px;
        }}
        
        QMenu::item:selected {{
            background-color: rgba(46, 80, 144, 0.1);
            color: {PRIMARY_COLOR};
        }}
        
        QMenu::separator {{
            height: 1px;
            background-color: {BORDER_COLOR};
            margin: 5px 15px;
        }}
    """
    
    # أنماط شريط الأدوات
    TOOLBAR_STYLE = f"""
        QToolBar {{
            background-color: {PRIMARY_COLOR};
            border: none;
            spacing: 8px;
            padding: 8px;
        }}
        
        QToolBar::separator {{
            background-color: rgba(255, 255, 255, 0.3);
            width: 1px;
            height: 24px;
            margin: 0 10px;
        }}
        
        QToolButton {{
            background-color: transparent;
            color: white;
            border: none;
            border-radius: {BORDER_RADIUS}px;
            padding: 8px;
            font-size: {FONT_SIZE_NORMAL}px;
        }}
        
        QToolButton:hover {{
            background-color: rgba(255, 255, 255, 0.2);
        }}
        
        QToolButton:pressed {{
            background-color: rgba(255, 255, 255, 0.1);
        }}
        
        QToolButton:checked {{
            background-color: rgba(255, 255, 255, 0.3);
        }}
    """
    
    # أنماط شريط الحالة
    STATUSBAR_STYLE = f"""
        QStatusBar {{
            background-color: {SECONDARY_COLOR};
            color: white;
            padding: 8px;
            font-weight: bold;
        }}
        
        QStatusBar::item {{
            border: none;
        }}
    """
    
    # أنماط مربعات الاختيار
    CHECKBOX_STYLE = f"""
        QCheckBox {{
            color: {TEXT_PRIMARY};
            font-size: {FONT_SIZE_NORMAL}px;
            spacing: 10px;
        }}
        
        QCheckBox::indicator {{
            width: 20px;
            height: 20px;
            border: {BORDER_WIDTH}px solid {BORDER_COLOR};
            border-radius: 4px;
            background-color: white;
        }}
        
        QCheckBox::indicator:checked {{
            background-color: {PRIMARY_COLOR};
            border: {BORDER_WIDTH}px solid {PRIMARY_COLOR};
            image: url(:/icons/check.png);
        }}
        
        QCheckBox::indicator:unchecked:hover {{
            border: {BORDER_WIDTH}px solid {HIGHLIGHT_COLOR};
        }}
        
        QCheckBox::indicator:checked:hover {{
            background-color: {HIGHLIGHT_COLOR};
            border-color: {HIGHLIGHT_COLOR};
        }}
        
        QCheckBox:disabled {{
            color: {TEXT_SECONDARY};
        }}
        
        QCheckBox::indicator:disabled {{
            border-color: #E0E0E0;
            background-color: #F5F5F5;
        }}
    """
    
    # أنماط أزرار الراديو
    RADIO_STYLE = f"""
        QRadioButton {{
            color: {TEXT_PRIMARY};
            font-size: {FONT_SIZE_NORMAL}px;
            spacing: 10px;
        }}
        
        QRadioButton::indicator {{
            width: 20px;
            height: 20px;
            border: {BORDER_WIDTH}px solid {BORDER_COLOR};
            border-radius: 10px;
            background-color: white;
        }}
        
        QRadioButton::indicator:checked {{
            background-color: {PRIMARY_COLOR};
            border: 5px solid white;
            outline: {BORDER_WIDTH}px solid {PRIMARY_COLOR};
        }}
        
        QRadioButton::indicator:unchecked:hover {{
            border: {BORDER_WIDTH}px solid {HIGHLIGHT_COLOR};
        }}
        
        QRadioButton::indicator:checked:hover {{
            background-color: {HIGHLIGHT_COLOR};
            outline: {BORDER_WIDTH}px solid {HIGHLIGHT_COLOR};
        }}
        
        QRadioButton:disabled {{
            color: {TEXT_SECONDARY};
        }}
        
        QRadioButton::indicator:disabled {{
            border-color: #E0E0E0;
            background-color: #F5F5F5;
        }}
    """
    
    # أنماط شريط التقدم
    PROGRESSBAR_STYLE = f"""
        QProgressBar {{
            border: none;
            border-radius: {BORDER_RADIUS}px;
            background-color: #E0E0E0;
            text-align: center;
            color: {TEXT_PRIMARY};
            height: 12px;
            font-size: {FONT_SIZE_SMALL}px;
            font-weight: bold;
        }}
        
        QProgressBar::chunk {{
            background-color: {PRIMARY_COLOR};
            border-radius: {BORDER_RADIUS}px;
        }}
    """
    
    # أنماط الإطارات
    FRAME_STYLE = f"""
        QFrame {{
            background-color: white;
            border: {BORDER_WIDTH}px solid {BORDER_COLOR};
            border-radius: {BORDER_RADIUS}px;
        }}
    """
    
    CARD_STYLE = f"""
        QFrame {{
            background-color: white;
            border-radius: {BORDER_RADIUS}px;
            padding: 15px;
        }}
    """
    
    CARD_SHADOW_STYLE = f"""
        QFrame {{
            background-color: white;
            border-radius: {BORDER_RADIUS}px;
            padding: 15px;
            border: none;
        }}
    """
    
    # أنماط علامات التبويب
    TAB_STYLE = f"""
        QTabWidget::pane {{
            border: {BORDER_WIDTH}px solid {BORDER_COLOR};
            border-radius: {BORDER_RADIUS}px;
            background-color: white;
            top: -1px;
        }}
        
        QTabBar::tab {{
            background-color: {BACKGROUND_COLOR};
            color: {TEXT_SECONDARY};
            border: {BORDER_WIDTH}px solid {BORDER_COLOR};
            border-bottom: none;
            border-top-left-radius: {BORDER_RADIUS}px;
            border-top-right-radius: {BORDER_RADIUS}px;
            padding: 10px 20px;
            margin-right: 4px;
            font-size: {FONT_SIZE_NORMAL}px;
        }}
        
        QTabBar::tab:selected {{
            background-color: white;
            color: {PRIMARY_COLOR};
            border-bottom: 3px solid {PRIMARY_COLOR};
            font-weight: bold;
        }}
        
        QTabBar::tab:!selected {{
            margin-top: 2px;
        }}
        
        QTabBar::tab:hover {{
            background-color: #EAEAEA;
            color: {TEXT_PRIMARY};
        }}
    """
    
    # أنماط القوائم المنسدلة
    COMBOBOX_STYLE = f"""
        QComboBox {{
            background-color: white;
            border: {BORDER_WIDTH}px solid {BORDER_COLOR};
            border-radius: {BORDER_RADIUS}px;
            padding: 8px;
            font-size: {FONT_SIZE_NORMAL}px;
            color: {TEXT_PRIMARY};
        }}
        
        QComboBox:focus {{
            border: {BORDER_WIDTH}px solid {PRIMARY_COLOR};
        }}
        
        QComboBox::drop-down {{
            subcontrol-origin: padding;
            subcontrol-position: top right;
            width: 20px;
            border-left: {BORDER_WIDTH}px solid {BORDER_COLOR};
        }}
        
        QComboBox QAbstractItemView {{
            background-color: white;
            border: {BORDER_WIDTH}px solid {BORDER_COLOR};
            border-radius: {BORDER_RADIUS}px;
            selection-background-color: {PRIMARY_COLOR};
            selection-color: white;
        }}
    """
    
    # أنماط الرسائل
    MESSAGE_ERROR_STYLE = f"""
        QLabel {{
            color: {ERROR_COLOR};
            font-size: {FONT_SIZE_NORMAL}px;
            padding: 8px;
            background-color: #FFEBEE;
            border: {BORDER_WIDTH}px solid {ERROR_COLOR};
            border-radius: {BORDER_RADIUS}px;
        }}
    """
    
    MESSAGE_SUCCESS_STYLE = f"""
        QLabel {{
            color: {SUCCESS_COLOR};
            font-size: {FONT_SIZE_NORMAL}px;
            padding: 8px;
            background-color: #E8F5E9;
            border: {BORDER_WIDTH}px solid {SUCCESS_COLOR};
            border-radius: {BORDER_RADIUS}px;
        }}
    """
    
    MESSAGE_WARNING_STYLE = f"""
        QLabel {{
            color: {WARNING_COLOR};
            font-size: {FONT_SIZE_NORMAL}px;
            padding: 8px;
            background-color: #FFF8E1;
            border: {BORDER_WIDTH}px solid {WARNING_COLOR};
            border-radius: {BORDER_RADIUS}px;
        }}
    """
    
    MESSAGE_INFO_STYLE = f"""
        QLabel {{
            color: {INFO_COLOR};
            font-size: {FONT_SIZE_NORMAL}px;
            padding: 8px;
            background-color: #E3F2FD;
            border: {BORDER_WIDTH}px solid {INFO_COLOR};
            border-radius: {BORDER_RADIUS}px;
        }}
    """
    
    # أنماط أزرار القائمة الجانبية - تصميم عصري
    MENU_BUTTON_STYLE = f"""
        QPushButton {{
            background-color: transparent;
            color: white;
            border: none;
            border-radius: {BORDER_RADIUS}px;
            padding: 16px 24px;
            font-size: {FONT_SIZE_NORMAL}px;
            text-align: left;
            min-height: 44px;
            font-weight: 500;
        }}
        
        QPushButton:hover {{
            background: rgba(255, 255, 255, 0.15);
            color: white;
            font-weight: 600;
        }}
        
        QPushButton:pressed {{
            background: rgba(255, 255, 255, 0.25);
        }}
        
        QPushButton:checked {{
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-weight: bold;
            border-left: 4px solid white;
        }}
    """
    
    # نمط الصفحة الرئيسية
    MAIN_WINDOW_STYLE = f"""
        QMainWindow {{
            background-color: {BACKGROUND_COLOR};
        }}
        
        QWidget#centralWidget {{
            background-color: {BACKGROUND_COLOR};
        }}
        
        QScrollArea {{
            background-color: transparent;
            border: none;
        }}
        
        QScrollBar:vertical {{
            background-color: #F0F0F0;
            width: 12px;
            margin: 0px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: #BDBDBD;
            min-height: 30px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: #9E9E9E;
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}
        
        QScrollBar:horizontal {{
            background-color: #F0F0F0;
            height: 12px;
            margin: 0px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:horizontal {{
            background-color: #BDBDBD;
            min-width: 30px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:horizontal:hover {{
            background-color: #9E9E9E;
        }}
        
        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
            width: 0px;
        }}
    """
    
    # نمط صفحة تسجيل الدخول
    LOGIN_WINDOW_STYLE = f"""
        QWidget {{
            background-color: {BACKGROUND_COLOR};
        }}
        
        QLabel#titleLabel {{
            color: {PRIMARY_COLOR};
            font-size: {FONT_SIZE_XXLARGE}px;
            font-weight: bold;
        }}
        
        QLabel#subtitleLabel {{
            color: {TEXT_SECONDARY};
            font-size: {FONT_SIZE_LARGE}px;
        }}
        
        QFrame#loginFrame {{
            background-color: white;
            border-radius: {BORDER_RADIUS * 2}px;
            padding: 30px;
            border: 1px solid {BORDER_COLOR};
        }}
        
        QLabel#welcomeLabel {{
            color: {PRIMARY_COLOR};
            font-size: {FONT_SIZE_XLARGE}px;
            font-weight: bold;
            margin-bottom: 20px;
        }}
        
        QLabel#loginInfoLabel {{
            color: {TEXT_SECONDARY};
            font-size: {FONT_SIZE_NORMAL}px;
            margin-bottom: 20px;
        }}
        
        QPushButton#loginButton {{
            background-color: {PRIMARY_COLOR};
            color: white;
            border: none;
            border-radius: {BORDER_RADIUS}px;
            padding: 12px;
            font-size: {FONT_SIZE_NORMAL}px;
            font-weight: bold;
            min-height: 40px;
        }}
        
        QPushButton#loginButton:hover {{
            background-color: {HIGHLIGHT_COLOR};
        }}
        
        QPushButton#registerButton {{
            background-color: transparent;
            color: {PRIMARY_COLOR};
            border: none;
            font-size: {FONT_SIZE_NORMAL}px;
            text-decoration: underline;
        }}
        
        QPushButton#registerButton:hover {{
            color: {HIGHLIGHT_COLOR};
        }}
    """
    
    # أنماط علامات التبويب
    TAB_WIDGET_STYLE = f"""
        QTabWidget::pane {{
            border: 1px solid {BORDER_COLOR};
            border-radius: {BORDER_RADIUS}px;
            background-color: white;
        }}
        
        QTabBar::tab {{
            background-color: {BACKGROUND_COLOR};
            color: {TEXT_PRIMARY};
            padding: 12px 20px;
            margin-right: 2px;
            border-top-left-radius: {BORDER_RADIUS}px;
            border-top-right-radius: {BORDER_RADIUS}px;
            font-size: {FONT_SIZE_NORMAL}px;
            font-weight: 500;
        }}
        
        QTabBar::tab:selected {{
            background-color: {PRIMARY_COLOR};
            color: white;
            font-weight: bold;
        }}
        
        QTabBar::tab:hover {{
            background-color: {HIGHLIGHT_COLOR};
            color: white;
        }}
    """