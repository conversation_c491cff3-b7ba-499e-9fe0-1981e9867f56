"""
صفحة المبيعات المحسنة مع نقطة بيع سريعة وإدارة شاملة
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QTableWidget, QTableWidgetItem,
                              QHeaderView, QComboBox, QLineEdit, QSpinBox,
                              QDoubleSpinBox, QTextEdit, QGroupBox, QGridLayout,
                              QScrollArea, QSplitter, QTabWidget, QFormLayout,
                              QMessageBox, QDialog, QDialogButtonBox, QCheckBox,
                              QProgressBar, QFileDialog, QMenu, QApplication,
                              QCalendarWidget, QDateEdit, QListWidget, QListWidgetItem)
from PySide6.QtCore import Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QDate
from PySide6.QtGui import QFont, QColor, QPainter, QPen, QBrush, QPixmap, QAction, QCursor
import json
import random
from datetime import datetime, timedelta

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class QuickSalePOSWidget(QWidget):
    """واجهة نقطة البيع السريعة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.cart_items = []
        self.total_amount = 0.0
        self.init_ui()
        self.load_products()
    
    def init_ui(self):
        """إعداد واجهة نقطة البيع"""
        self.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 8px;
                border: 1px solid #dee2e6;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)
        
        # عنوان نقطة البيع
        title = QLabel("💰 نقطة البيع السريعة")
        title.setStyleSheet("""
            color: #28a745;
            font-size: 16px;
            font-weight: bold;
            padding: 10px 0;
            border-bottom: 2px solid #e8f5e8;
        """)
        layout.addWidget(title)
        
        # البحث عن المنتجات
        search_layout = QHBoxLayout()
        
        self.product_search = QLineEdit()
        self.product_search.setPlaceholderText("ابحث عن منتج أو امسح الباركود...")
        self.product_search.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #28a745;
                border-radius: 20px;
                font-size: 12px;
                background: #f8fff8;
            }
            QLineEdit:focus {
                border-color: #20c997;
                background: white;
            }
        """)
        self.product_search.textChanged.connect(self.search_products)
        
        barcode_btn = QPushButton("📷")
        barcode_btn.setFixedSize(35, 35)
        barcode_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 17px;
                font-size: 14px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        barcode_btn.setToolTip("مسح الباركود")
        barcode_btn.clicked.connect(self.scan_barcode)
        
        search_layout.addWidget(self.product_search)
        search_layout.addWidget(barcode_btn)
        layout.addLayout(search_layout)
        
        # قائمة المنتجات
        self.products_list = QListWidget()
        self.products_list.setMaximumHeight(150)
        self.products_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #e9ecef;
                border-radius: 4px;
                background: #f8f9fa;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }
            QListWidget::item:hover {
                background: #e8f5e8;
            }
            QListWidget::item:selected {
                background: #28a745;
                color: white;
            }
        """)
        self.products_list.itemDoubleClicked.connect(self.add_product_to_cart)
        layout.addWidget(self.products_list)
        
        # سلة المشتريات
        cart_title = QLabel("🛒 سلة المشتريات")
        cart_title.setStyleSheet("""
            color: #495057;
            font-size: 14px;
            font-weight: bold;
            margin-top: 10px;
        """)
        layout.addWidget(cart_title)
        
        self.cart_table = QTableWidget()
        self.cart_table.setColumnCount(5)
        self.cart_table.setHorizontalHeaderLabels(["المنتج", "السعر", "الكمية", "الإجمالي", "حذف"])
        self.cart_table.horizontalHeader().setStretchLastSection(False)
        self.cart_table.setMaximumHeight(200)
        self.cart_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-size: 11px;
            }
            QHeaderView::section {
                background: #28a745;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # تحديد عرض الأعمدة
        header = self.cart_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # السعر
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # الكمية
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # الإجمالي
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # حذف
        
        self.cart_table.setColumnWidth(1, 70)   # السعر
        self.cart_table.setColumnWidth(2, 60)   # الكمية
        self.cart_table.setColumnWidth(3, 80)   # الإجمالي
        self.cart_table.setColumnWidth(4, 50)   # حذف
        
        layout.addWidget(self.cart_table)
        
        # الإجمالي
        total_frame = QFrame()
        total_frame.setStyleSheet("""
            QFrame {
                background: #e8f5e8;
                border-radius: 6px;
                padding: 10px;
                border: 2px solid #28a745;
            }
        """)
        total_layout = QHBoxLayout(total_frame)
        
        total_label = QLabel("الإجمالي:")
        total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #28a745;")
        
        self.total_amount_label = QLabel("0.00 ج.م")
        self.total_amount_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #28a745;")
        
        total_layout.addWidget(total_label)
        total_layout.addStretch()
        total_layout.addWidget(self.total_amount_label)
        
        layout.addWidget(total_frame)
        
        # أزرار العمليات
        buttons_layout = QGridLayout()
        
        # زر إتمام البيع
        complete_btn = QPushButton("✅ إتمام البيع")
        complete_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        complete_btn.clicked.connect(self.complete_sale)
        
        # زر مسح السلة
        clear_btn = QPushButton("🗑️ مسح السلة")
        clear_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        clear_btn.clicked.connect(self.clear_cart)
        
        buttons_layout.addWidget(complete_btn, 0, 0)
        buttons_layout.addWidget(clear_btn, 0, 1)
        
        layout.addLayout(buttons_layout)
    
    def load_products(self):
        """تحميل المنتجات"""
        self.products = [
            {"id": 1, "name": "لابتوب ديل", "price": 15000, "stock": 25},
            {"id": 2, "name": "قميص قطني", "price": 120, "stock": 150},
            {"id": 3, "name": "هاتف ذكي", "price": 8000, "stock": 3},
            {"id": 4, "name": "كتاب برمجة", "price": 250, "stock": 45},
            {"id": 5, "name": "مكواة كهربائية", "price": 300, "stock": 12},
        ]
        self.update_products_list()
    
    def update_products_list(self):
        """تحديث قائمة المنتجات"""
        self.products_list.clear()
        search_text = self.product_search.text().lower()
        
        for product in self.products:
            if not search_text or search_text in product["name"].lower():
                item_text = f"{product['name']} - {product['price']:.2f} ج.م (متوفر: {product['stock']})"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, product)
                self.products_list.addItem(item)
    
    def search_products(self):
        """البحث في المنتجات"""
        self.update_products_list()
    
    def scan_barcode(self):
        """مسح الباركود"""
        QMessageBox.information(self, "مسح الباركود", "ميزة مسح الباركود ستتوفر قريباً!")
    
    def add_product_to_cart(self, item):
        """إضافة منتج إلى السلة"""
        product = item.data(Qt.UserRole)
        
        if product["stock"] <= 0:
            QMessageBox.warning(self, "تحذير", f"المنتج '{product['name']}' غير متوفر!")
            return
        
        # البحث عن المنتج في السلة
        for i, cart_item in enumerate(self.cart_items):
            if cart_item["id"] == product["id"]:
                # زيادة الكمية
                if cart_item["quantity"] < product["stock"]:
                    cart_item["quantity"] += 1
                    self.update_cart_display()
                    return
                else:
                    QMessageBox.warning(self, "تحذير", "الكمية المطلوبة تتجاوز المخزون المتاح!")
                    return
        
        # إضافة منتج جديد للسلة
        cart_item = {
            "id": product["id"],
            "name": product["name"],
            "price": product["price"],
            "quantity": 1,
            "stock": product["stock"]
        }
        self.cart_items.append(cart_item)
        self.update_cart_display()
    
    def update_cart_display(self):
        """تحديث عرض السلة"""
        self.cart_table.setRowCount(len(self.cart_items))
        self.total_amount = 0.0
        
        for row, item in enumerate(self.cart_items):
            # اسم المنتج
            self.cart_table.setItem(row, 0, QTableWidgetItem(item["name"]))
            
            # السعر
            price_item = QTableWidgetItem(f"{item['price']:.2f}")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.cart_table.setItem(row, 1, price_item)
            
            # الكمية
            quantity_spin = QSpinBox()
            quantity_spin.setRange(1, item["stock"])
            quantity_spin.setValue(item["quantity"])
            quantity_spin.valueChanged.connect(lambda value, r=row: self.update_quantity(r, value))
            self.cart_table.setCellWidget(row, 2, quantity_spin)
            
            # الإجمالي
            total_price = item["price"] * item["quantity"]
            total_item = QTableWidgetItem(f"{total_price:.2f}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.cart_table.setItem(row, 3, total_item)
            
            # زر الحذف
            delete_btn = QPushButton("🗑️")
            delete_btn.setFixedSize(30, 25)
            delete_btn.setStyleSheet("""
                QPushButton {
                    background: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 12px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background: #c82333;
                }
            """)
            delete_btn.clicked.connect(lambda checked, r=row: self.remove_from_cart(r))
            self.cart_table.setCellWidget(row, 4, delete_btn)
            
            self.total_amount += total_price
        
        self.total_amount_label.setText(f"{self.total_amount:.2f} ج.م")
    
    def update_quantity(self, row, quantity):
        """تحديث الكمية"""
        if row < len(self.cart_items):
            self.cart_items[row]["quantity"] = quantity
            self.update_cart_display()
    
    def remove_from_cart(self, row):
        """حذف منتج من السلة"""
        if row < len(self.cart_items):
            del self.cart_items[row]
            self.update_cart_display()
    
    def clear_cart(self):
        """مسح السلة"""
        reply = QMessageBox.question(
            self, "تأكيد", "هل تريد مسح جميع المنتجات من السلة؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.cart_items.clear()
            self.update_cart_display()
    
    def complete_sale(self):
        """إتمام البيع"""
        if not self.cart_items:
            QMessageBox.warning(self, "تحذير", "السلة فارغة!")
            return
        
        QMessageBox.information(self, "نجح البيع", "تم إتمام البيع بنجاح!")
        self.cart_items.clear()
        self.update_cart_display()


class SalesTableWidget(QTableWidget):
    """جدول المبيعات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_table()
        self.load_sales_data()
    
    def init_table(self):
        """إعداد الجدول"""
        columns = [
            "رقم الفاتورة", "التاريخ", "العميل", "عدد المنتجات", 
            "الإجمالي", "طريقة الدفع", "الحالة", "الإجراءات"
        ]
        
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 12px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #495057;
                padding: 12px 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        # إعدادات الجدول  
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        self.verticalHeader().setVisible(False)
        
        # تحديد عرض الأعمدة
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)   # رقم الفاتورة
        header.setSectionResizeMode(1, QHeaderView.Fixed)   # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Stretch) # العميل
        header.setSectionResizeMode(3, QHeaderView.Fixed)   # عدد المنتجات
        header.setSectionResizeMode(4, QHeaderView.Fixed)   # الإجمالي
        header.setSectionResizeMode(5, QHeaderView.Fixed)   # طريقة الدفع
        header.setSectionResizeMode(6, QHeaderView.Fixed)   # الحالة
        header.setSectionResizeMode(7, QHeaderView.Fixed)   # الإجراءات
        
        self.setColumnWidth(0, 100)  # رقم الفاتورة
        self.setColumnWidth(1, 100)  # التاريخ
        self.setColumnWidth(3, 90)   # عدد المنتجات
        self.setColumnWidth(4, 100)  # الإجمالي
        self.setColumnWidth(5, 100)  # طريقة الدفع
        self.setColumnWidth(6, 80)   # الحالة
        self.setColumnWidth(7, 120)  # الإجراءات
    
    def load_sales_data(self):
        """تحميل بيانات المبيعات"""
        sales_data = [
            {"invoice": "INV-001", "date": "2024-01-15", "customer": "أحمد محمد", 
             "items": 3, "total": 16370, "payment": "نقدي", "status": "مكتمل"},
            {"invoice": "INV-002", "date": "2024-01-15", "customer": "سارة أحمد", 
             "items": 2, "total": 8120, "payment": "بطاقة", "status": "مكتمل"},
            {"invoice": "INV-003", "date": "2024-01-14", "customer": "محمد علي", 
             "items": 1, "total": 15000, "payment": "تقسيط", "status": "جاري"},
            {"invoice": "INV-004", "date": "2024-01-14", "customer": "فاطمة حسن", 
             "items": 5, "total": 1850, "payment": "نقدي", "status": "مكتمل"},
            {"invoice": "INV-005", "date": "2024-01-13", "customer": "عمر خالد", 
             "items": 2, "total": 550, "payment": "بطاقة", "status": "ملغي"},
        ]
        
        self.setRowCount(len(sales_data))
        
        for row, sale in enumerate(sales_data):
            # رقم الفاتورة
            invoice_item = QTableWidgetItem(sale["invoice"])
            invoice_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 0, invoice_item)
            
            # التاريخ
            date_item = QTableWidgetItem(sale["date"])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 1, date_item)
            
            # العميل
            self.setItem(row, 2, QTableWidgetItem(sale["customer"]))
            
            # عدد المنتجات
            items_item = QTableWidgetItem(f"{sale['items']} منتج")
            items_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 3, items_item)
            
            # الإجمالي
            total_item = QTableWidgetItem(f"{sale['total']:,.2f} ج.م")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 4, total_item)
            
            # طريقة الدفع
            payment_item = QTableWidgetItem(sale["payment"])
            payment_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 5, payment_item)
            
            # الحالة
            status_colors = {
                "مكتمل": {"bg": "#d4edda", "fg": "#155724"},
                "جاري": {"bg": "#fff3cd", "fg": "#856404"},
                "ملغي": {"bg": "#f8d7da", "fg": "#721c24"}
            }
            
            status_item = QTableWidgetItem(sale["status"])
            status_item.setTextAlignment(Qt.AlignCenter)
            color = status_colors.get(sale["status"], status_colors["مكتمل"])
            status_item.setBackground(QColor(color["bg"]))
            status_item.setForeground(QColor(color["fg"]))
            self.setItem(row, 6, status_item)
            
            # الإجراءات
            actions_widget = self.create_actions_widget(row, sale)
            self.setCellWidget(row, 7, actions_widget)
    
    def create_actions_widget(self, row, sale_data):
        """إنشاء أزرار الإجراءات"""
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(5, 2, 5, 2)
        actions_layout.setSpacing(3)
        
        # زر عرض التفاصيل
        view_btn = QPushButton("👁️")
        view_btn.setFixedSize(22, 22)
        view_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        view_btn.setToolTip("عرض التفاصيل")
        view_btn.clicked.connect(lambda: self.view_sale_details(row, sale_data))
        
        # زر طباعة الفاتورة
        print_btn = QPushButton("🖨️")
        print_btn.setFixedSize(22, 22)
        print_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        print_btn.setToolTip("طباعة الفاتورة")
        print_btn.clicked.connect(lambda: self.print_invoice(row, sale_data))
        
        # زر حذف/إلغاء
        delete_btn = QPushButton("🗑️")
        delete_btn.setFixedSize(22, 22)
        delete_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        delete_btn.setToolTip("إلغاء الفاتورة")
        delete_btn.clicked.connect(lambda: self.cancel_sale(row, sale_data))
        
        actions_layout.addWidget(view_btn)
        actions_layout.addWidget(print_btn)
        actions_layout.addWidget(delete_btn)
        actions_layout.addStretch()
        
        return actions_widget
    
    def view_sale_details(self, row, sale_data):
        """عرض تفاصيل البيع"""
        details_text = f"""
        🧾 تفاصيل الفاتورة:
        
        📋 رقم الفاتورة: {sale_data['invoice']}
        📅 التاريخ: {sale_data['date']}
        👤 العميل: {sale_data['customer']}
        📦 عدد المنتجات: {sale_data['items']} منتج
        💰 الإجمالي: {sale_data['total']:,.2f} ج.م
        💳 طريقة الدفع: {sale_data['payment']}
        ✅ الحالة: {sale_data['status']}
        """
        
        QMessageBox.information(self, "تفاصيل البيع", details_text)
    
    def print_invoice(self, row, sale_data):
        """طباعة الفاتورة"""
        QMessageBox.information(self, "طباعة", f"تم إرسال الفاتورة {sale_data['invoice']} للطباعة!")
    
    def cancel_sale(self, row, sale_data):
        """إلغاء البيع"""
        if sale_data['status'] == 'ملغي':
            QMessageBox.information(self, "تنبيه", "هذه الفاتورة ملغية بالفعل!")
            return
        
        reply = QMessageBox.question(
            self, "تأكيد الإلغاء",
            f"هل تريد إلغاء الفاتورة {sale_data['invoice']}؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الإلغاء", "تم إلغاء الفاتورة بنجاح!")
            self.load_sales_data()


class EnhancedSalesWindow(QWidget):
    """نافذة المبيعات المحسنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # التبويبات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background: white;
                border-radius: 8px;
            }
            QTabBar::tab {
                background: #f8f9fa;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                border: 1px solid #dee2e6;
                color: #495057;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background: white;
                color: #007bff;
                border-bottom: 1px solid white;
            }
            QTabBar::tab:hover {
                background: #e9ecef;
            }
        """)
        
        # تبويب نقطة البيع
        pos_tab = QWidget()
        pos_layout = QHBoxLayout(pos_tab)
        pos_layout.setContentsMargins(20, 20, 20, 20)
        
        # نقطة البيع السريعة
        self.pos_widget = QuickSalePOSWidget()
        self.pos_widget.setMaximumWidth(400)
        pos_layout.addWidget(self.pos_widget)
        
        # منطقة الملخص
        summary_widget = self.create_sales_summary()
        pos_layout.addWidget(summary_widget)
        
        self.tabs.addTab(pos_tab, "💰 نقطة البيع")
        
        # تبويب إدارة المبيعات
        sales_management_tab = QWidget()
        sales_layout = QVBoxLayout(sales_management_tab)
        sales_layout.setContentsMargins(20, 20, 20, 20)
        
        # شريط أدوات المبيعات
        sales_toolbar = self.create_sales_toolbar()
        sales_layout.addWidget(sales_toolbar)
        
        # جدول المبيعات
        self.sales_table = SalesTableWidget()
        sales_layout.addWidget(self.sales_table)
        
        self.tabs.addTab(sales_management_tab, "📊 إدارة المبيعات")
        
        main_layout.addWidget(self.tabs)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
    
    def create_header(self):
        """إنشاء الرأس"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #007bff, stop:0.5 #6610f2, stop:1 #007bff);
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # معلومات الصفحة
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        title = QLabel("🛒 إدارة المبيعات")
        title.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
        """)
        
        subtitle = QLabel("نقطة بيع سريعة وإدارة شاملة للمبيعات مع تقارير مفصلة")
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-top: 5px;
        """)
        
        info_layout.addWidget(title)
        info_layout.addWidget(subtitle)
        
        # الإحصائيات السريعة
        stats_widget = self.create_sales_stats()
        
        layout.addWidget(info_widget, 7)
        layout.addWidget(stats_widget, 3)
        
        return header_frame
    
    def create_sales_stats(self):
        """إنشاء إحصائيات المبيعات"""
        stats_widget = QWidget()
        stats_layout = QHBoxLayout(stats_widget)
        stats_layout.setSpacing(10)
        
        # مبيعات اليوم
        today_stat = self.create_stat_card("📈", "42,890", "مبيعات اليوم")
        stats_layout.addWidget(today_stat)
        
        # عدد الفواتير
        invoices_stat = self.create_stat_card("🧾", "127", "فاتورة اليوم")
        stats_layout.addWidget(invoices_stat)
        
        # متوسط البيع
        avg_stat = self.create_stat_card("💡", "337", "متوسط البيع")
        stats_layout.addWidget(avg_stat)
        
        return stats_widget
    
    def create_stat_card(self, icon, value, label):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("color: white; font-size: 20px;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 10px;")
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(label_widget)
        
        return card
    
    def create_sales_summary(self):
        """إنشاء ملخص المبيعات"""
        summary_frame = QFrame()
        summary_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(summary_frame)
        layout.setSpacing(15)
        
        # عنوان الملخص
        title = QLabel("📊 ملخص المبيعات")
        title.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # الإحصائيات التفصيلية
        stats_grid = QGridLayout()
        
        # مبيعات اليوم
        today_sales = self.create_summary_item("💰 مبيعات اليوم", "42,890 ج.م", "#28a745")
        stats_grid.addWidget(today_sales, 0, 0)
        
        # مبيعات الأسبوع
        week_sales = self.create_summary_item("📅 مبيعات الأسبوع", "285,670 ج.م", "#007bff")
        stats_grid.addWidget(week_sales, 0, 1)
        
        # مبيعات الشهر
        month_sales = self.create_summary_item("📊 مبيعات الشهر", "1,234,567 ج.م", "#6f42c1")
        stats_grid.addWidget(month_sales, 1, 0)
        
        # أفضل منتج
        best_product = self.create_summary_item("🏆 أفضل منتج", "لابتوب ديل", "#ffc107")
        stats_grid.addWidget(best_product, 1, 1)
        
        layout.addLayout(stats_grid)
        layout.addStretch()
        
        return summary_frame
    
    def create_summary_item(self, title, value, color):
        """إنشاء عنصر ملخص"""
        item_frame = QFrame()
        item_frame.setStyleSheet(f"""
            QFrame {{
                background: white;
                border-left: 4px solid {color};
                border-radius: 4px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }}
        """)
        
        layout = QVBoxLayout(item_frame)
        layout.setSpacing(5)
        
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            color: {color};
            font-size: 12px;
            font-weight: bold;
        """)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
        """)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        return item_frame
    
    def create_sales_toolbar(self):
        """إنشاء شريط أدوات المبيعات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        
        # فلتر التاريخ
        date_filter = QDateEdit()
        date_filter.setDate(QDate.currentDate())
        date_filter.setCalendarPopup(True)
        date_filter.setStyleSheet("""
            QDateEdit {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
            }
        """)
        
        # فلتر الحالة
        status_filter = QComboBox()
        status_filter.addItems(["جميع الحالات", "مكتمل", "جاري", "ملغي"])
        status_filter.setStyleSheet("""
            QComboBox {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
            }
        """)
        
        # زر تقرير المبيعات
        report_btn = QPushButton("📊 تقرير المبيعات")
        report_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        report_btn.clicked.connect(self.generate_sales_report)
        
        # زر تصدير
        export_btn = QPushButton("📤 تصدير")
        export_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        export_btn.clicked.connect(self.export_sales)
        
        layout.addWidget(QLabel("من تاريخ:"))
        layout.addWidget(date_filter)
        layout.addWidget(QLabel("الحالة:"))
        layout.addWidget(status_filter)
        layout.addWidget(report_btn)
        layout.addWidget(export_btn)
        layout.addStretch()
        
        # إجمالي المبيعات المفلترة
        total_label = QLabel("الإجمالي: 41,890 ج.م")
        total_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 14px;")
        layout.addWidget(total_label)
        
        return toolbar_frame
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFixedHeight(40)
        status_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 8px 15px;
            }
        """)
        
        layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز - نظام المبيعات متاح")
        self.status_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 12px;")
        
        self.sales_count_label = QLabel("5 فواتير")
        self.sales_count_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        
        self.daily_total_label = QLabel("إجمالي اليوم: 41,890 ج.م")
        self.daily_total_label.setStyleSheet("color: #007bff; font-weight: bold; font-size: 12px;")
        
        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.sales_count_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.daily_total_label)
        
        return status_frame
    
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_data)
        self.update_timer.start(30000)  # تحديث كل 30 ثانية
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.status_label.setText(f"تم التحديث - {datetime.now().strftime('%H:%M')}")
    
    def generate_sales_report(self):
        """إنشاء تقرير المبيعات"""
        QMessageBox.information(self, "تقرير المبيعات", "تم إنشاء تقرير المبيعات بنجاح!")
    
    def export_sales(self):
        """تصدير المبيعات"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير المبيعات", "sales.xlsx",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;PDF Files (*.pdf)"
        )
        
        if file_path:
            QMessageBox.information(self, "تصدير", f"تم تصدير المبيعات إلى:\n{file_path}")"""
صفحة المبيعات المحسنة مع نقطة بيع سريعة وإدارة شاملة
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QTableWidget, QTableWidgetItem,
                              QHeaderView, QComboBox, QLineEdit, QSpinBox,
                              QDoubleSpinBox, QTextEdit, QGroupBox, QGridLayout,
                              QScrollArea, QSplitter, QTabWidget, QFormLayout,
                              QMessageBox, QDialog, QDialogButtonBox, QCheckBox,
                              QProgressBar, QFileDialog, QMenu, QApplication,
                              QCalendarWidget, QDateEdit, QListWidget, QListWidgetItem)
from PySide6.QtCore import Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QDate
from PySide6.QtGui import QFont, QColor, QPainter, QPen, QBrush, QPixmap, QAction, QCursor
import json
import random
from datetime import datetime, timedelta

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class QuickSalePOSWidget(QWidget):
    """واجهة نقطة البيع السريعة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.cart_items = []
        self.total_amount = 0.0
        self.init_ui()
        self.load_products()
    
    def init_ui(self):
        """إعداد واجهة نقطة البيع"""
        self.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 8px;
                border: 1px solid #dee2e6;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)
        
        # عنوان نقطة البيع
        title = QLabel("💰 نقطة البيع السريعة")
        title.setStyleSheet("""
            color: #28a745;
            font-size: 16px;
            font-weight: bold;
            padding: 10px 0;
            border-bottom: 2px solid #e8f5e8;
        """)
        layout.addWidget(title)
        
        # البحث عن المنتجات
        search_layout = QHBoxLayout()
        
        self.product_search = QLineEdit()
        self.product_search.setPlaceholderText("ابحث عن منتج أو امسح الباركود...")
        self.product_search.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #28a745;
                border-radius: 20px;
                font-size: 12px;
                background: #f8fff8;
            }
            QLineEdit:focus {
                border-color: #20c997;
                background: white;
            }
        """)
        self.product_search.textChanged.connect(self.search_products)
        
        barcode_btn = QPushButton("📷")
        barcode_btn.setFixedSize(35, 35)
        barcode_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 17px;
                font-size: 14px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        barcode_btn.setToolTip("مسح الباركود")
        barcode_btn.clicked.connect(self.scan_barcode)
        
        search_layout.addWidget(self.product_search)
        search_layout.addWidget(barcode_btn)
        layout.addLayout(search_layout)
        
        # قائمة المنتجات
        self.products_list = QListWidget()
        self.products_list.setMaximumHeight(150)
        self.products_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #e9ecef;
                border-radius: 4px;
                background: #f8f9fa;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }
            QListWidget::item:hover {
                background: #e8f5e8;
            }
            QListWidget::item:selected {
                background: #28a745;
                color: white;
            }
        """)
        self.products_list.itemDoubleClicked.connect(self.add_product_to_cart)
        layout.addWidget(self.products_list)
        
        # سلة المشتريات
        cart_title = QLabel("🛒 سلة المشتريات")
        cart_title.setStyleSheet("""
            color: #495057;
            font-size: 14px;
            font-weight: bold;
            margin-top: 10px;
        """)
        layout.addWidget(cart_title)
        
        self.cart_table = QTableWidget()
        self.cart_table.setColumnCount(5)
        self.cart_table.setHorizontalHeaderLabels(["المنتج", "السعر", "الكمية", "الإجمالي", "حذف"])
        self.cart_table.horizontalHeader().setStretchLastSection(False)
        self.cart_table.setMaximumHeight(200)
        self.cart_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-size: 11px;
            }
            QHeaderView::section {
                background: #28a745;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # تحديد عرض الأعمدة
        header = self.cart_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # السعر
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # الكمية
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # الإجمالي
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # حذف
        
        self.cart_table.setColumnWidth(1, 70)   # السعر
        self.cart_table.setColumnWidth(2, 60)   # الكمية
        self.cart_table.setColumnWidth(3, 80)   # الإجمالي
        self.cart_table.setColumnWidth(4, 50)   # حذف
        
        layout.addWidget(self.cart_table)
        
        # الإجمالي
        total_frame = QFrame()
        total_frame.setStyleSheet("""
            QFrame {
                background: #e8f5e8;
                border-radius: 6px;
                padding: 10px;
                border: 2px solid #28a745;
            }
        """)
        total_layout = QHBoxLayout(total_frame)
        
        total_label = QLabel("الإجمالي:")
        total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #28a745;")
        
        self.total_amount_label = QLabel("0.00 ج.م")
        self.total_amount_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #28a745;")
        
        total_layout.addWidget(total_label)
        total_layout.addStretch()
        total_layout.addWidget(self.total_amount_label)
        
        layout.addWidget(total_frame)
        
        # أزرار العمليات
        buttons_layout = QGridLayout()
        
        # زر إتمام البيع
        complete_btn = QPushButton("✅ إتمام البيع")
        complete_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        complete_btn.clicked.connect(self.complete_sale)
        
        # زر مسح السلة
        clear_btn = QPushButton("🗑️ مسح السلة")
        clear_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        clear_btn.clicked.connect(self.clear_cart)
        
        buttons_layout.addWidget(complete_btn, 0, 0)
        buttons_layout.addWidget(clear_btn, 0, 1)
        
        layout.addLayout(buttons_layout)
    
    def load_products(self):
        """تحميل المنتجات"""
        self.products = [
            {"id": 1, "name": "لابتوب ديل", "price": 15000, "stock": 25},
            {"id": 2, "name": "قميص قطني", "price": 120, "stock": 150},
            {"id": 3, "name": "هاتف ذكي", "price": 8000, "stock": 3},
            {"id": 4, "name": "كتاب برمجة", "price": 250, "stock": 45},
            {"id": 5, "name": "مكواة كهربائية", "price": 300, "stock": 12},
        ]
        self.update_products_list()
    
    def update_products_list(self):
        """تحديث قائمة المنتجات"""
        self.products_list.clear()
        search_text = self.product_search.text().lower()
        
        for product in self.products:
            if not search_text or search_text in product["name"].lower():
                item_text = f"{product['name']} - {product['price']:.2f} ج.م (متوفر: {product['stock']})"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, product)
                self.products_list.addItem(item)
    
    def search_products(self):
        """البحث في المنتجات"""
        self.update_products_list()
    
    def scan_barcode(self):
        """مسح الباركود"""
        QMessageBox.information(self, "مسح الباركود", "ميزة مسح الباركود ستتوفر قريباً!")
    
    def add_product_to_cart(self, item):
        """إضافة منتج إلى السلة"""
        product = item.data(Qt.UserRole)
        
        if product["stock"] <= 0:
            QMessageBox.warning(self, "تحذير", f"المنتج '{product['name']}' غير متوفر!")
            return
        
        # البحث عن المنتج في السلة
        for i, cart_item in enumerate(self.cart_items):
            if cart_item["id"] == product["id"]:
                # زيادة الكمية
                if cart_item["quantity"] < product["stock"]:
                    cart_item["quantity"] += 1
                    self.update_cart_display()
                    return
                else:
                    QMessageBox.warning(self, "تحذير", "الكمية المطلوبة تتجاوز المخزون المتاح!")
                    return
        
        # إضافة منتج جديد للسلة
        cart_item = {
            "id": product["id"],
            "name": product["name"],
            "price": product["price"],
            "quantity": 1,
            "stock": product["stock"]
        }
        self.cart_items.append(cart_item)
        self.update_cart_display()
    
    def update_cart_display(self):
        """تحديث عرض السلة"""
        self.cart_table.setRowCount(len(self.cart_items))
        self.total_amount = 0.0
        
        for row, item in enumerate(self.cart_items):
            # اسم المنتج
            self.cart_table.setItem(row, 0, QTableWidgetItem(item["name"]))
            
            # السعر
            price_item = QTableWidgetItem(f"{item['price']:.2f}")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.cart_table.setItem(row, 1, price_item)
            
            # الكمية
            quantity_spin = QSpinBox()
            quantity_spin.setRange(1, item["stock"])
            quantity_spin.setValue(item["quantity"])
            quantity_spin.valueChanged.connect(lambda value, r=row: self.update_quantity(r, value))
            self.cart_table.setCellWidget(row, 2, quantity_spin)
            
            # الإجمالي
            total_price = item["price"] * item["quantity"]
            total_item = QTableWidgetItem(f"{total_price:.2f}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.cart_table.setItem(row, 3, total_item)
            
            # زر الحذف
            delete_btn = QPushButton("🗑️")
            delete_btn.setFixedSize(30, 25)
            delete_btn.setStyleSheet("""
                QPushButton {
                    background: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 12px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background: #c82333;
                }
            """)
            delete_btn.clicked.connect(lambda checked, r=row: self.remove_from_cart(r))
            self.cart_table.setCellWidget(row, 4, delete_btn)
            
            self.total_amount += total_price
        
        self.total_amount_label.setText(f"{self.total_amount:.2f} ج.م")
    
    def update_quantity(self, row, quantity):
        """تحديث الكمية"""
        if row < len(self.cart_items):
            self.cart_items[row]["quantity"] = quantity
            self.update_cart_display()
    
    def remove_from_cart(self, row):
        """حذف منتج من السلة"""
        if row < len(self.cart_items):
            del self.cart_items[row]
            self.update_cart_display()
    
    def clear_cart(self):
        """مسح السلة"""
        reply = QMessageBox.question(
            self, "تأكيد", "هل تريد مسح جميع المنتجات من السلة؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.cart_items.clear()
            self.update_cart_display()
    
    def complete_sale(self):
        """إتمام البيع"""
        if not self.cart_items:
            QMessageBox.warning(self, "تحذير", "السلة فارغة!")
            return
        
        # نافذة إتمام البيع
        dialog = SaleCompletionDialog(self.cart_items, self.total_amount, self)
        if dialog.exec() == QDialog.Accepted:
            self.cart_items.clear()
            self.update_cart_display()
            QMessageBox.information(self, "نجح البيع", "تم إتمام البيع بنجاح!")


class SaleCompletionDialog(QDialog):
    """نافذة إتمام البيع"""
    
    def __init__(self, cart_items, total_amount, parent=None):
        super().__init__(parent)
        self.cart_items = cart_items
        self.total_amount = total_amount
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة النافذة"""
        self.setWindowTitle("إتمام البيع")
        self.setFixedSize(400, 500)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان النافذة
        title = QLabel("💳 إتمام عملية البيع")
        title.setStyleSheet("""
            color: #28a745;
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # ملخص البيع
        summary_frame = QFrame()
        summary_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        summary_layout = QVBoxLayout(summary_frame)
        
        # عدد المنتجات
        items_count = sum(item["quantity"] for item in self.cart_items)
        count_label = QLabel(f"عدد المنتجات: {items_count} قطعة")
        count_label.setStyleSheet("font-size: 12px; color: #6c757d;")
        
        # الإجمالي
        total_label = QLabel(f"الإجمالي: {self.total_amount:.2f} ج.م")
        total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #28a745;")
        
        summary_layout.addWidget(count_label)
        summary_layout.addWidget(total_label)
        layout.addWidget(summary_frame)
        
        # بيانات العميل
        customer_group = QGroupBox("معلومات العميل")
        customer_layout = QFormLayout(customer_group)
        
        self.customer_name = QLineEdit()
        self.customer_name.setPlaceholderText("اسم العميل (اختياري)")
        customer_layout.addRow("الاسم:", self.customer_name)
        
        self.customer_phone = QLineEdit()
        self.customer_phone.setPlaceholderText("رقم الهاتف (اختياري)")
        customer_layout.addRow("الهاتف:", self.customer_phone)
        
        layout.addWidget(customer_group)
        
        # طريقة الدفع
        payment_group = QGroupBox("طريقة الدفع")
        payment_layout = QVBoxLayout(payment_group)
        
        self.cash_radio = QCheckBox("نقدي")
        self.cash_radio.setChecked(True)
        self.card_radio = QCheckBox("بطاقة ائتمان")
        self.installment_radio = QCheckBox("تقسيط")
        
        payment_layout.addWidget(self.cash_radio)
        payment_layout.addWidget(self.card_radio)
        payment_layout.addWidget(self.installment_radio)
        
        layout.addWidget(payment_group)
        
        # المبلغ المدفوع
        payment_frame = QFrame()
        payment_frame.setStyleSheet("""
            QFrame {
                background: #fff3cd;
                border-radius: 6px;
                padding: 10px;
                border: 1px solid #ffc107;
            }
        """)
        payment_layout_frame = QFormLayout(payment_frame)
        
        self.paid_amount = QDoubleSpinBox()
        self.paid_amount.setRange(0, 999999)
        self.paid_amount.setValue(self.total_amount)
        self.paid_amount.setSuffix(" ج.م")
        self.paid_amount.valueChanged.connect(self.calculate_change)
        
        self.change_label = QLabel("0.00 ج.م")
        self.change_label.setStyleSheet("font-weight: bold; color: #28a745;")
        
        payment_layout_frame.addRow("المبلغ المدفوع:", self.paid_amount)
        payment_layout_frame.addRow("الباقي:", self.change_label)
        
        layout.addWidget(payment_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        complete_btn = QPushButton("✅ إتمام البيع")
        complete_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        complete_btn.clicked.connect(self.accept)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #545b62;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(complete_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def calculate_change(self):
        """حساب الباقي"""
        paid = self.paid_amount.value()
        change = paid - self.total_amount
        self.change_label.setText(f"{change:.2f} ج.م")
        
        if change < 0:
            self.change_label.setStyleSheet("font-weight: bold; color: #dc3545;")
        else:
            self.change_label.setStyleSheet("font-weight: bold; color: #28a745;")


class SalesTableWidget(QTableWidget):
    """جدول المبيعات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_table()
        self.load_sales_data()
    
    def init_table(self):
        """إعداد الجدول"""
        columns = [
            "رقم الفاتورة", "التاريخ", "العميل", "عدد المنتجات", 
            "الإجمالي", "طريقة الدفع", "الحالة", "الإجراءات"
        ]
        
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 12px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #495057;
                padding: 12px 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        # إعدادات الجدول  
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        self.verticalHeader().setVisible(False)
        
        # تحديد عرض الأعمدة
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)   # رقم الفاتورة
        header.setSectionResizeMode(1, QHeaderView.Fixed)   # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Stretch) # العميل
        header.setSectionResizeMode(3, QHeaderView.Fixed)   # عدد المنتجات
        header.setSectionResizeMode(4, QHeaderView.Fixed)   # الإجمالي
        header.setSectionResizeMode(5, QHeaderView.Fixed)   # طريقة الدفع
        header.setSectionResizeMode(6, QHeaderView.Fixed)   # الحالة
        header.setSectionResizeMode(7, QHeaderView.Fixed)   # الإجراءات
        
        self.setColumnWidth(0, 100)  # رقم الفاتورة
        self.setColumnWidth(1, 100)  # التاريخ
        self.setColumnWidth(3, 90)   # عدد المنتجات
        self.setColumnWidth(4, 100)  # الإجمالي
        self.setColumnWidth(5, 100)  # طريقة الدفع
        self.setColumnWidth(6, 80)   # الحالة
        self.setColumnWidth(7, 120)  # الإجراءات
    
    def load_sales_data(self):
        """تحميل بيانات المبيعات"""
        sales_data = [
            {"invoice": "INV-001", "date": "2024-01-15", "customer": "أحمد محمد", 
             "items": 3, "total": 16370, "payment": "نقدي", "status": "مكتمل"},
            {"invoice": "INV-002", "date": "2024-01-15", "customer": "سارة أحمد", 
             "items": 2, "total": 8120, "payment": "بطاقة", "status": "مكتمل"},
            {"invoice": "INV-003", "date": "2024-01-14", "customer": "محمد علي", 
             "items": 1, "total": 15000, "payment": "تقسيط", "status": "جاري"},
            {"invoice": "INV-004", "date": "2024-01-14", "customer": "فاطمة حسن", 
             "items": 5, "total": 1850, "payment": "نقدي", "status": "مكتمل"},
            {"invoice": "INV-005", "date": "2024-01-13", "customer": "عمر خالد", 
             "items": 2, "total": 550, "payment": "بطاقة", "status": "ملغي"},
        ]
        
        self.setRowCount(len(sales_data))
        
        for row, sale in enumerate(sales_data):
            # رقم الفاتورة
            invoice_item = QTableWidgetItem(sale["invoice"])
            invoice_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 0, invoice_item)
            
            # التاريخ
            date_item = QTableWidgetItem(sale["date"])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 1, date_item)
            
            # العميل
            self.setItem(row, 2, QTableWidgetItem(sale["customer"]))
            
            # عدد المنتجات
            items_item = QTableWidgetItem(f"{sale['items']} منتج")
            items_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 3, items_item)
            
            # الإجمالي
            total_item = QTableWidgetItem(f"{sale['total']:,.2f} ج.م")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 4, total_item)
            
            # طريقة الدفع
            payment_item = QTableWidgetItem(sale["payment"])
            payment_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 5, payment_item)
            
            # الحالة
            status_colors = {
                "مكتمل": {"bg": "#d4edda", "fg": "#155724"},
                "جاري": {"bg": "#fff3cd", "fg": "#856404"},
                "ملغي": {"bg": "#f8d7da", "fg": "#721c24"}
            }
            
            status_item = QTableWidgetItem(sale["status"])
            status_item.setTextAlignment(Qt.AlignCenter)
            color = status_colors.get(sale["status"], status_colors["مكتمل"])
            status_item.setBackground(QColor(color["bg"]))
            status_item.setForeground(QColor(color["fg"]))
            self.setItem(row, 6, status_item)
            
            # الإجراءات
            actions_widget = self.create_actions_widget(row, sale)
            self.setCellWidget(row, 7, actions_widget)
    
    def create_actions_widget(self, row, sale_data):
        """إنشاء أزرار الإجراءات"""
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(5, 2, 5, 2)
        actions_layout.setSpacing(3)
        
        # زر عرض التفاصيل
        view_btn = QPushButton("👁️")
        view_btn.setFixedSize(22, 22)
        view_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        view_btn.setToolTip("عرض التفاصيل")
        view_btn.clicked.connect(lambda: self.view_sale_details(row, sale_data))
        
        # زر طباعة الفاتورة
        print_btn = QPushButton("🖨️")
        print_btn.setFixedSize(22, 22)
        print_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        print_btn.setToolTip("طباعة الفاتورة")
        print_btn.clicked.connect(lambda: self.print_invoice(row, sale_data))
        
        # زر حذف/إلغاء
        delete_btn = QPushButton("🗑️")
        delete_btn.setFixedSize(22, 22)
        delete_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        delete_btn.setToolTip("إلغاء الفاتورة")
        delete_btn.clicked.connect(lambda: self.cancel_sale(row, sale_data))
        
        actions_layout.addWidget(view_btn)
        actions_layout.addWidget(print_btn)
        actions_layout.addWidget(delete_btn)
        actions_layout.addStretch()
        
        return actions_widget
    
    def view_sale_details(self, row, sale_data):
        """عرض تفاصيل البيع"""
        details_text = f"""
        🧾 تفاصيل الفاتورة:
        
        📋 رقم الفاتورة: {sale_data['invoice']}
        📅 التاريخ: {sale_data['date']}
        👤 العميل: {sale_data['customer']}
        📦 عدد المنتجات: {sale_data['items']} منتج
        💰 الإجمالي: {sale_data['total']:,.2f} ج.م
        💳 طريقة الدفع: {sale_data['payment']}
        ✅ الحالة: {sale_data['status']}
        """
        
        QMessageBox.information(self, "تفاصيل البيع", details_text)
    
    def print_invoice(self, row, sale_data):
        """طباعة الفاتورة"""
        QMessageBox.information(self, "طباعة", f"تم إرسال الفاتورة {sale_data['invoice']} للطباعة!")
    
    def cancel_sale(self, row, sale_data):
        """إلغاء البيع"""
        if sale_data['status'] == 'ملغي':
            QMessageBox.information(self, "تنبيه", "هذه الفاتورة ملغية بالفعل!")
            return
        
        reply = QMessageBox.question(
            self, "تأكيد الإلغاء",
            f"هل تريد إلغاء الفاتورة {sale_data['invoice']}؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الإلغاء", "تم إلغاء الفاتورة بنجاح!")
            self.load_sales_data()


class EnhancedSalesWindow(QWidget):
    """نافذة المبيعات المحسنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # التبويبات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background: white;
                border-radius: 8px;
            }
            QTabBar::tab {
                background: #f8f9fa;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                border: 1px solid #dee2e6;
                color: #495057;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background: white;
                color: #007bff;
                border-bottom: 1px solid white;
            }
            QTabBar::tab:hover {
                background: #e9ecef;
            }
        """)
        
        # تبويب نقطة البيع
        pos_tab = QWidget()
        pos_layout = QHBoxLayout(pos_tab)
        pos_layout.setContentsMargins(20, 20, 20, 20)
        
        # نقطة البيع السريعة
        self.pos_widget = QuickSalePOSWidget()
        self.pos_widget.setMaximumWidth(400)
        pos_layout.addWidget(self.pos_widget)
        
        # منطقة الملخص والإحصائيات
        summary_widget = self.create_sales_summary()
        pos_layout.addWidget(summary_widget)
        
        self.tabs.addTab(pos_tab, "💰 نقطة البيع")
        
        # تبويب إدارة المبيعات
        sales_management_tab = QWidget()
        sales_layout = QVBoxLayout(sales_management_tab)
        sales_layout.setContentsMargins(20, 20, 20, 20)
        
        # شريط أدوات المبيعات
        sales_toolbar = self.create_sales_toolbar()
        sales_layout.addWidget(sales_toolbar)
        
        # جدول المبيعات
        self.sales_table = SalesTableWidget()
        sales_layout.addWidget(self.sales_table)
        
        self.tabs.addTab(sales_management_tab, "📊 إدارة المبيعات")
        
        main_layout.addWidget(self.tabs)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
    
    def create_header(self):
        """إنشاء الرأس"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #007bff, stop:0.5 #6610f2, stop:1 #007bff);
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # معلومات الصفحة
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        title = QLabel("🛒 إدارة المبيعات")
        title.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
        """)
        
        subtitle = QLabel("نقطة بيع سريعة وإدارة شاملة للمبيعات مع تقارير مفصلة")
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-top: 5px;
        """)
        
        info_layout.addWidget(title)
        info_layout.addWidget(subtitle)
        
        # الإحصائيات السريعة
        stats_widget = self.create_sales_stats()
        
        layout.addWidget(info_widget, 7)
        layout.addWidget(stats_widget, 3)
        
        return header_frame
    
    def create_sales_stats(self):
        """إنشاء إحصائيات المبيعات"""
        stats_widget = QWidget()
        stats_layout = QHBoxLayout(stats_widget)
        stats_layout.setSpacing(10)
        
        # مبيعات اليوم
        today_stat = self.create_stat_card("📈", "42,890", "مبيعات اليوم")
        stats_layout.addWidget(today_stat)
        
        # عدد الفواتير
        invoices_stat = self.create_stat_card("🧾", "127", "فاتورة اليوم")
        stats_layout.addWidget(invoices_stat)
        
        # متوسط البيع
        avg_stat = self.create_stat_card("💡", "337", "متوسط البيع")
        stats_layout.addWidget(avg_stat)
        
        return stats_widget
    
    def create_stat_card(self, icon, value, label):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("color: white; font-size: 20px;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 10px;")
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(label_widget)
        
        return card
    
    def create_sales_summary(self):
        """إنشاء ملخص المبيعات"""
        summary_frame = QFrame()
        summary_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(summary_frame)
        layout.setSpacing(15)
        
        # عنوان الملخص
        title = QLabel("📊 ملخص المبيعات")
        title.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # الإحصائيات التفصيلية
        stats_grid = QGridLayout()
        
        # مبيعات اليوم
        today_sales = self.create_summary_item("💰 مبيعات اليوم", "42,890 ج.م", "#28a745")
        stats_grid.addWidget(today_sales, 0, 0)
        
        # مبيعات الأسبوع
        week_sales = self.create_summary_item("📅 مبيعات الأسبوع", "285,670 ج.م", "#007bff")
        stats_grid.addWidget(week_sales, 0, 1)
        
        # مبيعات الشهر
        month_sales = self.create_summary_item("📊 مبيعات الشهر", "1,234,567 ج.م", "#6f42c1")
        stats_grid.addWidget(month_sales, 1, 0)
        
        # أفضل منتج
        best_product = self.create_summary_item("🏆 أفضل منتج", "لابتوب ديل", "#ffc107")
        stats_grid.addWidget(best_product, 1, 1)
        
        layout.addLayout(stats_grid)
        
        # الرسم البياني (محاكاة)
        chart_placeholder = QFrame()
        chart_placeholder.setFixedHeight(200)
        chart_placeholder.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                border: 2px dashed #dee2e6;
            }
        """)
        
        chart_layout = QVBoxLayout(chart_placeholder)
        chart_label = QLabel("📈 رسم بياني للمبيعات\n(سيتم إضافته قريباً)")
        chart_label.setAlignment(Qt.AlignCenter)
        chart_label.setStyleSheet("color: #6c757d; font-size: 14px;")
        chart_layout.addWidget(chart_label)
        
        layout.addWidget(chart_placeholder)
        
        return summary_frame
    
    def create_summary_item(self, title, value, color):
        """إنشاء عنصر ملخص"""
        item_frame = QFrame()
        item_frame.setStyleSheet(f"""
            QFrame {{
                background: white;
                border-left: 4px solid {color};
                border-radius: 4px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }}
        """)
        
        layout = QVBoxLayout(item_frame)
        layout.setSpacing(5)
        
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            color: {color};
            font-size: 12px;
            font-weight: bold;
        """)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
        """)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        return item_frame
    
    def create_sales_toolbar(self):
        """إنشاء شريط أدوات المبيعات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        
        # فلتر التاريخ
        date_filter = QDateEdit()
        date_filter.setDate(QDate.currentDate())
        date_filter.setCalendarPopup(True)
        date_filter.setStyleSheet("""
            QDateEdit {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
            }
        """)
        
        # فلتر الحالة
        status_filter = QComboBox()
        status_filter.addItems(["جميع الحالات", "مكتمل", "جاري", "ملغي"])
        status_filter.setStyleSheet("""
            QComboBox {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
            }
        """)
        
        # زر تقرير المبيعات
        report_btn = QPushButton("📊 تقرير المبيعات")
        report_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        report_btn.clicked.connect(self.generate_sales_report)
        
        # زر تصدير
        export_btn = QPushButton("📤 تصدير")
        export_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        export_btn.clicked.connect(self.export_sales)
        
        layout.addWidget(QLabel("من تاريخ:"))
        layout.addWidget(date_filter)
        layout.addWidget(QLabel("الحالة:"))
        layout.addWidget(status_filter)
        layout.addWidget(report_btn)
        layout.addWidget(export_btn)
        layout.addStretch()
        
        # إجمالي المبيعات المفلترة
        total_label = QLabel("الإجمالي: 41,890 ج.م")
        total_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 14px;")
        layout.addWidget(total_label)
        
        return toolbar_frame
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFixedHeight(40)
        status_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 8px 15px;
            }
        """)
        
        layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز - نظام المبيعات متاح")
        self.status_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 12px;")
        
        self.sales_count_label = QLabel("5 فواتير")
        self.sales_count_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        
        self.daily_total_label = QLabel("إجمالي اليوم: 41,890 ج.م")
        self.daily_total_label.setStyleSheet("color: #007bff; font-weight: bold; font-size: 12px;")
        
        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.sales_count_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.daily_total_label)
        
        return status_frame
    
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_data)
        self.update_timer.start(30000)  # تحديث كل 30 ثانية
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.status_label.setText(f"تم التحديث - {datetime.now().strftime('%H:%M')}")
    
    def generate_sales_report(self):
        """إنشاء تقرير المبيعات"""
        QMessageBox.information(self, "تقرير المبيعات", "تم إنشاء تقرير المبيعات بنجاح!")
    
    def export_sales(self):
        """تصدير المبيعات"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير المبيعات", "sales.xlsx",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;PDF Files (*.pdf)"
        )
        
        if file_path:
            QMessageBox.information(self, "تصدير", f"تم تصدير المبيعات إلى:\n{file_path}")"""
صفحة المبيعات المحسنة مع نقطة بيع سريعة وإدارة شاملة
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QTableWidget, QTableWidgetItem,
                              QHeaderView, QComboBox, QLineEdit, QSpinBox,
                              QDoubleSpinBox, QTextEdit, QGroupBox, QGridLayout,
                              QScrollArea, QSplitter, QTabWidget, QFormLayout,
                              QMessageBox, QDialog, QDialogButtonBox, QCheckBox,
                              QProgressBar, QFileDialog, QMenu, QApplication,
                              QCalendarWidget, QDateEdit, QListWidget, QListWidgetItem)
from PySide6.QtCore import Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QDate
from PySide6.QtGui import QFont, QColor, QPainter, QPen, QBrush, QPixmap, QAction, QCursor
import json
import random
from datetime import datetime, timedelta

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class QuickSalePOSWidget(QWidget):
    """واجهة نقطة البيع السريعة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.cart_items = []
        self.total_amount = 0.0
        self.init_ui()
        self.load_products()
    
    def init_ui(self):
        """إعداد واجهة نقطة البيع"""
        self.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 8px;
                border: 1px solid #dee2e6;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)
        
        # عنوان نقطة البيع
        title = QLabel("💰 نقطة البيع السريعة")
        title.setStyleSheet("""
            color: #28a745;
            font-size: 16px;
            font-weight: bold;
            padding: 10px 0;
            border-bottom: 2px solid #e8f5e8;
        """)
        layout.addWidget(title)
        
        # البحث عن المنتجات
        search_layout = QHBoxLayout()
        
        self.product_search = QLineEdit()
        self.product_search.setPlaceholderText("ابحث عن منتج أو امسح الباركود...")
        self.product_search.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #28a745;
                border-radius: 20px;
                font-size: 12px;
                background: #f8fff8;
            }
            QLineEdit:focus {
                border-color: #20c997;
                background: white;
            }
        """)
        self.product_search.textChanged.connect(self.search_products)
        
        barcode_btn = QPushButton("📷")
        barcode_btn.setFixedSize(35, 35)
        barcode_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 17px;
                font-size: 14px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        barcode_btn.setToolTip("مسح الباركود")
        barcode_btn.clicked.connect(self.scan_barcode)
        
        search_layout.addWidget(self.product_search)
        search_layout.addWidget(barcode_btn)
        layout.addLayout(search_layout)
        
        # قائمة المنتجات
        self.products_list = QListWidget()
        self.products_list.setMaximumHeight(150)
        self.products_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #e9ecef;
                border-radius: 4px;
                background: #f8f9fa;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }
            QListWidget::item:hover {
                background: #e8f5e8;
            }
            QListWidget::item:selected {
                background: #28a745;
                color: white;
            }
        """)
        self.products_list.itemDoubleClicked.connect(self.add_product_to_cart)
        layout.addWidget(self.products_list)
        
        # سلة المشتريات
        cart_title = QLabel("🛒 سلة المشتريات")
        cart_title.setStyleSheet("""
            color: #495057;
            font-size: 14px;
            font-weight: bold;
            margin-top: 10px;
        """)
        layout.addWidget(cart_title)
        
        self.cart_table = QTableWidget()
        self.cart_table.setColumnCount(5)
        self.cart_table.setHorizontalHeaderLabels(["المنتج", "السعر", "الكمية", "الإجمالي", "حذف"])
        self.cart_table.horizontalHeader().setStretchLastSection(False)
        self.cart_table.setMaximumHeight(200)
        self.cart_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-size: 11px;
            }
            QHeaderView::section {
                background: #28a745;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # تحديد عرض الأعمدة
        header = self.cart_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # السعر
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # الكمية
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # الإجمالي
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # حذف
        
        self.cart_table.setColumnWidth(1, 70)   # السعر
        self.cart_table.setColumnWidth(2, 60)   # الكمية
        self.cart_table.setColumnWidth(3, 80)   # الإجمالي
        self.cart_table.setColumnWidth(4, 50)   # حذف
        
        layout.addWidget(self.cart_table)
        
        # الإجمالي
        total_frame = QFrame()
        total_frame.setStyleSheet("""
            QFrame {
                background: #e8f5e8;
                border-radius: 6px;
                padding: 10px;
                border: 2px solid #28a745;
            }
        """)
        total_layout = QHBoxLayout(total_frame)
        
        total_label = QLabel("الإجمالي:")
        total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #28a745;")
        
        self.total_amount_label = QLabel("0.00 ج.م")
        self.total_amount_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #28a745;")
        
        total_layout.addWidget(total_label)
        total_layout.addStretch()
        total_layout.addWidget(self.total_amount_label)
        
        layout.addWidget(total_frame)
        
        # أزرار العمليات
        buttons_layout = QGridLayout()
        
        # زر إتمام البيع
        complete_btn = QPushButton("✅ إتمام البيع")
        complete_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        complete_btn.clicked.connect(self.complete_sale)
        
        # زر مسح السلة
        clear_btn = QPushButton("🗑️ مسح السلة")
        clear_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        clear_btn.clicked.connect(self.clear_cart)
        
        buttons_layout.addWidget(complete_btn, 0, 0)
        buttons_layout.addWidget(clear_btn, 0, 1)
        
        layout.addLayout(buttons_layout)
    
    def load_products(self):
        """تحميل المنتجات"""
        self.products = [
            {"id": 1, "name": "لابتوب ديل", "price": 15000, "stock": 25},
            {"id": 2, "name": "قميص قطني", "price": 120, "stock": 150},
            {"id": 3, "name": "هاتف ذكي", "price": 8000, "stock": 3},
            {"id": 4, "name": "كتاب برمجة", "price": 250, "stock": 45},
            {"id": 5, "name": "مكواة كهربائية", "price": 300, "stock": 12},
        ]
        self.update_products_list()
    
    def update_products_list(self):
        """تحديث قائمة المنتجات"""
        self.products_list.clear()
        search_text = self.product_search.text().lower()
        
        for product in self.products:
            if not search_text or search_text in product["name"].lower():
                item_text = f"{product['name']} - {product['price']:.2f} ج.م (متوفر: {product['stock']})"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, product)
                self.products_list.addItem(item)
    
    def search_products(self):
        """البحث في المنتجات"""
        self.update_products_list()
    
    def scan_barcode(self):
        """مسح الباركود"""
        QMessageBox.information(self, "مسح الباركود", "ميزة مسح الباركود ستتوفر قريباً!")
    
    def add_product_to_cart(self, item):
        """إضافة منتج إلى السلة"""
        product = item.data(Qt.UserRole)
        
        if product["stock"] <= 0:
            QMessageBox.warning(self, "تحذير", f"المنتج '{product['name']}' غير متوفر!")
            return
        
        # البحث عن المنتج في السلة
        for i, cart_item in enumerate(self.cart_items):
            if cart_item["id"] == product["id"]:
                # زيادة الكمية
                if cart_item["quantity"] < product["stock"]:
                    cart_item["quantity"] += 1
                    self.update_cart_display()
                    return
                else:
                    QMessageBox.warning(self, "تحذير", "الكمية المطلوبة تتجاوز المخزون المتاح!")
                    return
        
        # إضافة منتج جديد للسلة
        cart_item = {
            "id": product["id"],
            "name": product["name"],
            "price": product["price"],
            "quantity": 1,
            "stock": product["stock"]
        }
        self.cart_items.append(cart_item)
        self.update_cart_display()
    
    def update_cart_display(self):
        """تحديث عرض السلة"""
        self.cart_table.setRowCount(len(self.cart_items))
        self.total_amount = 0.0
        
        for row, item in enumerate(self.cart_items):
            # اسم المنتج
            self.cart_table.setItem(row, 0, QTableWidgetItem(item["name"]))
            
            # السعر
            price_item = QTableWidgetItem(f"{item['price']:.2f}")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.cart_table.setItem(row, 1, price_item)
            
            # الكمية
            quantity_spin = QSpinBox()
            quantity_spin.setRange(1, item["stock"])
            quantity_spin.setValue(item["quantity"])
            quantity_spin.valueChanged.connect(lambda value, r=row: self.update_quantity(r, value))
            self.cart_table.setCellWidget(row, 2, quantity_spin)
            
            # الإجمالي
            total_price = item["price"] * item["quantity"]
            total_item = QTableWidgetItem(f"{total_price:.2f}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.cart_table.setItem(row, 3, total_item)
            
            # زر الحذف
            delete_btn = QPushButton("🗑️")
            delete_btn.setFixedSize(30, 25)
            delete_btn.setStyleSheet("""
                QPushButton {
                    background: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 12px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background: #c82333;
                }
            """)
            delete_btn.clicked.connect(lambda checked, r=row: self.remove_from_cart(r))
            self.cart_table.setCellWidget(row, 4, delete_btn)
            
            self.total_amount += total_price
        
        self.total_amount_label.setText(f"{self.total_amount:.2f} ج.م")
    
    def update_quantity(self, row, quantity):
        """تحديث الكمية"""
        if row < len(self.cart_items):
            self.cart_items[row]["quantity"] = quantity
            self.update_cart_display()
    
    def remove_from_cart(self, row):
        """حذف منتج من السلة"""
        if row < len(self.cart_items):
            del self.cart_items[row]
            self.update_cart_display()
    
    def clear_cart(self):
        """مسح السلة"""
        reply = QMessageBox.question(
            self, "تأكيد", "هل تريد مسح جميع المنتجات من السلة؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.cart_items.clear()
            self.update_cart_display()
    
    def complete_sale(self):
        """إتمام البيع"""
        if not self.cart_items:
            QMessageBox.warning(self, "تحذير", "السلة فارغة!")
            return
        
        # نافذة إتمام البيع
        dialog = SaleCompletionDialog(self.cart_items, self.total_amount, self)
        if dialog.exec() == QDialog.Accepted:
            self.cart_items.clear()
            self.update_cart_display()
            QMessageBox.information(self, "نجح البيع", "تم إتمام البيع بنجاح!")


class SaleCompletionDialog(QDialog):
    """نافذة إتمام البيع"""
    
    def __init__(self, cart_items, total_amount, parent=None):
        super().__init__(parent)
        self.cart_items = cart_items
        self.total_amount = total_amount
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة النافذة"""
        self.setWindowTitle("إتمام البيع")
        self.setFixedSize(400, 500)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان النافذة
        title = QLabel("💳 إتمام عملية البيع")
        title.setStyleSheet("""
            color: #28a745;
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # ملخص البيع
        summary_frame = QFrame()
        summary_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        summary_layout = QVBoxLayout(summary_frame)
        
        # عدد المنتجات
        items_count = sum(item["quantity"] for item in self.cart_items)
        count_label = QLabel(f"عدد المنتجات: {items_count} قطعة")
        count_label.setStyleSheet("font-size: 12px; color: #6c757d;")
        
        # الإجمالي
        total_label = QLabel(f"الإجمالي: {self.total_amount:.2f} ج.م")
        total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #28a745;")
        
        summary_layout.addWidget(count_label)
        summary_layout.addWidget(total_label)
        layout.addWidget(summary_frame)
        
        # بيانات العميل
        customer_group = QGroupBox("معلومات العميل")
        customer_layout = QFormLayout(customer_group)
        
        self.customer_name = QLineEdit()
        self.customer_name.setPlaceholderText("اسم العميل (اختياري)")
        customer_layout.addRow("الاسم:", self.customer_name)
        
        self.customer_phone = QLineEdit()
        self.customer_phone.setPlaceholderText("رقم الهاتف (اختياري)")
        customer_layout.addRow("الهاتف:", self.customer_phone)
        
        layout.addWidget(customer_group)
        
        # طريقة الدفع
        payment_group = QGroupBox("طريقة الدفع")
        payment_layout = QVBoxLayout(payment_group)
        
        self.cash_radio = QCheckBox("نقدي")
        self.cash_radio.setChecked(True)
        self.card_radio = QCheckBox("بطاقة ائتمان")
        self.installment_radio = QCheckBox("تقسيط")
        
        payment_layout.addWidget(self.cash_radio)
        payment_layout.addWidget(self.card_radio)
        payment_layout.addWidget(self.installment_radio)
        
        layout.addWidget(payment_group)
        
        # المبلغ المدفوع
        payment_frame = QFrame()
        payment_frame.setStyleSheet("""
            QFrame {
                background: #fff3cd;
                border-radius: 6px;
                padding: 10px;
                border: 1px solid #ffc107;
            }
        """)
        payment_layout_frame = QFormLayout(payment_frame)
        
        self.paid_amount = QDoubleSpinBox()
        self.paid_amount.setRange(0, 999999)
        self.paid_amount.setValue(self.total_amount)
        self.paid_amount.setSuffix(" ج.م")
        self.paid_amount.valueChanged.connect(self.calculate_change)
        
        self.change_label = QLabel("0.00 ج.م")
        self.change_label.setStyleSheet("font-weight: bold; color: #28a745;")
        
        payment_layout_frame.addRow("المبلغ المدفوع:", self.paid_amount)
        payment_layout_frame.addRow("الباقي:", self.change_label)
        
        layout.addWidget(payment_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        complete_btn = QPushButton("✅ إتمام البيع")
        complete_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        complete_btn.clicked.connect(self.accept)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #545b62;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(complete_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def calculate_change(self):
        """حساب الباقي"""
        paid = self.paid_amount.value()
        change = paid - self.total_amount
        self.change_label.setText(f"{change:.2f} ج.م")
        
        if change < 0:
            self.change_label.setStyleSheet("font-weight: bold; color: #dc3545;")
        else:
            self.change_label.setStyleSheet("font-weight: bold; color: #28a745;")


class SalesTableWidget(QTableWidget):
    """جدول المبيعات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_table()
        self.load_sales_data()
    
    def init_table(self):
        """إعداد الجدول"""
        columns = [
            "رقم الفاتورة", "التاريخ", "العميل", "عدد المنتجات", 
            "الإجمالي", "طريقة الدفع", "الحالة", "الإجراءات"
        ]
        
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 12px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #495057;
                padding: 12px 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        # إعدادات الجدول  
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        self.verticalHeader().setVisible(False)
        
        # تحديد عرض الأعمدة
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)   # رقم الفاتورة
        header.setSectionResizeMode(1, QHeaderView.Fixed)   # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Stretch) # العميل
        header.setSectionResizeMode(3, QHeaderView.Fixed)   # عدد المنتجات
        header.setSectionResizeMode(4, QHeaderView.Fixed)   # الإجمالي
        header.setSectionResizeMode(5, QHeaderView.Fixed)   # طريقة الدفع
        header.setSectionResizeMode(6, QHeaderView.Fixed)   # الحالة
        header.setSectionResizeMode(7, QHeaderView.Fixed)   # الإجراءات
        
        self.setColumnWidth(0, 100)  # رقم الفاتورة
        self.setColumnWidth(1, 100)  # التاريخ
        self.setColumnWidth(3, 90)   # عدد المنتجات
        self.setColumnWidth(4, 100)  # الإجمالي
        self.setColumnWidth(5, 100)  # طريقة الدفع
        self.setColumnWidth(6, 80)   # الحالة
        self.setColumnWidth(7, 120)  # الإجراءات
    
    def load_sales_data(self):
        """تحميل بيانات المبيعات"""
        sales_data = [
            {"invoice": "INV-001", "date": "2024-01-15", "customer": "أحمد محمد", 
             "items": 3, "total": 16370, "payment": "نقدي", "status": "مكتمل"},
            {"invoice": "INV-002", "date": "2024-01-15", "customer": "سارة أحمد", 
             "items": 2, "total": 8120, "payment": "بطاقة", "status": "مكتمل"},
            {"invoice": "INV-003", "date": "2024-01-14", "customer": "محمد علي", 
             "items": 1, "total": 15000, "payment": "تقسيط", "status": "جاري"},
            {"invoice": "INV-004", "date": "2024-01-14", "customer": "فاطمة حسن", 
             "items": 5, "total": 1850, "payment": "نقدي", "status": "مكتمل"},
            {"invoice": "INV-005", "date": "2024-01-13", "customer": "عمر خالد", 
             "items": 2, "total": 550, "payment": "بطاقة", "status": "ملغي"},
        ]
        
        self.setRowCount(len(sales_data))
        
        for row, sale in enumerate(sales_data):
            # رقم الفاتورة
            invoice_item = QTableWidgetItem(sale["invoice"])
            invoice_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 0, invoice_item)
            
            # التاريخ
            date_item = QTableWidgetItem(sale["date"])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 1, date_item)
            
            # العميل
            self.setItem(row, 2, QTableWidgetItem(sale["customer"]))
            
            # عدد المنتجات
            items_item = QTableWidgetItem(f"{sale['items']} منتج")
            items_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 3, items_item)
            
            # الإجمالي
            total_item = QTableWidgetItem(f"{sale['total']:,.2f} ج.م")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 4, total_item)
            
            # طريقة الدفع
            payment_item = QTableWidgetItem(sale["payment"])
            payment_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 5, payment_item)
            
            # الحالة
            status_colors = {
                "مكتمل": {"bg": "#d4edda", "fg": "#155724"},
                "جاري": {"bg": "#fff3cd", "fg": "#856404"},
                "ملغي": {"bg": "#f8d7da", "fg": "#721c24"}
            }
            
            status_item = QTableWidgetItem(sale["status"])
            status_item.setTextAlignment(Qt.AlignCenter)
            color = status_colors.get(sale["status"], status_colors["مكتمل"])
            status_item.setBackground(QColor(color["bg"]))
            status_item.setForeground(QColor(color["fg"]))
            self.setItem(row, 6, status_item)
            
            # الإجراءات
            actions_widget = self.create_actions_widget(row, sale)
            self.setCellWidget(row, 7, actions_widget)
    
    def create_actions_widget(self, row, sale_data):
        """إنشاء أزرار الإجراءات"""
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(5, 2, 5, 2)
        actions_layout.setSpacing(3)
        
        # زر عرض التفاصيل
        view_btn = QPushButton("👁️")
        view_btn.setFixedSize(22, 22)
        view_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        view_btn.setToolTip("عرض التفاصيل")
        view_btn.clicked.connect(lambda: self.view_sale_details(row, sale_data))
        
        # زر طباعة الفاتورة
        print_btn = QPushButton("🖨️")
        print_btn.setFixedSize(22, 22)
        print_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        print_btn.setToolTip("طباعة الفاتورة")
        print_btn.clicked.connect(lambda: self.print_invoice(row, sale_data))
        
        # زر حذف/إلغاء
        delete_btn = QPushButton("🗑️")
        delete_btn.setFixedSize(22, 22)
        delete_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        delete_btn.setToolTip("إلغاء الفاتورة")
        delete_btn.clicked.connect(lambda: self.cancel_sale(row, sale_data))
        
        actions_layout.addWidget(view_btn)
        actions_layout.addWidget(print_btn)
        actions_layout.addWidget(delete_btn)
        actions_layout.addStretch()
        
        return actions_widget
    
    def view_sale_details(self, row, sale_data):
        """عرض تفاصيل البيع"""
        details_text = f"""
        🧾 تفاصيل الفاتورة:
        
        📋 رقم الفاتورة: {sale_data['invoice']}
        📅 التاريخ: {sale_data['date']}
        👤 العميل: {sale_data['customer']}
        📦 عدد المنتجات: {sale_data['items']} منتج
        💰 الإجمالي: {sale_data['total']:,.2f} ج.م
        💳 طريقة الدفع: {sale_data['payment']}
        ✅ الحالة: {sale_data['status']}
        """
        
        QMessageBox.information(self, "تفاصيل البيع", details_text)
    
    def print_invoice(self, row, sale_data):
        """طباعة الفاتورة"""
        QMessageBox.information(self, "طباعة", f"تم إرسال الفاتورة {sale_data['invoice']} للطباعة!")
    
    def cancel_sale(self, row, sale_data):
        """إلغاء البيع"""
        if sale_data['status'] == 'ملغي':
            QMessageBox.information(self, "تنبيه", "هذه الفاتورة ملغية بالفعل!")
            return
        
        reply = QMessageBox.question(
            self, "تأكيد الإلغاء",
            f"هل تريد إلغاء الفاتورة {sale_data['invoice']}؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الإلغاء", "تم إلغاء الفاتورة بنجاح!")
            self.load_sales_data()


class EnhancedSalesWindow(QWidget):
    """نافذة المبيعات المحسنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # التبويبات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background: white;
                border-radius: 8px;
            }
            QTabBar::tab {
                background: #f8f9fa;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                border: 1px solid #dee2e6;
                color: #495057;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background: white;
                color: #007bff;
                border-bottom: 1px solid white;
            }
            QTabBar::tab:hover {
                background: #e9ecef;
            }
        """)
        
        # تبويب نقطة البيع
        pos_tab = QWidget()
        pos_layout = QHBoxLayout(pos_tab)
        pos_layout.setContentsMargins(20, 20, 20, 20)
        
        # نقطة البيع السريعة
        self.pos_widget = QuickSalePOSWidget()
        self.pos_widget.setMaximumWidth(400)
        pos_layout.addWidget(self.pos_widget)
        
        # منطقة الملخص والإحصائيات
        summary_widget = self.create_sales_summary()
        pos_layout.addWidget(summary_widget)
        
        self.tabs.addTab(pos_tab, "💰 نقطة البيع")
        
        # تبويب إدارة المبيعات
        sales_management_tab = QWidget()
        sales_layout = QVBoxLayout(sales_management_tab)
        sales_layout.setContentsMargins(20, 20, 20, 20)
        
        # شريط أدوات المبيعات
        sales_toolbar = self.create_sales_toolbar()
        sales_layout.addWidget(sales_toolbar)
        
        # جدول المبيعات
        self.sales_table = SalesTableWidget()
        sales_layout.addWidget(self.sales_table)
        
        self.tabs.addTab(sales_management_tab, "📊 إدارة المبيعات")
        
        main_layout.addWidget(self.tabs)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
    
    def create_header(self):
        """إنشاء الرأس"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #007bff, stop:0.5 #6610f2, stop:1 #007bff);
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # معلومات الصفحة
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        title = QLabel("🛒 إدارة المبيعات")
        title.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
        """)
        
        subtitle = QLabel("نقطة بيع سريعة وإدارة شاملة للمبيعات مع تقارير مفصلة")
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-top: 5px;
        """)
        
        info_layout.addWidget(title)
        info_layout.addWidget(subtitle)
        
        # الإحصائيات السريعة
        stats_widget = self.create_sales_stats()
        
        layout.addWidget(info_widget, 7)
        layout.addWidget(stats_widget, 3)
        
        return header_frame
    
    def create_sales_stats(self):
        """إنشاء إحصائيات المبيعات"""
        stats_widget = QWidget()
        stats_layout = QHBoxLayout(stats_widget)
        stats_layout.setSpacing(10)
        
        # مبيعات اليوم
        today_stat = self.create_stat_card("📈", "42,890", "مبيعات اليوم")
        stats_layout.addWidget(today_stat)
        
        # عدد الفواتير
        invoices_stat = self.create_stat_card("🧾", "127", "فاتورة اليوم")
        stats_layout.addWidget(invoices_stat)
        
        # متوسط البيع
        avg_stat = self.create_stat_card("💡", "337", "متوسط البيع")
        stats_layout.addWidget(avg_stat)
        
        return stats_widget
    
    def create_stat_card(self, icon, value, label):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("color: white; font-size: 20px;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 10px;")
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(label_widget)
        
        return card
    
    def create_sales_summary(self):
        """إنشاء ملخص المبيعات"""
        summary_frame = QFrame()
        summary_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(summary_frame)
        layout.setSpacing(15)
        
        # عنوان الملخص
        title = QLabel("📊 ملخص المبيعات")
        title.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # الإحصائيات التفصيلية
        stats_grid = QGridLayout()
        
        # مبيعات اليوم
        today_sales = self.create_summary_item("💰 مبيعات اليوم", "42,890 ج.م", "#28a745")
        stats_grid.addWidget(today_sales, 0, 0)
        
        # مبيعات الأسبوع
        week_sales = self.create_summary_item("📅 مبيعات الأسبوع", "285,670 ج.م", "#007bff")
        stats_grid.addWidget(week_sales, 0, 1)
        
        # مبيعات الشهر
        month_sales = self.create_summary_item("📊 مبيعات الشهر", "1,234,567 ج.م", "#6f42c1")
        stats_grid.addWidget(month_sales, 1, 0)
        
        # أفضل منتج
        best_product = self.create_summary_item("🏆 أفضل منتج", "لابتوب ديل", "#ffc107")
        stats_grid.addWidget(best_product, 1, 1)
        
        layout.addLayout(stats_grid)
        
        # الرسم البياني (محاكاة)
        chart_placeholder = QFrame()
        chart_placeholder.setFixedHeight(200)
        chart_placeholder.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                border: 2px dashed #dee2e6;
            }
        """)
        
        chart_layout = QVBoxLayout(chart_placeholder)
        chart_label = QLabel("📈 رسم بياني للمبيعات\n(سيتم إضافته قريباً)")
        chart_label.setAlignment(Qt.AlignCenter)
        chart_label.setStyleSheet("color: #6c757d; font-size: 14px;")
        chart_layout.addWidget(chart_label)
        
        layout.addWidget(chart_placeholder)
        
        return summary_frame
    
    def create_summary_item(self, title, value, color):
        """إنشاء عنصر ملخص"""
        item_frame = QFrame()
        item_frame.setStyleSheet(f"""
            QFrame {{
                background: white;
                border-left: 4px solid {color};
                border-radius: 4px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }}
        """)
        
        layout = QVBoxLayout(item_frame)
        layout.setSpacing(5)
        
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            color: {color};
            font-size: 12px;
            font-weight: bold;
        """)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
        """)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        return item_frame
    
    def create_sales_toolbar(self):
        """إنشاء شريط أدوات المبيعات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        
        # فلتر التاريخ
        date_filter = QDateEdit()
        date_filter.setDate(QDate.currentDate())
        date_filter.setCalendarPopup(True)
        date_filter.setStyleSheet("""
            QDateEdit {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
            }
        """)
        
        # فلتر الحالة
        status_filter = QComboBox()
        status_filter.addItems(["جميع الحالات", "مكتمل", "جاري", "ملغي"])
        status_filter.setStyleSheet("""
            QComboBox {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
            }
        """)
        
        # زر تقرير المبيعات
        report_btn = QPushButton("📊 تقرير المبيعات")
        report_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        report_btn.clicked.connect(self.generate_sales_report)
        
        # زر تصدير
        export_btn = QPushButton("📤 تصدير")
        export_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        export_btn.clicked.connect(self.export_sales)
        
        layout.addWidget(QLabel("من تاريخ:"))
        layout.addWidget(date_filter)
        layout.addWidget(QLabel("الحالة:"))
        layout.addWidget(status_filter)
        layout.addWidget(report_btn)
        layout.addWidget(export_btn)
        layout.addStretch()
        
        # إجمالي المبيعات المفلترة
        total_label = QLabel("الإجمالي: 41,890 ج.م")
        total_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 14px;")
        layout.addWidget(total_label)
        
        return toolbar_frame
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFixedHeight(40)
        status_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 8px 15px;
            }
        """)
        
        layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز - نظام المبيعات متاح")
        self.status_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 12px;")
        
        self.sales_count_label = QLabel("5 فواتير")
        self.sales_count_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        
        self.daily_total_label = QLabel("إجمالي اليوم: 41,890 ج.م")
        self.daily_total_label.setStyleSheet("color: #007bff; font-weight: bold; font-size: 12px;")
        
        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.sales_count_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.daily_total_label)
        
        return status_frame
    
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_data)
        self.update_timer.start(30000)  # تحديث كل 30 ثانية
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.status_label.setText(f"تم التحديث - {datetime.now().strftime('%H:%M')}")
    
    def generate_sales_report(self):
        """إنشاء تقرير المبيعات"""
        QMessageBox.information(self, "تقرير المبيعات", "تم إنشاء تقرير المبيعات بنجاح!")
    
    def export_sales(self):
        """تصدير المبيعات"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير المبيعات", "sales.xlsx",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;PDF Files (*.pdf)"
        )
        
        if file_path:
            QMessageBox.information(self, "تصدير", f"تم تصدير المبيعات إلى:\n{file_path}")"""
صفحة المبيعات المحسنة مع نقطة بيع سريعة وإدارة شاملة
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QTableWidget, QTableWidgetItem,
                              QHeaderView, QComboBox, QLineEdit, QSpinBox,
                              QDoubleSpinBox, QTextEdit, QGroupBox, QGridLayout,
                              QScrollArea, QSplitter, QTabWidget, QFormLayout,
                              QMessageBox, QDialog, QDialogButtonBox, QCheckBox,
                              QProgressBar, QFileDialog, QMenu, QApplication,
                              QCalendarWidget, QDateEdit, QListWidget, QListWidgetItem)
from PySide6.QtCore import Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QDate
from PySide6.QtGui import QFont, QColor, QPainter, QPen, QBrush, QPixmap, QAction, QCursor
import json
import random
from datetime import datetime, timedelta

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class QuickSalePOSWidget(QWidget):
    """واجهة نقطة البيع السريعة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.cart_items = []
        self.total_amount = 0.0
        self.init_ui()
        self.load_products()
    
    def init_ui(self):
        """إعداد واجهة نقطة البيع"""
        self.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 8px;
                border: 1px solid #dee2e6;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)
        
        # عنوان نقطة البيع
        title = QLabel("💰 نقطة البيع السريعة")
        title.setStyleSheet("""
            color: #28a745;
            font-size: 16px;
            font-weight: bold;
            padding: 10px 0;
            border-bottom: 2px solid #e8f5e8;
        """)
        layout.addWidget(title)
        
        # البحث عن المنتجات
        search_layout = QHBoxLayout()
        
        self.product_search = QLineEdit()
        self.product_search.setPlaceholderText("ابحث عن منتج أو امسح الباركود...")
        self.product_search.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #28a745;
                border-radius: 20px;
                font-size: 12px;
                background: #f8fff8;
            }
            QLineEdit:focus {
                border-color: #20c997;
                background: white;
            }
        """)
        self.product_search.textChanged.connect(self.search_products)
        
        barcode_btn = QPushButton("📷")
        barcode_btn.setFixedSize(35, 35)
        barcode_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 17px;
                font-size: 14px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        barcode_btn.setToolTip("مسح الباركود")
        barcode_btn.clicked.connect(self.scan_barcode)
        
        search_layout.addWidget(self.product_search)
        search_layout.addWidget(barcode_btn)
        layout.addLayout(search_layout)
        
        # قائمة المنتجات
        self.products_list = QListWidget()
        self.products_list.setMaximumHeight(150)
        self.products_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #e9ecef;
                border-radius: 4px;
                background: #f8f9fa;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }
            QListWidget::item:hover {
                background: #e8f5e8;
            }
            QListWidget::item:selected {
                background: #28a745;
                color: white;
            }
        """)
        self.products_list.itemDoubleClicked.connect(self.add_product_to_cart)
        layout.addWidget(self.products_list)
        
        # سلة المشتريات
        cart_title = QLabel("🛒 سلة المشتريات")
        cart_title.setStyleSheet("""
            color: #495057;
            font-size: 14px;
            font-weight: bold;
            margin-top: 10px;
        """)
        layout.addWidget(cart_title)
        
        self.cart_table = QTableWidget()
        self.cart_table.setColumnCount(5)
        self.cart_table.setHorizontalHeaderLabels(["المنتج", "السعر", "الكمية", "الإجمالي", "حذف"])
        self.cart_table.horizontalHeader().setStretchLastSection(False)
        self.cart_table.setMaximumHeight(200)
        self.cart_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-size: 11px;
            }
            QHeaderView::section {
                background: #28a745;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # تحديد عرض الأعمدة
        header = self.cart_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # السعر
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # الكمية
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # الإجمالي
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # حذف
        
        self.cart_table.setColumnWidth(1, 70)   # السعر
        self.cart_table.setColumnWidth(2, 60)   # الكمية
        self.cart_table.setColumnWidth(3, 80)   # الإجمالي
        self.cart_table.setColumnWidth(4, 50)   # حذف
        
        layout.addWidget(self.cart_table)
        
        # الإجمالي
        total_frame = QFrame()
        total_frame.setStyleSheet("""
            QFrame {
                background: #e8f5e8;
                border-radius: 6px;
                padding: 10px;
                border: 2px solid #28a745;
            }
        """)
        total_layout = QHBoxLayout(total_frame)
        
        total_label = QLabel("الإجمالي:")
        total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #28a745;")
        
        self.total_amount_label = QLabel("0.00 ج.م")
        self.total_amount_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #28a745;")
        
        total_layout.addWidget(total_label)
        total_layout.addStretch()
        total_layout.addWidget(self.total_amount_label)
        
        layout.addWidget(total_frame)
        
        # أزرار العمليات
        buttons_layout = QGridLayout()
        
        # زر إتمام البيع
        complete_btn = QPushButton("✅ إتمام البيع")
        complete_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        complete_btn.clicked.connect(self.complete_sale)
        
        # زر مسح السلة
        clear_btn = QPushButton("🗑️ مسح السلة")
        clear_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        clear_btn.clicked.connect(self.clear_cart)
        
        buttons_layout.addWidget(complete_btn, 0, 0)
        buttons_layout.addWidget(clear_btn, 0, 1)
        
        layout.addLayout(buttons_layout)
    
    def load_products(self):
        """تحميل المنتجات"""
        self.products = [
            {"id": 1, "name": "لابتوب ديل", "price": 15000, "stock": 25},
            {"id": 2, "name": "قميص قطني", "price": 120, "stock": 150},
            {"id": 3, "name": "هاتف ذكي", "price": 8000, "stock": 3},
            {"id": 4, "name": "كتاب برمجة", "price": 250, "stock": 45},
            {"id": 5, "name": "مكواة كهربائية", "price": 300, "stock": 12},
        ]
        self.update_products_list()
    
    def update_products_list(self):
        """تحديث قائمة المنتجات"""
        self.products_list.clear()
        search_text = self.product_search.text().lower()
        
        for product in self.products:
            if not search_text or search_text in product["name"].lower():
                item_text = f"{product['name']} - {product['price']:.2f} ج.م (متوفر: {product['stock']})"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, product)
                self.products_list.addItem(item)
    
    def search_products(self):
        """البحث في المنتجات"""
        self.update_products_list()
    
    def scan_barcode(self):
        """مسح الباركود"""
        QMessageBox.information(self, "مسح الباركود", "ميزة مسح الباركود ستتوفر قريباً!")
    
    def add_product_to_cart(self, item):
        """إضافة منتج إلى السلة"""
        product = item.data(Qt.UserRole)
        
        if product["stock"] <= 0:
            QMessageBox.warning(self, "تحذير", f"المنتج '{product['name']}' غير متوفر!")
            return
        
        # البحث عن المنتج في السلة
        for i, cart_item in enumerate(self.cart_items):
            if cart_item["id"] == product["id"]:
                # زيادة الكمية
                if cart_item["quantity"] < product["stock"]:
                    cart_item["quantity"] += 1
                    self.update_cart_display()
                    return
                else:
                    QMessageBox.warning(self, "تحذير", "الكمية المطلوبة تتجاوز المخزون المتاح!")
                    return
        
        # إضافة منتج جديد للسلة
        cart_item = {
            "id": product["id"],
            "name": product["name"],
            "price": product["price"],
            "quantity": 1,
            "stock": product["stock"]
        }
        self.cart_items.append(cart_item)
        self.update_cart_display()
    
    def update_cart_display(self):
        """تحديث عرض السلة"""
        self.cart_table.setRowCount(len(self.cart_items))
        self.total_amount = 0.0
        
        for row, item in enumerate(self.cart_items):
            # اسم المنتج
            self.cart_table.setItem(row, 0, QTableWidgetItem(item["name"]))
            
            # السعر
            price_item = QTableWidgetItem(f"{item['price']:.2f}")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.cart_table.setItem(row, 1, price_item)
            
            # الكمية
            quantity_spin = QSpinBox()
            quantity_spin.setRange(1, item["stock"])
            quantity_spin.setValue(item["quantity"])
            quantity_spin.valueChanged.connect(lambda value, r=row: self.update_quantity(r, value))
            self.cart_table.setCellWidget(row, 2, quantity_spin)
            
            # الإجمالي
            total_price = item["price"] * item["quantity"]
            total_item = QTableWidgetItem(f"{total_price:.2f}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.cart_table.setItem(row, 3, total_item)
            
            # زر الحذف
            delete_btn = QPushButton("🗑️")
            delete_btn.setFixedSize(30, 25)
            delete_btn.setStyleSheet("""
                QPushButton {
                    background: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 12px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background: #c82333;
                }
            """)
            delete_btn.clicked.connect(lambda checked, r=row: self.remove_from_cart(r))
            self.cart_table.setCellWidget(row, 4, delete_btn)
            
            self.total_amount += total_price
        
        self.total_amount_label.setText(f"{self.total_amount:.2f} ج.م")
    
    def update_quantity(self, row, quantity):
        """تحديث الكمية"""
        if row < len(self.cart_items):
            self.cart_items[row]["quantity"] = quantity
            self.update_cart_display()
    
    def remove_from_cart(self, row):
        """حذف منتج من السلة"""
        if row < len(self.cart_items):
            del self.cart_items[row]
            self.update_cart_display()
    
    def clear_cart(self):
        """مسح السلة"""
        reply = QMessageBox.question(
            self, "تأكيد", "هل تريد مسح جميع المنتجات من السلة؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.cart_items.clear()
            self.update_cart_display()
    
    def complete_sale(self):
        """إتمام البيع"""
        if not self.cart_items:
            QMessageBox.warning(self, "تحذير", "السلة فارغة!")
            return
        
        # نافذة إتمام البيع
        dialog = SaleCompletionDialog(self.cart_items, self.total_amount, self)
        if dialog.exec() == QDialog.Accepted:
            self.cart_items.clear()
            self.update_cart_display()
            QMessageBox.information(self, "نجح البيع", "تم إتمام البيع بنجاح!")


class SaleCompletionDialog(QDialog):
    """نافذة إتمام البيع"""
    
    def __init__(self, cart_items, total_amount, parent=None):
        super().__init__(parent)
        self.cart_items = cart_items
        self.total_amount = total_amount
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة النافذة"""
        self.setWindowTitle("إتمام البيع")
        self.setFixedSize(400, 500)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان النافذة
        title = QLabel("💳 إتمام عملية البيع")
        title.setStyleSheet("""
            color: #28a745;
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # ملخص البيع
        summary_frame = QFrame()
        summary_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        summary_layout = QVBoxLayout(summary_frame)
        
        # عدد المنتجات
        items_count = sum(item["quantity"] for item in self.cart_items)
        count_label = QLabel(f"عدد المنتجات: {items_count} قطعة")
        count_label.setStyleSheet("font-size: 12px; color: #6c757d;")
        
        # الإجمالي
        total_label = QLabel(f"الإجمالي: {self.total_amount:.2f} ج.م")
        total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #28a745;")
        
        summary_layout.addWidget(count_label)
        summary_layout.addWidget(total_label)
        layout.addWidget(summary_frame)
        
        # بيانات العميل
        customer_group = QGroupBox("معلومات العميل")
        customer_layout = QFormLayout(customer_group)
        
        self.customer_name = QLineEdit()
        self.customer_name.setPlaceholderText("اسم العميل (اختياري)")
        customer_layout.addRow("الاسم:", self.customer_name)
        
        self.customer_phone = QLineEdit()
        self.customer_phone.setPlaceholderText("رقم الهاتف (اختياري)")
        customer_layout.addRow("الهاتف:", self.customer_phone)
        
        layout.addWidget(customer_group)
        
        # طريقة الدفع
        payment_group = QGroupBox("طريقة الدفع")
        payment_layout = QVBoxLayout(payment_group)
        
        self.cash_radio = QCheckBox("نقدي")
        self.cash_radio.setChecked(True)
        self.card_radio = QCheckBox("بطاقة ائتمان")
        self.installment_radio = QCheckBox("تقسيط")
        
        payment_layout.addWidget(self.cash_radio)
        payment_layout.addWidget(self.card_radio)
        payment_layout.addWidget(self.installment_radio)
        
        layout.addWidget(payment_group)
        
        # المبلغ المدفوع
        payment_frame = QFrame()
        payment_frame.setStyleSheet("""
            QFrame {
                background: #fff3cd;
                border-radius: 6px;
                padding: 10px;
                border: 1px solid #ffc107;
            }
        """)
        payment_layout_frame = QFormLayout(payment_frame)
        
        self.paid_amount = QDoubleSpinBox()
        self.paid_amount.setRange(0, 999999)
        self.paid_amount.setValue(self.total_amount)
        self.paid_amount.setSuffix(" ج.م")
        self.paid_amount.valueChanged.connect(self.calculate_change)
        
        self.change_label = QLabel("0.00 ج.م")
        self.change_label.setStyleSheet("font-weight: bold; color: #28a745;")
        
        payment_layout_frame.addRow("المبلغ المدفوع:", self.paid_amount)
        payment_layout_frame.addRow("الباقي:", self.change_label)
        
        layout.addWidget(payment_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        complete_btn = QPushButton("✅ إتمام البيع")
        complete_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        complete_btn.clicked.connect(self.accept)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #545b62;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(complete_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def calculate_change(self):
        """حساب الباقي"""
        paid = self.paid_amount.value()
        change = paid - self.total_amount
        self.change_label.setText(f"{change:.2f} ج.م")
        
        if change < 0:
            self.change_label.setStyleSheet("font-weight: bold; color: #dc3545;")
        else:
            self.change_label.setStyleSheet("font-weight: bold; color: #28a745;")


class SalesTableWidget(QTableWidget):
    """جدول المبيعات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_table()
        self.load_sales_data()
    
    def init_table(self):
        """إعداد الجدول"""
        columns = [
            "رقم الفاتورة", "التاريخ", "العميل", "عدد المنتجات", 
            "الإجمالي", "طريقة الدفع", "الحالة", "الإجراءات"
        ]
        
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 12px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #495057;
                padding: 12px 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        # إعدادات الجدول  
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        self.verticalHeader().setVisible(False)
        
        # تحديد عرض الأعمدة
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)   # رقم الفاتورة
        header.setSectionResizeMode(1, QHeaderView.Fixed)   # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Stretch) # العميل
        header.setSectionResizeMode(3, QHeaderView.Fixed)   # عدد المنتجات
        header.setSectionResizeMode(4, QHeaderView.Fixed)   # الإجمالي
        header.setSectionResizeMode(5, QHeaderView.Fixed)   # طريقة الدفع
        header.setSectionResizeMode(6, QHeaderView.Fixed)   # الحالة
        header.setSectionResizeMode(7, QHeaderView.Fixed)   # الإجراءات
        
        self.setColumnWidth(0, 100)  # رقم الفاتورة
        self.setColumnWidth(1, 100)  # التاريخ
        self.setColumnWidth(3, 90)   # عدد المنتجات
        self.setColumnWidth(4, 100)  # الإجمالي
        self.setColumnWidth(5, 100)  # طريقة الدفع
        self.setColumnWidth(6, 80)   # الحالة
        self.setColumnWidth(7, 120)  # الإجراءات
    
    def load_sales_data(self):
        """تحميل بيانات المبيعات"""
        sales_data = [
            {"invoice": "INV-001", "date": "2024-01-15", "customer": "أحمد محمد", 
             "items": 3, "total": 16370, "payment": "نقدي", "status": "مكتمل"},
            {"invoice": "INV-002", "date": "2024-01-15", "customer": "سارة أحمد", 
             "items": 2, "total": 8120, "payment": "بطاقة", "status": "مكتمل"},
            {"invoice": "INV-003", "date": "2024-01-14", "customer": "محمد علي", 
             "items": 1, "total": 15000, "payment": "تقسيط", "status": "جاري"},
            {"invoice": "INV-004", "date": "2024-01-14", "customer": "فاطمة حسن", 
             "items": 5, "total": 1850, "payment": "نقدي", "status": "مكتمل"},
            {"invoice": "INV-005", "date": "2024-01-13", "customer": "عمر خالد", 
             "items": 2, "total": 550, "payment": "بطاقة", "status": "ملغي"},
        ]
        
        self.setRowCount(len(sales_data))
        
        for row, sale in enumerate(sales_data):
            # رقم الفاتورة
            invoice_item = QTableWidgetItem(sale["invoice"])
            invoice_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 0, invoice_item)
            
            # التاريخ
            date_item = QTableWidgetItem(sale["date"])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 1, date_item)
            
            # العميل
            self.setItem(row, 2, QTableWidgetItem(sale["customer"]))
            
            # عدد المنتجات
            items_item = QTableWidgetItem(f"{sale['items']} منتج")
            items_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 3, items_item)
            
            # الإجمالي
            total_item = QTableWidgetItem(f"{sale['total']:,.2f} ج.م")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 4, total_item)
            
            # طريقة الدفع
            payment_item = QTableWidgetItem(sale["payment"])
            payment_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 5, payment_item)
            
            # الحالة
            status_colors = {
                "مكتمل": {"bg": "#d4edda", "fg": "#155724"},
                "جاري": {"bg": "#fff3cd", "fg": "#856404"},
                "ملغي": {"bg": "#f8d7da", "fg": "#721c24"}
            }
            
            status_item = QTableWidgetItem(sale["status"])
            status_item.setTextAlignment(Qt.AlignCenter)
            color = status_colors.get(sale["status"], status_colors["مكتمل"])
            status_item.setBackground(QColor(color["bg"]))
            status_item.setForeground(QColor(color["fg"]))
            self.setItem(row, 6, status_item)
            
            # الإجراءات
            actions_widget = self.create_actions_widget(row, sale)
            self.setCellWidget(row, 7, actions_widget)
    
    def create_actions_widget(self, row, sale_data):
        """إنشاء أزرار الإجراءات"""
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(5, 2, 5, 2)
        actions_layout.setSpacing(3)
        
        # زر عرض التفاصيل
        view_btn = QPushButton("👁️")
        view_btn.setFixedSize(22, 22)
        view_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        view_btn.setToolTip("عرض التفاصيل")
        view_btn.clicked.connect(lambda: self.view_sale_details(row, sale_data))
        
        # زر طباعة الفاتورة
        print_btn = QPushButton("🖨️")
        print_btn.setFixedSize(22, 22)
        print_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        print_btn.setToolTip("طباعة الفاتورة")
        print_btn.clicked.connect(lambda: self.print_invoice(row, sale_data))
        
        # زر حذف/إلغاء
        delete_btn = QPushButton("🗑️")
        delete_btn.setFixedSize(22, 22)
        delete_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        delete_btn.setToolTip("إلغاء الفاتورة")
        delete_btn.clicked.connect(lambda: self.cancel_sale(row, sale_data))
        
        actions_layout.addWidget(view_btn)
        actions_layout.addWidget(print_btn)
        actions_layout.addWidget(delete_btn)
        actions_layout.addStretch()
        
        return actions_widget
    
    def view_sale_details(self, row, sale_data):
        """عرض تفاصيل البيع"""
        details_text = f"""
        🧾 تفاصيل الفاتورة:
        
        📋 رقم الفاتورة: {sale_data['invoice']}
        📅 التاريخ: {sale_data['date']}
        👤 العميل: {sale_data['customer']}
        📦 عدد المنتجات: {sale_data['items']} منتج
        💰 الإجمالي: {sale_data['total']:,.2f} ج.م
        💳 طريقة الدفع: {sale_data['payment']}
        ✅ الحالة: {sale_data['status']}
        """
        
        QMessageBox.information(self, "تفاصيل البيع", details_text)
    
    def print_invoice(self, row, sale_data):
        """طباعة الفاتورة"""
        QMessageBox.information(self, "طباعة", f"تم إرسال الفاتورة {sale_data['invoice']} للطباعة!")
    
    def cancel_sale(self, row, sale_data):
        """إلغاء البيع"""
        if sale_data['status'] == 'ملغي':
            QMessageBox.information(self, "تنبيه", "هذه الفاتورة ملغية بالفعل!")
            return
        
        reply = QMessageBox.question(
            self, "تأكيد الإلغاء",
            f"هل تريد إلغاء الفاتورة {sale_data['invoice']}؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الإلغاء", "تم إلغاء الفاتورة بنجاح!")
            self.load_sales_data()


class EnhancedSalesWindow(QWidget):
    """نافذة المبيعات المحسنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # التبويبات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background: white;
                border-radius: 8px;
            }
            QTabBar::tab {
                background: #f8f9fa;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                border: 1px solid #dee2e6;
                color: #495057;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background: white;
                color: #007bff;
                border-bottom: 1px solid white;
            }
            QTabBar::tab:hover {
                background: #e9ecef;
            }
        """)
        
        # تبويب نقطة البيع
        pos_tab = QWidget()
        pos_layout = QHBoxLayout(pos_tab)
        pos_layout.setContentsMargins(20, 20, 20, 20)
        
        # نقطة البيع السريعة
        self.pos_widget = QuickSalePOSWidget()
        self.pos_widget.setMaximumWidth(400)
        pos_layout.addWidget(self.pos_widget)
        
        # منطقة الملخص والإحصائيات
        summary_widget = self.create_sales_summary()
        pos_layout.addWidget(summary_widget)
        
        self.tabs.addTab(pos_tab, "💰 نقطة البيع")
        
        # تبويب إدارة المبيعات
        sales_management_tab = QWidget()
        sales_layout = QVBoxLayout(sales_management_tab)
        sales_layout.setContentsMargins(20, 20, 20, 20)
        
        # شريط أدوات المبيعات
        sales_toolbar = self.create_sales_toolbar()
        sales_layout.addWidget(sales_toolbar)
        
        # جدول المبيعات
        self.sales_table = SalesTableWidget()
        sales_layout.addWidget(self.sales_table)
        
        self.tabs.addTab(sales_management_tab, "📊 إدارة المبيعات")
        
        main_layout.addWidget(self.tabs)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
    
    def create_header(self):
        """إنشاء الرأس"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #007bff, stop:0.5 #6610f2, stop:1 #007bff);
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # معلومات الصفحة
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        title = QLabel("🛒 إدارة المبيعات")
        title.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
        """)
        
        subtitle = QLabel("نقطة بيع سريعة وإدارة شاملة للمبيعات مع تقارير مفصلة")
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-top: 5px;
        """)
        
        info_layout.addWidget(title)
        info_layout.addWidget(subtitle)
        
        # الإحصائيات السريعة
        stats_widget = self.create_sales_stats()
        
        layout.addWidget(info_widget, 7)
        layout.addWidget(stats_widget, 3)
        
        return header_frame
    
    def create_sales_stats(self):
        """إنشاء إحصائيات المبيعات"""
        stats_widget = QWidget()
        stats_layout = QHBoxLayout(stats_widget)
        stats_layout.setSpacing(10)
        
        # مبيعات اليوم
        today_stat = self.create_stat_card("📈", "42,890", "مبيعات اليوم")
        stats_layout.addWidget(today_stat)
        
        # عدد الفواتير
        invoices_stat = self.create_stat_card("🧾", "127", "فاتورة اليوم")
        stats_layout.addWidget(invoices_stat)
        
        # متوسط البيع
        avg_stat = self.create_stat_card("💡", "337", "متوسط البيع")
        stats_layout.addWidget(avg_stat)
        
        return stats_widget
    
    def create_stat_card(self, icon, value, label):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("color: white; font-size: 20px;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 10px;")
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(label_widget)
        
        return card
    
    def create_sales_summary(self):
        """إنشاء ملخص المبيعات"""
        summary_frame = QFrame()
        summary_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(summary_frame)
        layout.setSpacing(15)
        
        # عنوان الملخص
        title = QLabel("📊 ملخص المبيعات")
        title.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # الإحصائيات التفصيلية
        stats_grid = QGridLayout()
        
        # مبيعات اليوم
        today_sales = self.create_summary_item("💰 مبيعات اليوم", "42,890 ج.م", "#28a745")
        stats_grid.addWidget(today_sales, 0, 0)
        
        # مبيعات الأسبوع
        week_sales = self.create_summary_item("📅 مبيعات الأسبوع", "285,670 ج.م", "#007bff")
        stats_grid.addWidget(week_sales, 0, 1)
        
        # مبيعات الشهر
        month_sales = self.create_summary_item("📊 مبيعات الشهر", "1,234,567 ج.م", "#6f42c1")
        stats_grid.addWidget(month_sales, 1, 0)
        
        # أفضل منتج
        best_product = self.create_summary_item("🏆 أفضل منتج", "لابتوب ديل", "#ffc107")
        stats_grid.addWidget(best_product, 1, 1)
        
        layout.addLayout(stats_grid)
        
        # الرسم البياني (محاكاة)
        chart_placeholder = QFrame()
        chart_placeholder.setFixedHeight(200)
        chart_placeholder.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                border: 2px dashed #dee2e6;
            }
        """)
        
        chart_layout = QVBoxLayout(chart_placeholder)
        chart_label = QLabel("📈 رسم بياني للمبيعات\n(سيتم إضافته قريباً)")
        chart_label.setAlignment(Qt.AlignCenter)
        chart_label.setStyleSheet("color: #6c757d; font-size: 14px;")
        chart_layout.addWidget(chart_label)
        
        layout.addWidget(chart_placeholder)
        
        return summary_frame
    
    def create_summary_item(self, title, value, color):
        """إنشاء عنصر ملخص"""
        item_frame = QFrame()
        item_frame.setStyleSheet(f"""
            QFrame {{
                background: white;
                border-left: 4px solid {color};
                border-radius: 4px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }}
        """)
        
        layout = QVBoxLayout(item_frame)
        layout.setSpacing(5)
        
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            color: {color};
            font-size: 12px;
            font-weight: bold;
        """)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
        """)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        return item_frame
    
    def create_sales_toolbar(self):
        """إنشاء شريط أدوات المبيعات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        
        # فلتر التاريخ
        date_filter = QDateEdit()
        date_filter.setDate(QDate.currentDate())
        date_filter.setCalendarPopup(True)
        date_filter.setStyleSheet("""
            QDateEdit {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
            }
        """)
        
        # فلتر الحالة
        status_filter = QComboBox()
        status_filter.addItems(["جميع الحالات", "مكتمل", "جاري", "ملغي"])
        status_filter.setStyleSheet("""
            QComboBox {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
            }
        """)
        
        # زر تقرير المبيعات
        report_btn = QPushButton("📊 تقرير المبيعات")
        report_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        report_btn.clicked.connect(self.generate_sales_report)
        
        # زر تصدير
        export_btn = QPushButton("📤 تصدير")
        export_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        export_btn.clicked.connect(self.export_sales)
        
        layout.addWidget(QLabel("من تاريخ:"))
        layout.addWidget(date_filter)
        layout.addWidget(QLabel("الحالة:"))
        layout.addWidget(status_filter)
        layout.addWidget(report_btn)
        layout.addWidget(export_btn)
        layout.addStretch()
        
        # إجمالي المبيعات المفلترة
        total_label = QLabel("الإجمالي: 41,890 ج.م")
        total_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 14px;")
        layout.addWidget(total_label)
        
        return toolbar_frame
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFixedHeight(40)
        status_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 8px 15px;
            }
        """)
        
        layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز - نظام المبيعات متاح")
        self.status_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 12px;")
        
        self.sales_count_label = QLabel("5 فواتير")
        self.sales_count_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        
        self.daily_total_label = QLabel("إجمالي اليوم: 41,890 ج.م")
        self.daily_total_label.setStyleSheet("color: #007bff; font-weight: bold; font-size: 12px;")
        
        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.sales_count_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.daily_total_label)
        
        return status_frame
    
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_data)
        self.update_timer.start(30000)  # تحديث كل 30 ثانية
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.status_label.setText(f"تم التحديث - {datetime.now().strftime('%H:%M')}")
    
    def generate_sales_report(self):
        """إنشاء تقرير المبيعات"""
        QMessageBox.information(self, "تقرير المبيعات", "تم إنشاء تقرير المبيعات بنجاح!")
    
    def export_sales(self):
        """تصدير المبيعات"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير المبيعات", "sales.xlsx",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;PDF Files (*.pdf)"
        )
        
        if file_path:
            QMessageBox.information(self, "تصدير", f"تم تصدير المبيعات إلى:\n{file_path}")"""
صفحة المبيعات المحسنة مع نقطة بيع سريعة وإدارة شاملة
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QTableWidget, QTableWidgetItem,
                              QHeaderView, QComboBox, QLineEdit, QSpinBox,
                              QDoubleSpinBox, QTextEdit, QGroupBox, QGridLayout,
                              QScrollArea, QSplitter, QTabWidget, QFormLayout,
                              QMessageBox, QDialog, QDialogButtonBox, QCheckBox,
                              QProgressBar, QFileDialog, QMenu, QApplication,
                              QCalendarWidget, QDateEdit, QListWidget, QListWidgetItem)
from PySide6.QtCore import Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QDate
from PySide6.QtGui import QFont, QColor, QPainter, QPen, QBrush, QPixmap, QAction, QCursor
import json
import random
from datetime import datetime, timedelta

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class QuickSalePOSWidget(QWidget):
    """واجهة نقطة البيع السريعة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.cart_items = []
        self.total_amount = 0.0
        self.init_ui()
        self.load_products()
    
    def init_ui(self):
        """إعداد واجهة نقطة البيع"""
        self.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 8px;
                border: 1px solid #dee2e6;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)
        
        # عنوان نقطة البيع
        title = QLabel("💰 نقطة البيع السريعة")
        title.setStyleSheet("""
            color: #28a745;
            font-size: 16px;
            font-weight: bold;
            padding: 10px 0;
            border-bottom: 2px solid #e8f5e8;
        """)
        layout.addWidget(title)
        
        # البحث عن المنتجات
        search_layout = QHBoxLayout()
        
        self.product_search = QLineEdit()
        self.product_search.setPlaceholderText("ابحث عن منتج أو امسح الباركود...")
        self.product_search.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #28a745;
                border-radius: 20px;
                font-size: 12px;
                background: #f8fff8;
            }
            QLineEdit:focus {
                border-color: #20c997;
                background: white;
            }
        """)
        self.product_search.textChanged.connect(self.search_products)
        
        barcode_btn = QPushButton("📷")
        barcode_btn.setFixedSize(35, 35)
        barcode_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 17px;
                font-size: 14px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        barcode_btn.setToolTip("مسح الباركود")
        barcode_btn.clicked.connect(self.scan_barcode)
        
        search_layout.addWidget(self.product_search)
        search_layout.addWidget(barcode_btn)
        layout.addLayout(search_layout)
        
        # قائمة المنتجات
        self.products_list = QListWidget()
        self.products_list.setMaximumHeight(150)
        self.products_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #e9ecef;
                border-radius: 4px;
                background: #f8f9fa;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }
            QListWidget::item:hover {
                background: #e8f5e8;
            }
            QListWidget::item:selected {
                background: #28a745;
                color: white;
            }
        """)
        self.products_list.itemDoubleClicked.connect(self.add_product_to_cart)
        layout.addWidget(self.products_list)
        
        # سلة المشتريات
        cart_title = QLabel("🛒 سلة المشتريات")
        cart_title.setStyleSheet("""
            color: #495057;
            font-size: 14px;
            font-weight: bold;
            margin-top: 10px;
        """)
        layout.addWidget(cart_title)
        
        self.cart_table = QTableWidget()
        self.cart_table.setColumnCount(5)
        self.cart_table.setHorizontalHeaderLabels(["المنتج", "السعر", "الكمية", "الإجمالي", "حذف"])
        self.cart_table.horizontalHeader().setStretchLastSection(False)
        self.cart_table.setMaximumHeight(200)
        self.cart_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-size: 11px;
            }
            QHeaderView::section {
                background: #28a745;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # تحديد عرض الأعمدة
        header = self.cart_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # السعر
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # الكمية
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # الإجمالي
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # حذف
        
        self.cart_table.setColumnWidth(1, 70)   # السعر
        self.cart_table.setColumnWidth(2, 60)   # الكمية
        self.cart_table.setColumnWidth(3, 80)   # الإجمالي
        self.cart_table.setColumnWidth(4, 50)   # حذف
        
        layout.addWidget(self.cart_table)
        
        # الإجمالي
        total_frame = QFrame()
        total_frame.setStyleSheet("""
            QFrame {
                background: #e8f5e8;
                border-radius: 6px;
                padding: 10px;
                border: 2px solid #28a745;
            }
        """)
        total_layout = QHBoxLayout(total_frame)
        
        total_label = QLabel("الإجمالي:")
        total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #28a745;")
        
        self.total_amount_label = QLabel("0.00 ج.م")
        self.total_amount_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #28a745;")
        
        total_layout.addWidget(total_label)
        total_layout.addStretch()
        total_layout.addWidget(self.total_amount_label)
        
        layout.addWidget(total_frame)
        
        # أزرار العمليات
        buttons_layout = QGridLayout()
        
        # زر إتمام البيع
        complete_btn = QPushButton("✅ إتمام البيع")
        complete_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        complete_btn.clicked.connect(self.complete_sale)
        
        # زر مسح السلة
        clear_btn = QPushButton("🗑️ مسح السلة")
        clear_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        clear_btn.clicked.connect(self.clear_cart)
        
        buttons_layout.addWidget(complete_btn, 0, 0)
        buttons_layout.addWidget(clear_btn, 0, 1)
        
        layout.addLayout(buttons_layout)
    
    def load_products(self):
        """تحميل المنتجات"""
        self.products = [
            {"id": 1, "name": "لابتوب ديل", "price": 15000, "stock": 25},
            {"id": 2, "name": "قميص قطني", "price": 120, "stock": 150},
            {"id": 3, "name": "هاتف ذكي", "price": 8000, "stock": 3},
            {"id": 4, "name": "كتاب برمجة", "price": 250, "stock": 45},
            {"id": 5, "name": "مكواة كهربائية", "price": 300, "stock": 12},
        ]
        self.update_products_list()
    
    def update_products_list(self):
        """تحديث قائمة المنتجات"""
        self.products_list.clear()
        search_text = self.product_search.text().lower()
        
        for product in self.products:
            if not search_text or search_text in product["name"].lower():
                item_text = f"{product['name']} - {product['price']:.2f} ج.م (متوفر: {product['stock']})"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, product)
                self.products_list.addItem(item)
    
    def search_products(self):
        """البحث في المنتجات"""
        self.update_products_list()
    
    def scan_barcode(self):
        """مسح الباركود"""
        QMessageBox.information(self, "مسح الباركود", "ميزة مسح الباركود ستتوفر قريباً!")
    
    def add_product_to_cart(self, item):
        """إضافة منتج إلى السلة"""
        product = item.data(Qt.UserRole)
        
        if product["stock"] <= 0:
            QMessageBox.warning(self, "تحذير", f"المنتج '{product['name']}' غير متوفر!")
            return
        
        # البحث عن المنتج في السلة
        for i, cart_item in enumerate(self.cart_items):
            if cart_item["id"] == product["id"]:
                # زيادة الكمية
                if cart_item["quantity"] < product["stock"]:
                    cart_item["quantity"] += 1
                    self.update_cart_display()
                    return
                else:
                    QMessageBox.warning(self, "تحذير", "الكمية المطلوبة تتجاوز المخزون المتاح!")
                    return
        
        # إضافة منتج جديد للسلة
        cart_item = {
            "id": product["id"],
            "name": product["name"],
            "price": product["price"],
            "quantity": 1,
            "stock": product["stock"]
        }
        self.cart_items.append(cart_item)
        self.update_cart_display()
    
    def update_cart_display(self):
        """تحديث عرض السلة"""
        self.cart_table.setRowCount(len(self.cart_items))
        self.total_amount = 0.0
        
        for row, item in enumerate(self.cart_items):
            # اسم المنتج
            self.cart_table.setItem(row, 0, QTableWidgetItem(item["name"]))
            
            # السعر
            price_item = QTableWidgetItem(f"{item['price']:.2f}")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.cart_table.setItem(row, 1, price_item)
            
            # الكمية
            quantity_spin = QSpinBox()
            quantity_spin.setRange(1, item["stock"])
            quantity_spin.setValue(item["quantity"])
            quantity_spin.valueChanged.connect(lambda value, r=row: self.update_quantity(r, value))
            self.cart_table.setCellWidget(row, 2, quantity_spin)
            
            # الإجمالي
            total_price = item["price"] * item["quantity"]
            total_item = QTableWidgetItem(f"{total_price:.2f}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.cart_table.setItem(row, 3, total_item)
            
            # زر الحذف
            delete_btn = QPushButton("🗑️")
            delete_btn.setFixedSize(30, 25)
            delete_btn.setStyleSheet("""
                QPushButton {
                    background: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 12px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background: #c82333;
                }
            """)
            delete_btn.clicked.connect(lambda checked, r=row: self.remove_from_cart(r))
            self.cart_table.setCellWidget(row, 4, delete_btn)
            
            self.total_amount += total_price
        
        self.total_amount_label.setText(f"{self.total_amount:.2f} ج.م")
    
    def update_quantity(self, row, quantity):
        """تحديث الكمية"""
        if row < len(self.cart_items):
            self.cart_items[row]["quantity"] = quantity
            self.update_cart_display()
    
    def remove_from_cart(self, row):
        """حذف منتج من السلة"""
        if row < len(self.cart_items):
            del self.cart_items[row]
            self.update_cart_display()
    
    def clear_cart(self):
        """مسح السلة"""
        reply = QMessageBox.question(
            self, "تأكيد", "هل تريد مسح جميع المنتجات من السلة؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.cart_items.clear()
            self.update_cart_display()
    
    def complete_sale(self):
        """إتمام البيع"""
        if not self.cart_items:
            QMessageBox.warning(self, "تحذير", "السلة فارغة!")
            return
        
        # نافذة إتمام البيع
        dialog = SaleCompletionDialog(self.cart_items, self.total_amount, self)
        if dialog.exec() == QDialog.Accepted:
            self.cart_items.clear()
            self.update_cart_display()
            QMessageBox.information(self, "نجح البيع", "تم إتمام البيع بنجاح!")


class SaleCompletionDialog(QDialog):
    """نافذة إتمام البيع"""
    
    def __init__(self, cart_items, total_amount, parent=None):
        super().__init__(parent)
        self.cart_items = cart_items
        self.total_amount = total_amount
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة النافذة"""
        self.setWindowTitle("إتمام البيع")
        self.setFixedSize(400, 500)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان النافذة
        title = QLabel("💳 إتمام عملية البيع")
        title.setStyleSheet("""
            color: #28a745;
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # ملخص البيع
        summary_frame = QFrame()
        summary_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        summary_layout = QVBoxLayout(summary_frame)
        
        # عدد المنتجات
        items_count = sum(item["quantity"] for item in self.cart_items)
        count_label = QLabel(f"عدد المنتجات: {items_count} قطعة")
        count_label.setStyleSheet("font-size: 12px; color: #6c757d;")
        
        # الإجمالي
        total_label = QLabel(f"الإجمالي: {self.total_amount:.2f} ج.م")
        total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #28a745;")
        
        summary_layout.addWidget(count_label)
        summary_layout.addWidget(total_label)
        layout.addWidget(summary_frame)
        
        # بيانات العميل
        customer_group = QGroupBox("معلومات العميل")
        customer_layout = QFormLayout(customer_group)
        
        self.customer_name = QLineEdit()
        self.customer_name.setPlaceholderText("اسم العميل (اختياري)")
        customer_layout.addRow("الاسم:", self.customer_name)
        
        self.customer_phone = QLineEdit()
        self.customer_phone.setPlaceholderText("رقم الهاتف (اختياري)")
        customer_layout.addRow("الهاتف:", self.customer_phone)
        
        layout.addWidget(customer_group)
        
        # طريقة الدفع
        payment_group = QGroupBox("طريقة الدفع")
        payment_layout = QVBoxLayout(payment_group)
        
        self.cash_radio = QCheckBox("نقدي")
        self.cash_radio.setChecked(True)
        self.card_radio = QCheckBox("بطاقة ائتمان")
        self.installment_radio = QCheckBox("تقسيط")
        
        payment_layout.addWidget(self.cash_radio)
        payment_layout.addWidget(self.card_radio)
        payment_layout.addWidget(self.installment_radio)
        
        layout.addWidget(payment_group)
        
        # المبلغ المدفوع
        payment_frame = QFrame()
        payment_frame.setStyleSheet("""
            QFrame {
                background: #fff3cd;
                border-radius: 6px;
                padding: 10px;
                border: 1px solid #ffc107;
            }
        """)
        payment_layout_frame = QFormLayout(payment_frame)
        
        self.paid_amount = QDoubleSpinBox()
        self.paid_amount.setRange(0, 999999)
        self.paid_amount.setValue(self.total_amount)
        self.paid_amount.setSuffix(" ج.م")
        self.paid_amount.valueChanged.connect(self.calculate_change)
        
        self.change_label = QLabel("0.00 ج.م")
        self.change_label.setStyleSheet("font-weight: bold; color: #28a745;")
        
        payment_layout_frame.addRow("المبلغ المدفوع:", self.paid_amount)
        payment_layout_frame.addRow("الباقي:", self.change_label)
        
        layout.addWidget(payment_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        complete_btn = QPushButton("✅ إتمام البيع")
        complete_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        complete_btn.clicked.connect(self.accept)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #545b62;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(complete_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def calculate_change(self):
        """حساب الباقي"""
        paid = self.paid_amount.value()
        change = paid - self.total_amount
        self.change_label.setText(f"{change:.2f} ج.م")
        
        if change < 0:
            self.change_label.setStyleSheet("font-weight: bold; color: #dc3545;")
        else:
            self.change_label.setStyleSheet("font-weight: bold; color: #28a745;")


class SalesTableWidget(QTableWidget):
    """جدول المبيعات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_table()
        self.load_sales_data()
    
    def init_table(self):
        """إعداد الجدول"""
        columns = [
            "رقم الفاتورة", "التاريخ", "العميل", "عدد المنتجات", 
            "الإجمالي", "طريقة الدفع", "الحالة", "الإجراءات"
        ]
        
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 12px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #495057;
                padding: 12px 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        # إعدادات الجدول  
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        self.verticalHeader().setVisible(False)
        
        # تحديد عرض الأعمدة
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)   # رقم الفاتورة
        header.setSectionResizeMode(1, QHeaderView.Fixed)   # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Stretch) # العميل
        header.setSectionResizeMode(3, QHeaderView.Fixed)   # عدد المنتجات
        header.setSectionResizeMode(4, QHeaderView.Fixed)   # الإجمالي
        header.setSectionResizeMode(5, QHeaderView.Fixed)   # طريقة الدفع
        header.setSectionResizeMode(6, QHeaderView.Fixed)   # الحالة
        header.setSectionResizeMode(7, QHeaderView.Fixed)   # الإجراءات
        
        self.setColumnWidth(0, 100)  # رقم الفاتورة
        self.setColumnWidth(1, 100)  # التاريخ
        self.setColumnWidth(3, 90)   # عدد المنتجات
        self.setColumnWidth(4, 100)  # الإجمالي
        self.setColumnWidth(5, 100)  # طريقة الدفع
        self.setColumnWidth(6, 80)   # الحالة
        self.setColumnWidth(7, 120)  # الإجراءات
    
    def load_sales_data(self):
        """تحميل بيانات المبيعات"""
        sales_data = [
            {"invoice": "INV-001", "date": "2024-01-15", "customer": "أحمد محمد", 
             "items": 3, "total": 16370, "payment": "نقدي", "status": "مكتمل"},
            {"invoice": "INV-002", "date": "2024-01-15", "customer": "سارة أحمد", 
             "items": 2, "total": 8120, "payment": "بطاقة", "status": "مكتمل"},
            {"invoice": "INV-003", "date": "2024-01-14", "customer": "محمد علي", 
             "items": 1, "total": 15000, "payment": "تقسيط", "status": "جاري"},
            {"invoice": "INV-004", "date": "2024-01-14", "customer": "فاطمة حسن", 
             "items": 5, "total": 1850, "payment": "نقدي", "status": "مكتمل"},
            {"invoice": "INV-005", "date": "2024-01-13", "customer": "عمر خالد", 
             "items": 2, "total": 550, "payment": "بطاقة", "status": "ملغي"},
        ]
        
        self.setRowCount(len(sales_data))
        
        for row, sale in enumerate(sales_data):
            # رقم الفاتورة
            invoice_item = QTableWidgetItem(sale["invoice"])
            invoice_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 0, invoice_item)
            
            # التاريخ
            date_item = QTableWidgetItem(sale["date"])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 1, date_item)
            
            # العميل
            self.setItem(row, 2, QTableWidgetItem(sale["customer"]))
            
            # عدد المنتجات
            items_item = QTableWidgetItem(f"{sale['items']} منتج")
            items_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 3, items_item)
            
            # الإجمالي
            total_item = QTableWidgetItem(f"{sale['total']:,.2f} ج.م")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 4, total_item)
            
            # طريقة الدفع
            payment_item = QTableWidgetItem(sale["payment"])
            payment_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 5, payment_item)
            
            # الحالة
            status_colors = {
                "مكتمل": {"bg": "#d4edda", "fg": "#155724"},
                "جاري": {"bg": "#fff3cd", "fg": "#856404"},
                "ملغي": {"bg": "#f8d7da", "fg": "#721c24"}
            }
            
            status_item = QTableWidgetItem(sale["status"])
            status_item.setTextAlignment(Qt.AlignCenter)
            color = status_colors.get(sale["status"], status_colors["مكتمل"])
            status_item.setBackground(QColor(color["bg"]))
            status_item.setForeground(QColor(color["fg"]))
            self.setItem(row, 6, status_item)
            
            # الإجراءات
            actions_widget = self.create_actions_widget(row, sale)
            self.setCellWidget(row, 7, actions_widget)
    
    def create_actions_widget(self, row, sale_data):
        """إنشاء أزرار الإجراءات"""
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(5, 2, 5, 2)
        actions_layout.setSpacing(3)
        
        # زر عرض التفاصيل
        view_btn = QPushButton("👁️")
        view_btn.setFixedSize(22, 22)
        view_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        view_btn.setToolTip("عرض التفاصيل")
        view_btn.clicked.connect(lambda: self.view_sale_details(row, sale_data))
        
        # زر طباعة الفاتورة
        print_btn = QPushButton("🖨️")
        print_btn.setFixedSize(22, 22)
        print_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        print_btn.setToolTip("طباعة الفاتورة")
        print_btn.clicked.connect(lambda: self.print_invoice(row, sale_data))
        
        # زر حذف/إلغاء
        delete_btn = QPushButton("🗑️")
        delete_btn.setFixedSize(22, 22)
        delete_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        delete_btn.setToolTip("إلغاء الفاتورة")
        delete_btn.clicked.connect(lambda: self.cancel_sale(row, sale_data))
        
        actions_layout.addWidget(view_btn)
        actions_layout.addWidget(print_btn)
        actions_layout.addWidget(delete_btn)
        actions_layout.addStretch()
        
        return actions_widget
    
    def view_sale_details(self, row, sale_data):
        """عرض تفاصيل البيع"""
        details_text = f"""
        🧾 تفاصيل الفاتورة:
        
        📋 رقم الفاتورة: {sale_data['invoice']}
        📅 التاريخ: {sale_data['date']}
        👤 العميل: {sale_data['customer']}
        📦 عدد المنتجات: {sale_data['items']} منتج
        💰 الإجمالي: {sale_data['total']:,.2f} ج.م
        💳 طريقة الدفع: {sale_data['payment']}
        ✅ الحالة: {sale_data['status']}
        """
        
        QMessageBox.information(self, "تفاصيل البيع", details_text)
    
    def print_invoice(self, row, sale_data):
        """طباعة الفاتورة"""
        QMessageBox.information(self, "طباعة", f"تم إرسال الفاتورة {sale_data['invoice']} للطباعة!")
    
    def cancel_sale(self, row, sale_data):
        """إلغاء البيع"""
        if sale_data['status'] == 'ملغي':
            QMessageBox.information(self, "تنبيه", "هذه الفاتورة ملغية بالفعل!")
            return
        
        reply = QMessageBox.question(
            self, "تأكيد الإلغاء",
            f"هل تريد إلغاء الفاتورة {sale_data['invoice']}؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الإلغاء", "تم إلغاء الفاتورة بنجاح!")
            self.load_sales_data()


class EnhancedSalesWindow(QWidget):
    """نافذة المبيعات المحسنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # التبويبات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background: white;
                border-radius: 8px;
            }
            QTabBar::tab {
                background: #f8f9fa;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                border: 1px solid #dee2e6;
                color: #495057;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background: white;
                color: #007bff;
                border-bottom: 1px solid white;
            }
            QTabBar::tab:hover {
                background: #e9ecef;
            }
        """)
        
        # تبويب نقطة البيع
        pos_tab = QWidget()
        pos_layout = QHBoxLayout(pos_tab)
        pos_layout.setContentsMargins(20, 20, 20, 20)
        
        # نقطة البيع السريعة
        self.pos_widget = QuickSalePOSWidget()
        self.pos_widget.setMaximumWidth(400)
        pos_layout.addWidget(self.pos_widget)
        
        # منطقة الملخص والإحصائيات
        summary_widget = self.create_sales_summary()
        pos_layout.addWidget(summary_widget)
        
        self.tabs.addTab(pos_tab, "💰 نقطة البيع")
        
        # تبويب إدارة المبيعات
        sales_management_tab = QWidget()
        sales_layout = QVBoxLayout(sales_management_tab)
        sales_layout.setContentsMargins(20, 20, 20, 20)
        
        # شريط أدوات المبيعات
        sales_toolbar = self.create_sales_toolbar()
        sales_layout.addWidget(sales_toolbar)
        
        # جدول المبيعات
        self.sales_table = SalesTableWidget()
        sales_layout.addWidget(self.sales_table)
        
        self.tabs.addTab(sales_management_tab, "📊 إدارة المبيعات")
        
        main_layout.addWidget(self.tabs)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
    
    def create_header(self):
        """إنشاء الرأس"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #007bff, stop:0.5 #6610f2, stop:1 #007bff);
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # معلومات الصفحة
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        title = QLabel("🛒 إدارة المبيعات")
        title.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
        """)
        
        subtitle = QLabel("نقطة بيع سريعة وإدارة شاملة للمبيعات مع تقارير مفصلة")
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-top: 5px;
        """)
        
        info_layout.addWidget(title)
        info_layout.addWidget(subtitle)
        
        # الإحصائيات السريعة
        stats_widget = self.create_sales_stats()
        
        layout.addWidget(info_widget, 7)
        layout.addWidget(stats_widget, 3)
        
        return header_frame
    
    def create_sales_stats(self):
        """إنشاء إحصائيات المبيعات"""
        stats_widget = QWidget()
        stats_layout = QHBoxLayout(stats_widget)
        stats_layout.setSpacing(10)
        
        # مبيعات اليوم
        today_stat = self.create_stat_card("📈", "42,890", "مبيعات اليوم")
        stats_layout.addWidget(today_stat)
        
        # عدد الفواتير
        invoices_stat = self.create_stat_card("🧾", "127", "فاتورة اليوم")
        stats_layout.addWidget(invoices_stat)
        
        # متوسط البيع
        avg_stat = self.create_stat_card("💡", "337", "متوسط البيع")
        stats_layout.addWidget(avg_stat)
        
        return stats_widget
    
    def create_stat_card(self, icon, value, label):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("color: white; font-size: 20px;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 10px;")
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(label_widget)
        
        return card
    
    def create_sales_summary(self):
        """إنشاء ملخص المبيعات"""
        summary_frame = QFrame()
        summary_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(summary_frame)
        layout.setSpacing(15)
        
        # عنوان الملخص
        title = QLabel("📊 ملخص المبيعات")
        title.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # الإحصائيات التفصيلية
        stats_grid = QGridLayout()
        
        # مبيعات اليوم
        today_sales = self.create_summary_item("💰 مبيعات اليوم", "42,890 ج.م", "#28a745")
        stats_grid.addWidget(today_sales, 0, 0)
        
        # مبيعات الأسبوع
        week_sales = self.create_summary_item("📅 مبيعات الأسبوع", "285,670 ج.م", "#007bff")
        stats_grid.addWidget(week_sales, 0, 1)
        
        # مبيعات الشهر
        month_sales = self.create_summary_item("📊 مبيعات الشهر", "1,234,567 ج.م", "#6f42c1")
        stats_grid.addWidget(month_sales, 1, 0)
        
        # أفضل منتج
        best_product = self.create_summary_item("🏆 أفضل منتج", "لابتوب ديل", "#ffc107")
        stats_grid.addWidget(best_product, 1, 1)
        
        layout.addLayout(stats_grid)
        
        # الرسم البياني (محاكاة)
        chart_placeholder = QFrame()
        chart_placeholder.setFixedHeight(200)
        chart_placeholder.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                border: 2px dashed #dee2e6;
            }
        """)
        
        chart_layout = QVBoxLayout(chart_placeholder)
        chart_label = QLabel("📈 رسم بياني للمبيعات\n(سيتم إضافته قريباً)")
        chart_label.setAlignment(Qt.AlignCenter)
        chart_label.setStyleSheet("color: #6c757d; font-size: 14px;")
        chart_layout.addWidget(chart_label)
        
        layout.addWidget(chart_placeholder)
        
        return summary_frame
    
    def create_summary_item(self, title, value, color):
        """إنشاء عنصر ملخص"""
        item_frame = QFrame()
        item_frame.setStyleSheet(f"""
            QFrame {{
                background: white;
                border-left: 4px solid {color};
                border-radius: 4px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }}
        """)
        
        layout = QVBoxLayout(item_frame)
        layout.setSpacing(5)
        
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            color: {color};
            font-size: 12px;
            font-weight: bold;
        """)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
        """)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        return item_frame
    
    def create_sales_toolbar(self):
        """إنشاء شريط أدوات المبيعات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        
        # فلتر التاريخ
        date_filter = QDateEdit()
        date_filter.setDate(QDate.currentDate())
        date_filter.setCalendarPopup(True)
        date_filter.setStyleSheet("""
            QDateEdit {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
            }
        """)
        
        # فلتر الحالة
        status_filter = QComboBox()
        status_filter.addItems(["جميع الحالات", "مكتمل", "جاري", "ملغي"])
        status_filter.setStyleSheet("""
            QComboBox {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
            }
        """)
        
        # زر تقرير المبيعات
        report_btn = QPushButton("📊 تقرير المبيعات")
        report_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        report_btn.clicked.connect(self.generate_sales_report)
        
        # زر تصدير
        export_btn = QPushButton("📤 تصدير")
        export_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        export_btn.clicked.connect(self.export_sales)
        
        layout.addWidget(QLabel("من تاريخ:"))
        layout.addWidget(date_filter)
        layout.addWidget(QLabel("الحالة:"))
        layout.addWidget(status_filter)
        layout.addWidget(report_btn)
        layout.addWidget(export_btn)
        layout.addStretch()
        
        # إجمالي المبيعات المفلترة
        total_label = QLabel("الإجمالي: 41,890 ج.م")
        total_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 14px;")
        layout.addWidget(total_label)
        
        return toolbar_frame
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFixedHeight(40)
        status_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 8px 15px;
            }
        """)
        
        layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز - نظام المبيعات متاح")
        self.status_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 12px;")
        
        self.sales_count_label = QLabel("5 فواتير")
        self.sales_count_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        
        self.daily_total_label = QLabel("إجمالي اليوم: 41,890 ج.م")
        self.daily_total_label.setStyleSheet("color: #007bff; font-weight: bold; font-size: 12px;")
        
        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.sales_count_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.daily_total_label)
        
        return status_frame
    
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_data)
        self.update_timer.start(30000)  # تحديث كل 30 ثانية
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.status_label.setText(f"تم التحديث - {datetime.now().strftime('%H:%M')}")
    
    def generate_sales_report(self):
        """إنشاء تقرير المبيعات"""
        QMessageBox.information(self, "تقرير المبيعات", "تم إنشاء تقرير المبيعات بنجاح!")
    
    def export_sales(self):
        """تصدير المبيعات"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير المبيعات", "sales.xlsx",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;PDF Files (*.pdf)"
        )
        
        if file_path:
            QMessageBox.information(self, "تصدير", f"تم تصدير المبيعات إلى:\n{file_path}")
