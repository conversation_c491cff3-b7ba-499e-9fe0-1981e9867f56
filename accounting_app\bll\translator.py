"""
مترجم التطبيق - يتعامل مع ترجمة نصوص التطبيق
"""
from accounting_app.bll.settings_manager import SettingsManager


class Translator:
    """مترجم التطبيق - يتعامل مع ترجمة نصوص التطبيق"""
    
    def __init__(self):
        """تهيئة المترجم"""
        self.settings_manager = SettingsManager()
        self._translations = {
            # ترجمات صفحة تسجيل الدخول
            'login_title': {
                'ar': 'تسجيل الدخول',
                'en': 'Login'
            },
            'username': {
                'ar': 'اسم المستخدم',
                'en': 'Username'
            },
            'password': {
                'ar': 'كلمة المرور',
                'en': 'Password'
            },
            'login_button': {
                'ar': 'تسجيل الدخول',
                'en': 'Login'
            },
            'create_account': {
                'ar': 'إنشاء حساب جديد',
                'en': 'Create New Account'
            },
            'login_error': {
                'ar': 'خطأ في تسجيل الدخول',
                'en': 'Login Error'
            },
            'invalid_credentials': {
                'ar': 'اسم المستخدم أو كلمة المرور غير صحيحة',
                'en': 'Invalid username or password'
            },
            'remember_me': {
                'ar': 'تذكرني',
                'en': 'Remember Me'
            },
            'forgot_password': {
                'ar': 'نسيت كلمة المرور؟',
                'en': 'Forgot Password?'
            },
            
            # ترجمات صفحة إنشاء حساب
            'register_title': {
                'ar': 'إنشاء حساب جديد',
                'en': 'Create New Account'
            },
            'full_name': {
                'ar': 'الاسم الكامل',
                'en': 'Full Name'
            },
            'email': {
                'ar': 'البريد الإلكتروني',
                'en': 'Email'
            },
            'phone': {
                'ar': 'رقم الهاتف',
                'en': 'Phone Number'
            },
            'confirm_password': {
                'ar': 'تأكيد كلمة المرور',
                'en': 'Confirm Password'
            },
            'register_button': {
                'ar': 'تسجيل',
                'en': 'Register'
            },
            'back_to_login': {
                'ar': 'العودة إلى تسجيل الدخول',
                'en': 'Back to Login'
            },
            
            # ترجمات عامة
            'app_name': {
                'ar': 'نظام المحاسبة المتكامل',
                'en': 'Integrated Accounting System'
            },
            'welcome': {
                'ar': 'مرحبًا',
                'en': 'Welcome'
            },
            'error': {
                'ar': 'خطأ',
                'en': 'Error'
            },
            'success': {
                'ar': 'نجاح',
                'en': 'Success'
            },
            'warning': {
                'ar': 'تحذير',
                'en': 'Warning'
            },
            'info': {
                'ar': 'معلومات',
                'en': 'Information'
            },
            'cancel': {
                'ar': 'إلغاء',
                'en': 'Cancel'
            },
            'save': {
                'ar': 'حفظ',
                'en': 'Save'
            },
            'delete': {
                'ar': 'حذف',
                'en': 'Delete'
            },
            'edit': {
                'ar': 'تعديل',
                'en': 'Edit'
            },
            'add': {
                'ar': 'إضافة',
                'en': 'Add'
            },
            'search': {
                'ar': 'بحث',
                'en': 'Search'
            },
            'close': {
                'ar': 'إغلاق',
                'en': 'Close'
            },
            'logout': {
                'ar': 'تسجيل الخروج',
                'en': 'Logout'
            },
            'settings': {
                'ar': 'الإعدادات',
                'en': 'Settings'
            },
            'language': {
                'ar': 'اللغة',
                'en': 'Language'
            },
            'arabic': {
                'ar': 'العربية',
                'en': 'Arabic'
            },
            'english': {
                'ar': 'الإنجليزية',
                'en': 'English'
            },
            'required_field': {
                'ar': 'هذا الحقل مطلوب',
                'en': 'This field is required'
            },
            'passwords_not_match': {
                'ar': 'كلمات المرور غير متطابقة',
                'en': 'Passwords do not match'
            },
            
            # ترجمات لوحة التحكم
            'dashboard': {
                'ar': 'لوحة التحكم',
                'en': 'Dashboard'
            },
            'products_management': {
                'ar': 'إدارة المنتجات',
                'en': 'Products Management'
            },
            'inventory_management': {
                'ar': 'إدارة المخزون',
                'en': 'Inventory Management'
            },
            'sales_invoices': {
                'ar': 'فواتير البيع',
                'en': 'Sales Invoices'
            },
            'purchase_invoices': {
                'ar': 'فواتير الشراء',
                'en': 'Purchase Invoices'
            },
            'customers_management': {
                'ar': 'إدارة العملاء',
                'en': 'Customers Management'
            },
            'suppliers_management': {
                'ar': 'إدارة الموردين',
                'en': 'Suppliers Management'
            },
            'reports': {
                'ar': 'التقارير',
                'en': 'Reports'
            }
        }
    
    def get_current_language(self):
        """
        الحصول على اللغة الحالية
        :return: رمز اللغة (ar/en)
        """
        return self.settings_manager.get_language()
    
    def translate(self, key):
        """
        ترجمة نص
        :param key: مفتاح النص
        :return: النص المترجم
        """
        language = self.get_current_language()
        
        if key in self._translations:
            return self._translations[key].get(language, key)
        
        # إذا لم يتم العثور على المفتاح، إرجاع المفتاح نفسه
        return key