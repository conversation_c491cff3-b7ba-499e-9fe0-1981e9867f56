#!/usr/bin/env python3
"""
اختبار سريع للتطبيق المحسن
Quick Test for Enhanced Application
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import QApplication, QMessageBox
    from PySide6.QtCore import Qt
    
    # اختبار استيراد الواجهات
    from accounting_app.ui.enhanced_sidebar import EnhancedSidebar
    from accounting_app.ui.enhanced_dashboard import EnhancedDashboard
    from accounting_app.ui.enhanced_products_window import EnhancedProductsWindow
    from accounting_app.ui.enhanced_reports_window import EnhancedReportsWindow
    from accounting_app.ui.enhanced_settings_window import EnhancedSettingsWindow
    from accounting_app.ui.login_window import LoginWindow
    
    print("✅ جميع الواجهات تم استيرادها بنجاح!")
    print("✅ All interfaces imported successfully!")
    
    # اختبار قاعدة البيانات
    from accounting_app.dal.database import DatabaseManager
    
    db = DatabaseManager()
    db.connect()
    print("✅ قاعدة البيانات متصلة بنجاح!")
    print("✅ Database connected successfully!")
    db.close()
    
    # تشغيل التطبيق
    app = QApplication(sys.argv)
    
    # بيانات مستخدم تجريبية
    user_data = (1, "admin", "admin123", "المدير", "<EMAIL>")
    
    # اختبار لوحة التحكم
    dashboard = EnhancedDashboard(user_data)
    dashboard.setWindowTitle("🧪 اختبار لوحة التحكم - Dashboard Test")
    dashboard.show()
    
    print("🚀 التطبيق يعمل بنجاح!")
    print("🚀 Application running successfully!")
    print("📝 بيانات تسجيل الدخول:")
    print("📝 Login credentials:")
    print("   اسم المستخدم / Username: admin")
    print("   كلمة المرور / Password: admin123")
    
    sys.exit(app.exec())
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print(f"❌ Import error: {e}")
    print("💡 تأكد من تثبيت المتطلبات: pip install -r requirements.txt")
    print("💡 Make sure to install requirements: pip install -r requirements.txt")
    
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    print(f"❌ General error: {e}")
