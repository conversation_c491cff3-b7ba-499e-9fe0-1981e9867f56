"""
صفحة التقارير مع الرسوم البيانية
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QComboBox, QDateEdit,
                              QGridLayout, QScrollArea, QTabWidget,
                              QTableWidget, QTableWidgetItem, QHeaderView,
                              QMessageBox, QGroupBox)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QPainter, QColor, QPen, QBrush, QFont
import random

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class ReportsWindow(QWidget):
    """صفحة التقارير"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        try:
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)
            
            # عنوان الصفحة
            header_frame = self.create_header()
            
            # علامات التبويب للتقارير
            tabs_widget = self.create_reports_tabs()
            
            # إضافة العناصر
            main_layout.addWidget(header_frame)
            main_layout.addWidget(tabs_widget)
        except Exception as e:
            print("Error in ReportsWindow.init_ui:", e)
    
    def create_header(self):
        """إنشاء رأس الصفحة"""
        try:
            header_frame = QFrame()
            header_frame.setStyleSheet(f"""
                background: {AppStyles.HEADER_GRADIENT};
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 30px;
                border: 1px solid {AppStyles.BORDER_COLOR};
            """)
            
            header_layout = QHBoxLayout(header_frame)
            
            # عنوان القسم
            title_widget = QWidget()
            title_layout = QVBoxLayout(title_widget)
            
            title_label = QLabel("التقارير والإحصائيات")
            title_label.setStyleSheet(f"""
                color: white;
                font-size: {AppStyles.FONT_SIZE_XXLARGE}px;
                font-weight: bold;
            """)
            
            subtitle_label = QLabel("مراجعة شاملة لأداء الأعمال وتحليل البيانات")
            subtitle_label.setStyleSheet(f"""
                color: white;
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                opacity: 0.9;
                margin-top: 5px;
            """)
            
            title_layout.addWidget(title_label)
            title_layout.addWidget(subtitle_label)
            
            # مرشحات التاريخ
            filters_widget = QWidget()
            filters_layout = QVBoxLayout(filters_widget)
            
            date_filter_label = QLabel("فترة التقرير:")
            date_filter_label.setStyleSheet(f"""
                color: white;
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                font-weight: bold;
                margin-bottom: 10px;
            """)
            
            date_filters_container = QWidget()
            date_filters_layout = QHBoxLayout(date_filters_container)
            
            # تاريخ البداية
            self.start_date = QDateEdit(QDate.currentDate().addDays(-30))
            self.start_date.setStyleSheet(f"""
                QDateEdit {{
                    background-color: white;
                    border: none;
                    border-radius: {AppStyles.BORDER_RADIUS}px;
                    padding: 8px;
                    font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                }}
            """)
            
            # تاريخ النهاية
            self.end_date = QDateEdit(QDate.currentDate())
            self.end_date.setStyleSheet(f"""
                QDateEdit {{
                    background-color: white;
                    border: none;
                    border-radius: {AppStyles.BORDER_RADIUS}px;
                    padding: 8px;
                    font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                }}
            """)
            
            # زر التحديث
            refresh_btn = QPushButton("تحديث التقارير")
            refresh_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: white;
                    color: {AppStyles.PRIMARY_COLOR};
                    border: none;
                    border-radius: {AppStyles.BORDER_RADIUS}px;
                    padding: 10px 20px;
                    font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {AppStyles.BACKGROUND_COLOR};
                }}
            """)
            refresh_btn.clicked.connect(self.refresh_reports)
            
            date_filters_layout.addWidget(QLabel("من:"))
            date_filters_layout.addWidget(self.start_date)
            date_filters_layout.addWidget(QLabel("إلى:"))
            date_filters_layout.addWidget(self.end_date)
            date_filters_layout.addWidget(refresh_btn)
            
            # تطبيق الأنماط على التسميات
            for i in range(date_filters_layout.count()):
                widget = date_filters_layout.itemAt(i).widget()
                if isinstance(widget, QLabel):
                    widget.setStyleSheet("color: white; font-weight: bold;")
            
            filters_layout.addWidget(date_filter_label)
            filters_layout.addWidget(date_filters_container)
            
            header_layout.addWidget(title_widget, 7)
            header_layout.addWidget(filters_widget, 3)
            
            return header_frame
        except Exception as e:
            print("Error in create_header:", e)
            return QFrame()
    
    def create_reports_tabs(self):
        """إنشاء علامات تبويب التقارير"""
        try:
            tabs_widget = QTabWidget()
            tabs_widget.setStyleSheet(AppStyles.TAB_WIDGET_STYLE)
            
            # تبويب المبيعات
            sales_tab = self.create_sales_report_tab()
            tabs_widget.addTab(sales_tab, "تقرير المبيعات")
            
            # تبويب المخزون
            inventory_tab = self.create_inventory_report_tab()
            tabs_widget.addTab(inventory_tab, "تقرير المخزون")
            
            # تبويب الأرباح
            profit_tab = self.create_profit_report_tab()
            tabs_widget.addTab(profit_tab, "تقرير الأرباح")
            
            # تبويب العملاء
            customers_tab = self.create_customers_report_tab()
            tabs_widget.addTab(customers_tab, "تقرير العملاء")
            
            return tabs_widget
        except Exception as e:
            print("Error in create_reports_tabs:", e)
            return QTabWidget()
    
    def create_sales_report_tab(self):
        """إنشاء تبويب تقرير المبيعات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        # إحصائيات سريعة
        stats_frame = QFrame()
        stats_frame.setStyleSheet(f"""
            background-color: white;
            border-radius: {AppStyles.BORDER_RADIUS}px;
            padding: 20px;
            border: 1px solid {AppStyles.BORDER_COLOR};
        """)
        stats_layout = QHBoxLayout(stats_frame)
        
        # إجمالي المبيعات
        total_sales_card = self.create_summary_card("إجمالي المبيعات", "485,750 ج.م", AppStyles.PRIMARY_COLOR, "📈")
        # عدد الفواتير
        invoices_count_card = self.create_summary_card("عدد الفواتير", "156", AppStyles.INFO_COLOR, "📄")
        # متوسط قيمة الفاتورة
        avg_invoice_card = self.create_summary_card("متوسط الفاتورة", "3,114 ج.م", AppStyles.SUCCESS_COLOR, "💰")
        # أفضل عميل
        top_customer_card = self.create_summary_card("أفضل عميل", "أحمد محمد", AppStyles.WARNING_COLOR, "👤")
        
        stats_layout.addWidget(total_sales_card)
        stats_layout.addWidget(invoices_count_card)
        stats_layout.addWidget(avg_invoice_card)
        stats_layout.addWidget(top_customer_card)
        
        # رسم بياني للمبيعات الشهرية
        sales_chart = SalesChartWidget()
        
        # جدول أفضل المنتجات مبيعاً
        top_products_table = self.create_top_products_table()
        
        layout.addWidget(stats_frame)
        layout.addWidget(sales_chart)
        layout.addWidget(top_products_table)
        
        return tab
    
    def create_inventory_report_tab(self):
        """إنشاء تبويب تقرير المخزون"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        # إحصائيات المخزون
        stats_frame = QFrame()
        stats_frame.setStyleSheet(f"""
            background-color: white;
            border-radius: {AppStyles.BORDER_RADIUS}px;
            padding: 20px;
            border: 1px solid {AppStyles.BORDER_COLOR};
        """)
        stats_layout = QHBoxLayout(stats_frame)
        
        # قيمة المخزون
        inventory_value_card = self.create_summary_card("قيمة المخزون", "850,000 ج.م", AppStyles.PRIMARY_COLOR, "📦")
        # عدد المنتجات
        products_count_card = self.create_summary_card("عدد المنتجات", "248", AppStyles.INFO_COLOR, "📋")
        # منخفض المخزون
        low_stock_card = self.create_summary_card("منخفض المخزون", "15", AppStyles.WARNING_COLOR, "⚠️")
        # نفد المخزون
        out_of_stock_card = self.create_summary_card("نفد المخزون", "3", AppStyles.ERROR_COLOR, "❌")
        
        stats_layout.addWidget(inventory_value_card)
        stats_layout.addWidget(products_count_card)
        stats_layout.addWidget(low_stock_card)
        stats_layout.addWidget(out_of_stock_card)
        
        # رسم بياني دائري لتوزيع المخزون
        inventory_chart = InventoryPieChart()
        
        # جدول تحليل حركة المخزون
        movement_table = self.create_inventory_movement_table()
        
        layout.addWidget(stats_frame)
        layout.addWidget(inventory_chart)
        layout.addWidget(movement_table)
        
        return tab
    
    def create_profit_report_tab(self):
        """إنشاء تبويب تقرير الأرباح"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        # إحصائيات الأرباح
        stats_frame = QFrame()
        stats_frame.setStyleSheet(f"""
            background-color: white;
            border-radius: {AppStyles.BORDER_RADIUS}px;
            padding: 20px;
            border: 1px solid {AppStyles.BORDER_COLOR};
        """)
        stats_layout = QHBoxLayout(stats_frame)
        
        # إجمالي الإيرادات
        revenue_card = self.create_summary_card("إجمالي الإيرادات", "485,750 ج.م", AppStyles.PRIMARY_COLOR, "💵")
        # إجمالي التكاليف
        costs_card = self.create_summary_card("إجمالي التكاليف", "324,500 ج.م", AppStyles.ERROR_COLOR, "💸")
        # صافي الربح
        profit_card = self.create_summary_card("صافي الربح", "161,250 ج.م", AppStyles.SUCCESS_COLOR, "💰")
        # هامش الربح
        margin_card = self.create_summary_card("هامش الربح", "33.2%", AppStyles.INFO_COLOR, "📊")
        
        stats_layout.addWidget(revenue_card)
        stats_layout.addWidget(costs_card)
        stats_layout.addWidget(profit_card)
        stats_layout.addWidget(margin_card)
        
        # رسم بياني للأرباح والخسائر
        profit_chart = ProfitLossChart()
        
        layout.addWidget(stats_frame)
        layout.addWidget(profit_chart)
        
        return tab
    
    def create_customers_report_tab(self):
        """إنشاء تبويب تقرير العملاء"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        # إحصائيات العملاء
        stats_frame = QFrame()
        stats_frame.setStyleSheet(f"""
            background-color: white;
            border-radius: {AppStyles.BORDER_RADIUS}px;
            padding: 20px;
            border: 1px solid {AppStyles.BORDER_COLOR};
        """)
        stats_layout = QHBoxLayout(stats_frame)
        
        # إجمالي العملاء
        total_customers_card = self.create_summary_card("إجمالي العملاء", "125", AppStyles.PRIMARY_COLOR, "👥")
        # عملاء جدد
        new_customers_card = self.create_summary_card("عملاء جدد", "18", AppStyles.SUCCESS_COLOR, "🆕")
        # عملاء نشطين
        active_customers_card = self.create_summary_card("عملاء نشطين", "89", AppStyles.INFO_COLOR, "⚡")
        # متوسط قيمة العميل
        avg_customer_value_card = self.create_summary_card("متوسط قيمة العميل", "3,886 ج.م", AppStyles.WARNING_COLOR, "💎")
        
        stats_layout.addWidget(total_customers_card)
        stats_layout.addWidget(new_customers_card)
        stats_layout.addWidget(active_customers_card)
        stats_layout.addWidget(avg_customer_value_card)
        
        # جدول أفضل العملاء
        top_customers_table = self.create_top_customers_table()
        
        layout.addWidget(stats_frame)
        layout.addWidget(top_customers_table)
        
        return tab
    
    def create_summary_card(self, title, value, color, icon):
        """إنشاء بطاقة ملخص"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 {color}, 
                    stop:1 {AppStyles.HIGHLIGHT_COLOR});
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 20px;
                min-height: 100px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            font-size: 32px;
            color: white;
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            color: white;
            font-size: {AppStyles.FONT_SIZE_LARGE}px;
            font-weight: bold;
        """)
        value_label.setAlignment(Qt.AlignCenter)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            color: white;
            font-size: {AppStyles.FONT_SIZE_NORMAL}px;
            opacity: 0.9;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(title_label)
        
        return card
    
    def create_top_products_table(self):
        """إنشاء جدول أفضل المنتجات"""
        frame = QFrame()
        frame.setStyleSheet(f"""
            background-color: white;
            border-radius: {AppStyles.BORDER_RADIUS}px;
            padding: 20px;
            border: 1px solid {AppStyles.BORDER_COLOR};
        """)
        
        layout = QVBoxLayout(frame)
        
        title = QLabel("أفضل المنتجات مبيعاً")
        title.setStyleSheet(f"""
            color: {AppStyles.PRIMARY_COLOR};
            font-size: {AppStyles.FONT_SIZE_LARGE}px;
            font-weight: bold;
            margin-bottom: 15px;
        """)
        
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["المنتج", "الكمية المباعة", "الإيرادات", "الربح"])
        table.setStyleSheet(AppStyles.TABLE_STYLE)
        
        # بيانات وهمية
        data = [
            ["لابتوب HP", "45", "810,000 ج.م", "135,000 ج.م"],
            ["شاشة 24 بوصة", "32", "128,000 ج.م", "32,000 ج.م"],
            ["ماوس لاسلكي", "128", "25,600 ج.م", "6,400 ج.م"],
            ["كيبورد ميكانيكي", "67", "80,400 ج.م", "20,100 ج.م"],
            ["طابعة Canon", "23", "73,600 ج.م", "16,100 ج.م"]
        ]
        
        table.setRowCount(len(data))
        for row, row_data in enumerate(data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(cell_data)
                table.setItem(row, col, item)
        
        table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(title)
        layout.addWidget(table)
        
        return frame
    
    def create_inventory_movement_table(self):
        """إنشاء جدول حركة المخزون"""
        frame = QFrame()
        frame.setStyleSheet(f"""
            background-color: white;
            border-radius: {AppStyles.BORDER_RADIUS}px;
            padding: 20px;
            border: 1px solid {AppStyles.BORDER_COLOR};
        """)
        
        layout = QVBoxLayout(frame)
        
        title = QLabel("حركة المخزون خلال الفترة")
        title.setStyleSheet(f"""
            color: {AppStyles.PRIMARY_COLOR};
            font-size: {AppStyles.FONT_SIZE_LARGE}px;
            font-weight: bold;
            margin-bottom: 15px;
        """)
        
        table = QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(["المنتج", "المخزون الأولي", "الوارد", "الصادر", "المخزون الحالي"])
        table.setStyleSheet(AppStyles.TABLE_STYLE)
        
        # بيانات وهمية
        data = [
            ["لابتوب HP", "60", "20", "45", "35"],
            ["شاشة 24 بوصة", "40", "15", "32", "23"],
            ["ماوس لاسلكي", "200", "50", "128", "122"],
            ["كيبورد ميكانيكي", "80", "30", "67", "43"],
            ["طابعة Canon", "35", "10", "23", "22"]
        ]
        
        table.setRowCount(len(data))
        for row, row_data in enumerate(data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(cell_data)
                table.setItem(row, col, item)
        
        table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(title)
        layout.addWidget(table)
        
        return frame
    
    def create_top_customers_table(self):
        """إنشاء جدول أفضل العملاء"""
        frame = QFrame()
        frame.setStyleSheet(f"""
            background-color: white;
            border-radius: {AppStyles.BORDER_RADIUS}px;
            padding: 20px;
            border: 1px solid {AppStyles.BORDER_COLOR};
        """)
        
        layout = QVBoxLayout(frame)
        
        title = QLabel("أفضل العملاء")
        title.setStyleSheet(f"""
            color: {AppStyles.PRIMARY_COLOR};
            font-size: {AppStyles.FONT_SIZE_LARGE}px;
            font-weight: bold;
            margin-bottom: 15px;
        """)
        
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["العميل", "عدد الفواتير", "إجمالي المشتريات", "آخر شراء"])
        table.setStyleSheet(AppStyles.TABLE_STYLE)
        
        # بيانات وهمية
        data = [
            ["أحمد محمد علي", "23", "95,750 ج.م", "2024-01-20"],
            ["فاطمة أحمد", "18", "76,200 ج.م", "2024-01-19"],
            ["محمد حسن", "15", "62,800 ج.م", "2024-01-18"],
            ["سارة محمود", "12", "48,500 ج.م", "2024-01-17"],
            ["علي عبدالله", "11", "42,300 ج.م", "2024-01-16"]
        ]
        
        table.setRowCount(len(data))
        for row, row_data in enumerate(data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(cell_data)
                table.setItem(row, col, item)
        
        table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(title)
        layout.addWidget(table)
        
        return frame
    
    def refresh_reports(self):
        """تحديث التقارير"""
        try:
            QMessageBox.information(self, "تحديث", "تم تحديث جميع التقارير بنجاح")
        except Exception as e:
            print("Error in refresh_reports:", e)


class SalesChartWidget(QWidget):
    """رسم بياني للمبيعات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumHeight(300)
        self.data = [
            ("يناير", 45000), ("فبراير", 52000), ("مارس", 48000), 
            ("أبريل", 61000), ("مايو", 58000), ("يونيو", 67000)
        ]
    
    def paintEvent(self, event):
        """رسم الرسم البياني"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # خلفية بيضاء
        painter.fillRect(self.rect(), QColor("white"))
        
        # رسم العنوان
        painter.setPen(QColor(AppStyles.TEXT_PRIMARY))
        painter.setFont(QFont("Arial", 14, QFont.Bold))
        painter.drawText(20, 30, "مبيعات آخر 6 أشهر")
        
        if not self.data:
            return
        
        # حساب النطاقات
        margin = 50
        chart_width = self.width() - 2 * margin
        chart_height = self.height() - 2 * margin - 40
        
        max_value = max(item[1] for item in self.data)
        bar_width = chart_width / len(self.data) - 20
        
        # رسم الأعمدة
        for i, (month, value) in enumerate(self.data):
            x = margin + i * (chart_width / len(self.data)) + 10
            bar_height = (value / max_value) * chart_height
            y = self.height() - margin - bar_height
            
            # تدرج لوني للعمود
            painter.setBrush(QBrush(QColor(AppStyles.PRIMARY_COLOR)))
            painter.setPen(QPen(QColor(AppStyles.PRIMARY_COLOR)))
            painter.drawRect(x, y, bar_width, bar_height)
            
            # رسم القيمة فوق العمود
            painter.setPen(QColor(AppStyles.TEXT_PRIMARY))
            painter.setFont(QFont("Arial", 9))
            painter.drawText(x, y - 10, f"{value:,}")
            
            # رسم اسم الشهر
            painter.drawText(x, self.height() - margin + 20, month)


class InventoryPieChart(QWidget):
    """رسم بياني دائري للمخزون"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumHeight(300)
        self.data = [
            ("إلكترونيات", 60, AppStyles.PRIMARY_COLOR),
            ("إكسسوارات", 25, AppStyles.SUCCESS_COLOR),
            ("مكتبية", 15, AppStyles.WARNING_COLOR)
        ]
    
    def paintEvent(self, event):
        """رسم الرسم البياني الدائري"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # خلفية بيضاء
        painter.fillRect(self.rect(), QColor("white"))
        
        # رسم العنوان
        painter.setPen(QColor(AppStyles.TEXT_PRIMARY))
        painter.setFont(QFont("Arial", 14, QFont.Bold))
        painter.drawText(20, 30, "توزيع المخزون حسب الفئة")
        
        # حساب المجموع
        total = sum(item[1] for item in self.data)
        
        # رسم الدائرة
        rect_size = 200
        center_x = self.width() // 2 - rect_size // 2
        center_y = self.height() // 2 - rect_size // 2 + 20
        
        start_angle = 0
        for category, value, color in self.data:
            span_angle = int((value / total) * 360 * 16)
            
            painter.setBrush(QBrush(QColor(color)))
            painter.setPen(QPen(QColor("white"), 2))
            painter.drawPie(center_x, center_y, rect_size, rect_size, start_angle, span_angle)
            
            start_angle += span_angle


class ProfitLossChart(QWidget):
    """رسم بياني للأرباح والخسائر"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumHeight(300)
        self.revenue_data = [45000, 52000, 48000, 61000, 58000, 67000]
        self.cost_data = [30000, 34000, 32000, 40000, 38000, 43000]
    
    def paintEvent(self, event):
        """رسم الرسم البياني"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # خلفية بيضاء
        painter.fillRect(self.rect(), QColor("white"))
        
        # رسم العنوان
        painter.setPen(QColor(AppStyles.TEXT_PRIMARY))
        painter.setFont(QFont("Arial", 14, QFont.Bold))
        painter.drawText(20, 30, "الإيرادات مقابل التكاليف")
        
        margin = 50
        chart_width = self.width() - 2 * margin
        chart_height = self.height() - 2 * margin - 40
        
        max_value = max(max(self.revenue_data), max(self.cost_data))
        
        # رسم خط الإيرادات
        painter.setPen(QPen(QColor(AppStyles.SUCCESS_COLOR), 3))
        revenue_points = []
        for i, value in enumerate(self.revenue_data):
            x = margin + (i / (len(self.revenue_data) - 1)) * chart_width
            y = self.height() - margin - (value / max_value) * chart_height
            revenue_points.append((x, y))
        
        for i in range(len(revenue_points) - 1):
            painter.drawLine(revenue_points[i][0], revenue_points[i][1], 
                           revenue_points[i+1][0], revenue_points[i+1][1])
        
        # رسم خط التكاليف
        painter.setPen(QPen(QColor(AppStyles.ERROR_COLOR), 3))
        cost_points = []
        for i, value in enumerate(self.cost_data):
            x = margin + (i / (len(self.cost_data) - 1)) * chart_width
            y = self.height() - margin - (value / max_value) * chart_height
            cost_points.append((x, y))
        
        for i in range(len(cost_points) - 1):
            painter.drawLine(cost_points[i][0], cost_points[i][1], 
                           cost_points[i+1][0], cost_points[i+1][1])
        
        # رسم النقاط
        painter.setBrush(QBrush(QColor(AppStyles.SUCCESS_COLOR)))
        for x, y in revenue_points:
            painter.drawEllipse(x - 4, y - 4, 8, 8)
        
        painter.setBrush(QBrush(QColor(AppStyles.ERROR_COLOR)))
        for x, y in cost_points:
            painter.drawEllipse(x - 4, y - 4, 8, 8)