"""
صفحة العملاء المحسنة مع إدارة شاملة وتحليلات متقدمة
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QTableWidget, QTableWidgetItem,
                              QHeaderView, QComboBox, QLineEdit, QSpinBox,
                              QDoubleSpinBox, QTextEdit, QGroupBox, QGridLayout,
                              QScrollArea, QSplitter, QTabWidget, QFormLayout,
                              QMessageBox, QDialog, QDialogButtonBox, QCheckBox,
                              QProgressBar, QFileDialog, QMenu, QApplication,
                              QCalendarWidget, QDateEdit, QListWidget, QListWidgetItem,
                              QButtonGroup, QRadioButton)
from PySide6.QtCore import Qt, QThread, Signal, QTimer, QPropertyAnimation, QEasingCurve, QDate
from PySide6.QtGui import QFont, QColor, QPainter, QPen, QBrush, QPixmap, QAction, QCursor
import json
import random
from datetime import datetime, timedelta

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class CustomerFormDialog(QDialog):
    """نافذة إضافة/تعديل العملاء"""
    
    def __init__(self, customer_data=None, parent=None):
        super().__init__(parent)
        self.customer_data = customer_data
        self.is_edit_mode = customer_data is not None
        self.init_ui()
        if self.is_edit_mode:
            self.load_customer_data()
    
    def init_ui(self):
        """إعداد واجهة النافذة"""
        title = "تعديل العميل" if self.is_edit_mode else "إضافة عميل جديد"
        self.setWindowTitle(title)
        self.setFixedSize(600, 500)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان النافذة
        header_label = QLabel(f"👤 {title}")
        header_label.setStyleSheet("""
            color: #007bff;
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            background: #e7f3ff;
            border-radius: 8px;
        """)
        header_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(header_label)
        
        # التبويبات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background: white;
                border-radius: 8px;
            }
            QTabBar::tab {
                background: #f8f9fa;
                padding: 10px 15px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                border: 1px solid #dee2e6;
                color: #495057;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background: white;
                color: #007bff;
                border-bottom: 1px solid white;
            }
            QTabBar::tab:hover {
                background: #e9ecef;
            }
        """)
        
        # تبويب المعلومات الأساسية
        basic_tab = self.create_basic_info_tab()
        self.tabs.addTab(basic_tab, "📝 المعلومات الأساسية")
        
        # تبويب معلومات الاتصال
        contact_tab = self.create_contact_info_tab()
        self.tabs.addTab(contact_tab, "📞 معلومات الاتصال")
        
        layout.addWidget(self.tabs)
        
        # أزرار التحكم
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        buttons_layout = QHBoxLayout(buttons_frame)
        
        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet(self.get_button_style("#28a745"))
        save_btn.clicked.connect(self.save_customer)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet(self.get_button_style("#6c757d"))
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addWidget(buttons_frame)
    
    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        tab_widget = QWidget()
        layout = QFormLayout(tab_widget)
        layout.setSpacing(15)
        
        # اسم العميل
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("اسم العميل")
        self.name_input.setStyleSheet(self.get_input_style())
        layout.addRow("اسم العميل:", self.name_input)
        
        # رقم الهاتف
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("رقم الهاتف")
        self.phone_input.setStyleSheet(self.get_input_style())
        layout.addRow("رقم الهاتف:", self.phone_input)
        
        # البريد الإلكتروني
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("البريد الإلكتروني")
        self.email_input.setStyleSheet(self.get_input_style())
        layout.addRow("البريد الإلكتروني:", self.email_input)
        
        # نوع العميل
        self.type_combo = QComboBox()
        self.type_combo.addItems(["فرد", "شركة", "مؤسسة"])
        self.type_combo.setStyleSheet(self.get_input_style())
        layout.addRow("نوع العميل:", self.type_combo)
        
        return tab_widget
    
    def create_contact_info_tab(self):
        """إنشاء تبويب معلومات الاتصال"""
        tab_widget = QWidget()
        layout = QFormLayout(tab_widget)
        layout.setSpacing(15)
        
        # العنوان
        self.address_input = QTextEdit()
        self.address_input.setPlaceholderText("العنوان التفصيلي...")
        self.address_input.setMaximumHeight(80)
        self.address_input.setStyleSheet(self.get_input_style())
        layout.addRow("العنوان:", self.address_input)
        
        # المدينة
        self.city_input = QLineEdit()
        self.city_input.setPlaceholderText("المدينة")
        self.city_input.setStyleSheet(self.get_input_style())
        layout.addRow("المدينة:", self.city_input)
        
        # الرمز البريدي
        self.postal_code_input = QLineEdit()
        self.postal_code_input.setPlaceholderText("الرمز البريدي")
        self.postal_code_input.setStyleSheet(self.get_input_style())
        layout.addRow("الرمز البريدي:", self.postal_code_input)
        
        return tab_widget
    
    def load_customer_data(self):
        """تحميل بيانات العميل للتعديل"""
        if not self.customer_data:
            return
        
        self.name_input.setText(self.customer_data.get("name", ""))
        self.phone_input.setText(self.customer_data.get("phone", ""))
        self.email_input.setText(self.customer_data.get("email", ""))
        self.address_input.setPlainText(self.customer_data.get("address", ""))
        self.city_input.setText(self.customer_data.get("city", ""))
    
    def save_customer(self):
        """حفظ بيانات العميل"""
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم العميل!")
            return
        
        customer_data = {
            'name': self.name_input.text().strip(),
            'phone': self.phone_input.text().strip(),
            'email': self.email_input.text().strip(),
            'type': self.type_combo.currentText(),
            'address': self.address_input.toPlainText().strip(),
            'city': self.city_input.text().strip(),
            'postal_code': self.postal_code_input.text().strip()
        }
        
        action = "تحديث" if self.is_edit_mode else "إضافة"
        QMessageBox.information(self, "تم الحفظ", f"تم {action} العميل بنجاح!")
        self.accept()
    
    def get_input_style(self):
        return """
            QLineEdit, QComboBox, QTextEdit {
                padding: 8px 12px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 12px;
                background: white;
            }
            QLineEdit:focus, QComboBox:focus, QTextEdit:focus {
                border-color: #007bff;
            }
        """
    
    def get_button_style(self, color):
        return f"""
            QPushButton {{
                background: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 25px;
                font-size: 14px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                opacity: 0.9;
            }}
        """


class CustomersAnalyticsWidget(QWidget):
    """واجهة تحليلات العملاء"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.load_analytics()
    
    def init_ui(self):
        """إعداد واجهة التحليلات"""
        self.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 8px;
                border: 1px solid #dee2e6;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # عنوان التحليلات
        title = QLabel("📊 تحليلات العملاء")
        title.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
            padding: 10px 0;
            border-bottom: 2px solid #e9ecef;
        """)
        layout.addWidget(title)
        
        # الإحصائيات الرئيسية
        stats_frame = QFrame()
        stats_layout = QGridLayout(stats_frame)
        
        # إجمالي العملاء
        total_customers = self.create_stat_card("👥", "1,247", "إجمالي العملاء", "#007bff")
        stats_layout.addWidget(total_customers, 0, 0)
        
        # عملاء جدد هذا الشهر
        new_customers = self.create_stat_card("🆕", "87", "عملاء جدد", "#28a745")
        stats_layout.addWidget(new_customers, 0, 1)
        
        # عملاء نشطون
        active_customers = self.create_stat_card("✅", "956", "عملاء نشطون", "#ffc107")
        stats_layout.addWidget(active_customers, 1, 0)
        
        # متوسط المشتريات
        avg_purchases = self.create_stat_card("💰", "2,450", "متوسط المشتريات", "#17a2b8")
        stats_layout.addWidget(avg_purchases, 1, 1)
        
        layout.addWidget(stats_frame)
        
        # أفضل العملاء
        best_customers_title = QLabel("🏆 أفضل العملاء")
        best_customers_title.setStyleSheet("""
            color: #495057;
            font-size: 14px;
            font-weight: bold;
            margin-top: 10px;
        """)
        layout.addWidget(best_customers_title)
        
        self.best_customers_list = QListWidget()
        self.best_customers_list.setMaximumHeight(150)
        self.best_customers_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #e9ecef;
                border-radius: 4px;
                background: #f8f9fa;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }
            QListWidget::item:hover {
                background: #e3f2fd;
            }
        """)
        layout.addWidget(self.best_customers_list)
        
        layout.addStretch()
    
    def create_stat_card(self, icon, value, label, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: {color};
                border-radius: 8px;
                padding: 15px;
                border: none;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("color: white; font-size: 24px;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 20px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: white; font-size: 12px;")
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(label_widget)
        
        return card
    
    def load_analytics(self):
        """تحميل بيانات التحليلات"""
        best_customers = [
            "أحمد محمد - 45,000 ج.م",
            "سارة أحمد - 38,500 ج.م", 
            "محمد علي - 32,200 ج.م",
            "فاطمة حسن - 28,900 ج.م",
            "عمر خالد - 24,750 ج.م"
        ]
        
        for customer in best_customers:
            item = QListWidgetItem(customer)
            self.best_customers_list.addItem(item)


class CustomersTableWidget(QTableWidget):
    """جدول العملاء المحسن"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_table()
        self.load_customers_data()
    
    def init_table(self):
        """إعداد الجدول"""
        columns = [
            "الكود", "اسم العميل", "رقم الهاتف", "البريد الإلكتروني", 
            "النوع", "المدينة", "إجمالي المشتريات", "الحالة", "الإجراءات"
        ]
        
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 12px;
            }
            QHeaderView::section {
                background: #007bff;
                color: white;
                padding: 12px 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        # إعدادات الجدول
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        self.verticalHeader().setVisible(False)
        
        # تحديد عرض الأعمدة
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)   # الكود
        header.setSectionResizeMode(1, QHeaderView.Stretch) # اسم العميل
        header.setSectionResizeMode(2, QHeaderView.Fixed)   # رقم الهاتف
        header.setSectionResizeMode(3, QHeaderView.Stretch) # البريد الإلكتروني
        header.setSectionResizeMode(4, QHeaderView.Fixed)   # النوع
        header.setSectionResizeMode(5, QHeaderView.Fixed)   # المدينة
        header.setSectionResizeMode(6, QHeaderView.Fixed)   # إجمالي المشتريات
        header.setSectionResizeMode(7, QHeaderView.Fixed)   # الحالة
        header.setSectionResizeMode(8, QHeaderView.Fixed)   # الإجراءات
        
        self.setColumnWidth(0, 80)   # الكود
        self.setColumnWidth(2, 120)  # رقم الهاتف
        self.setColumnWidth(4, 80)   # النوع
        self.setColumnWidth(5, 100)  # المدينة
        self.setColumnWidth(6, 120)  # إجمالي المشتريات
        self.setColumnWidth(7, 80)   # الحالة
        self.setColumnWidth(8, 120)  # الإجراءات
    
    def load_customers_data(self):
        """تحميل بيانات العملاء"""
        customers_data = [
            {"code": "C001", "name": "أحمد محمد علي", "phone": "01012345678", 
             "email": "<EMAIL>", "type": "فرد", "city": "القاهرة", 
             "total": 45000, "status": "نشط"},
            {"code": "C002", "name": "سارة أحمد حسن", "phone": "01023456789", 
             "email": "<EMAIL>", "type": "فرد", "city": "الجيزة", 
             "total": 38500, "status": "نشط"},
            {"code": "C003", "name": "شركة التقنية الحديثة", "phone": "01034567890", 
             "email": "<EMAIL>", "type": "شركة", "city": "الإسكندرية", 
             "total": 125000, "status": "نشط"},
            {"code": "C004", "name": "محمد علي حسين", "phone": "01045678901", 
             "email": "<EMAIL>", "type": "فرد", "city": "أسيوط", 
             "total": 15200, "status": "غير نشط"},
            {"code": "C005", "name": "مؤسسة الأعمال المتطورة", "phone": "01056789012", 
             "email": "<EMAIL>", "type": "مؤسسة", "city": "المنصورة", 
             "total": 89300, "status": "نشط"},
        ]
        
        self.setRowCount(len(customers_data))
        
        for row, customer in enumerate(customers_data):
            # الكود
            self.setItem(row, 0, QTableWidgetItem(customer["code"]))
            
            # اسم العميل
            self.setItem(row, 1, QTableWidgetItem(customer["name"]))
            
            # رقم الهاتف
            phone_item = QTableWidgetItem(customer["phone"])
            phone_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 2, phone_item)
            
            # البريد الإلكتروني
            self.setItem(row, 3, QTableWidgetItem(customer["email"]))
            
            # النوع
            type_item = QTableWidgetItem(customer["type"])
            type_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 4, type_item)
            
            # المدينة
            city_item = QTableWidgetItem(customer["city"])
            city_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 5, city_item)
            
            # إجمالي المشتريات
            total_item = QTableWidgetItem(f"{customer['total']:,.0f} ج.م")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 6, total_item)
            
            # الحالة
            status_colors = {
                "نشط": {"bg": "#d4edda", "fg": "#155724"},
                "غير نشط": {"bg": "#f8d7da", "fg": "#721c24"}
            }
            
            status_item = QTableWidgetItem(customer["status"])
            status_item.setTextAlignment(Qt.AlignCenter)
            color = status_colors.get(customer["status"], status_colors["نشط"])
            status_item.setBackground(QColor(color["bg"]))
            status_item.setForeground(QColor(color["fg"]))
            self.setItem(row, 7, status_item)
            
            # الإجراءات
            actions_widget = self.create_actions_widget(row, customer)
            self.setCellWidget(row, 8, actions_widget)
    
    def create_actions_widget(self, row, customer_data):
        """إنشاء أزرار الإجراءات"""
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(5, 2, 5, 2)
        actions_layout.setSpacing(3)
        
        # زر التعديل
        edit_btn = QPushButton("✏️")
        edit_btn.setFixedSize(22, 22)
        edit_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        edit_btn.setToolTip("تعديل العميل")
        edit_btn.clicked.connect(lambda: self.edit_customer(row, customer_data))
        
        # زر عرض التفاصيل
        view_btn = QPushButton("👁️")
        view_btn.setFixedSize(22, 22)
        view_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        view_btn.setToolTip("عرض التفاصيل")
        view_btn.clicked.connect(lambda: self.view_customer_details(row, customer_data))
        
        # زر الحذف
        delete_btn = QPushButton("🗑️")
        delete_btn.setFixedSize(22, 22)
        delete_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        delete_btn.setToolTip("حذف العميل")
        delete_btn.clicked.connect(lambda: self.delete_customer(row, customer_data))
        
        actions_layout.addWidget(edit_btn)
        actions_layout.addWidget(view_btn)
        actions_layout.addWidget(delete_btn)
        actions_layout.addStretch()
        
        return actions_widget
    
    def edit_customer(self, row, customer_data):
        """تعديل العميل"""
        dialog = CustomerFormDialog(customer_data, self)
        if dialog.exec() == QDialog.Accepted:
            self.load_customers_data()
    
    def view_customer_details(self, row, customer_data):
        """عرض تفاصيل العميل"""
        details_text = f"""
        👤 تفاصيل العميل:
        
        🏷️ الكود: {customer_data['code']}
        📛 الاسم: {customer_data['name']}
        📞 الهاتف: {customer_data['phone']}
        📧 البريد: {customer_data['email']}
        🏢 النوع: {customer_data['type']}
        🏙️ المدينة: {customer_data['city']}
        💰 إجمالي المشتريات: {customer_data['total']:,.0f} ج.م
        ✅ الحالة: {customer_data['status']}
        """
        
        QMessageBox.information(self, "تفاصيل العميل", details_text)
    
    def delete_customer(self, row, customer_data):
        """حذف العميل"""
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل تريد حذف العميل '{customer_data['name']}'؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الحذف", "تم حذف العميل بنجاح!")
            self.load_customers_data()


class EnhancedCustomersWindow(QWidget):
    """نافذة العملاء المحسنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # المحتوى الرئيسي
        content_splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - التحليلات
        left_panel = self.create_left_panel()
        left_panel.setMaximumWidth(300)
        content_splitter.addWidget(left_panel)
        
        # الجانب الأيمن - جدول العملاء
        right_panel = self.create_right_panel()
        content_splitter.addWidget(right_panel)
        
        content_splitter.setSizes([300, 900])
        main_layout.addWidget(content_splitter)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
    
    def create_header(self):
        """إنشاء الرأس"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #007bff, stop:0.5 #6610f2, stop:1 #007bff);
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # معلومات الصفحة
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        title = QLabel("👥 إدارة العملاء")
        title.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
        """)
        
        subtitle = QLabel("إدارة شاملة للعملاء مع تحليلات متقدمة وتتبع المشتريات")
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-top: 5px;
        """)
        
        info_layout.addWidget(title)
        info_layout.addWidget(subtitle)
        
        # الإحصائيات السريعة
        stats_widget = self.create_customers_stats()
        
        layout.addWidget(info_widget, 7)
        layout.addWidget(stats_widget, 3)
        
        return header_frame
    
    def create_customers_stats(self):
        """إنشاء إحصائيات العملاء"""
        stats_widget = QWidget()
        stats_layout = QHBoxLayout(stats_widget)
        stats_layout.setSpacing(10)
        
        # إجمالي العملاء
        total_stat = self.create_stat_card("👥", "1,247", "إجمالي العملاء")
        stats_layout.addWidget(total_stat)
        
        # عملاء جدد
        new_stat = self.create_stat_card("🆕", "87", "عملاء جدد")
        stats_layout.addWidget(new_stat)
        
        # عملاء نشطون
        active_stat = self.create_stat_card("✅", "956", "عملاء نشطون")
        stats_layout.addWidget(active_stat)
        
        return stats_widget
    
    def create_stat_card(self, icon, value, label):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("color: white; font-size: 20px;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 10px;")
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(label_widget)
        
        return card
    
    def create_left_panel(self):
        """إنشاء اللوحة اليسرى"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(15)
        
        # التحليلات
        self.analytics_widget = CustomersAnalyticsWidget()
        left_layout.addWidget(self.analytics_widget)
        
        return left_widget
    
    def create_right_panel(self):
        """إنشاء اللوحة اليمنى"""
        right_frame = QFrame()
        right_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        
        layout = QVBoxLayout(right_frame)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # الجدول
        self.customers_table = CustomersTableWidget()
        layout.addWidget(self.customers_table)
        
        return right_frame
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        
        # زر إضافة عميل جديد
        add_btn = QPushButton("➕ إضافة عميل")
        add_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        add_btn.clicked.connect(self.add_new_customer)
        
        # زر استيراد
        import_btn = QPushButton("📥 استيراد")
        import_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        import_btn.clicked.connect(self.import_customers)
        
        # زر تصدير
        export_btn = QPushButton("📤 تصدير")
        export_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        export_btn.clicked.connect(self.export_customers)
        
        layout.addWidget(add_btn)
        layout.addWidget(import_btn)
        layout.addWidget(export_btn)
        layout.addStretch()
        
        # البحث والفلتر
        filter_combo = QComboBox()
        filter_combo.addItems(["جميع العملاء", "أفراد", "شركات", "مؤسسات"])
        filter_combo.setStyleSheet("""
            QComboBox {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
                font-size: 12px;
            }
        """)
        
        search_input = QLineEdit()
        search_input.setPlaceholderText("البحث في العملاء...")
        search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #007bff;
                border-radius: 20px;
                font-size: 12px;
                background: white;
            }
            QLineEdit:focus {
                border-color: #0056b3;
            }
        """)
        search_input.textChanged.connect(self.search_customers)
        
        layout.addWidget(QLabel("الفلتر:"))
        layout.addWidget(filter_combo)
        layout.addWidget(search_input)
        
        return toolbar_frame
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFixedHeight(40)
        status_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 8px 15px;
            }
        """)
        
        layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز - قاعدة بيانات العملاء محدثة")
        self.status_label.setStyleSheet("color: #007bff; font-weight: bold; font-size: 12px;")
        
        self.customers_count_label = QLabel("5 عملاء")
        self.customers_count_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        
        self.total_value_label = QLabel("إجمالي القيمة: 313,000 ج.م")
        self.total_value_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 12px;")
        
        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.customers_count_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.total_value_label)
        
        return status_frame
    
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_data)
        self.update_timer.start(60000)  # تحديث كل دقيقة
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.analytics_widget.load_analytics()
        self.status_label.setText(f"تم التحديث - {datetime.now().strftime('%H:%M')}")
    
    def add_new_customer(self):
        """إضافة عميل جديد"""
        dialog = CustomerFormDialog(parent=self)
        if dialog.exec() == QDialog.Accepted:
            self.customers_table.load_customers_data()
            self.status_label.setText("تم إضافة عميل جديد")
    
    def import_customers(self):
        """استيراد العملاء"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "استيراد العملاء", "",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;All Files (*)"
        )
        
        if file_path:
            QMessageBox.information(self, "استيراد", f"تم استيراد العملاء من:\n{file_path}")
    
    def export_customers(self):
        """تصدير العملاء"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير العملاء", "customers.xlsx",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;PDF Files (*.pdf)"
        )
        
        if file_path:
            QMessageBox.information(self, "تصدير", f"تم تصدير العملاء إلى:\n{file_path}")
    
    def search_customers(self, search_text):
        """البحث في العملاء"""
        if not search_text.strip():
            self.customers_table.load_customers_data()
            return
        
        # هنا يمكن تطبيق منطق البحث الفعلي
        self.status_label.setText(f"البحث عن: {search_text}")