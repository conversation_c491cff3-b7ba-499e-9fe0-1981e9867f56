"""
نافذة تقارير محسنة مع فلاتر متقدمة وتصميم محسن
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QComboBox, QDateEdit,
                              QGridLayout, QScrollArea, QTabWidget,
                              QTableWidget, QTableWidgetItem, QHeaderView,
                              QMessageBox, QGroupBox, QSplitter, QCheckBox,
                              QLineEdit, QSlider, QSpinBox, QTextEdit,
                              QProgressBar, QButtonGroup, QRadioButton)
from PySide6.QtCore import Qt, QDate, QThread, pyqtSignal, QTimer
from PySide6.QtGui import QPainter, QColor, QPen, QBrush, QFont, QPixmap
import random
from datetime import datetime, timedelta
import json

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class ReportGenerator(QThread):
    """Worker thread لإنشاء التقارير"""
    progressUpdate = pyqtSignal(int, str)
    reportGenerated = pyqtSignal(dict)
    
    def __init__(self, report_type, filters):
        super().__init__()
        self.report_type = report_type
        self.filters = filters
    
    def run(self):
        """إنشاء التقرير"""
        try:
            steps = [
                "جمع البيانات الأساسية...",
                "تطبيق الفلاتر...",
                "حساب الإحصائيات...",
                "إنشاء الرسوم البيانية...",
                "تنسيق النتائج...",
                "اكتمل التقرير!"
            ]
            
            for i, step in enumerate(steps):
                progress = int((i / len(steps)) * 100)
                self.progressUpdate.emit(progress, step)
                self.msleep(300)  # محاكاة وقت المعالجة
            
            # إنشاء بيانات التقرير
            report_data = self.generate_report_data()
            self.reportGenerated.emit(report_data)
            
        except Exception as e:
            print(f"خطأ في إنشاء التقرير: {e}")
    
    def generate_report_data(self):
        """إنشاء بيانات تجريبية للتقرير"""
        if self.report_type == "المبيعات":
            return {
                "summary": {
                    "total_sales": 125000.75,
                    "total_orders": 456,
                    "avg_order": 274.12,
                    "growth": 12.5
                },
                "daily_sales": {
                    f"2024-01-{i:02d}": random.randint(2000, 8000) 
                    for i in range(1, 16)
                },
                "top_products": [
                    {"name": "منتج A", "quantity": 150, "revenue": 15000},
                    {"name": "منتج B", "quantity": 120, "revenue": 12000},
                    {"name": "منتج C", "quantity": 98, "revenue": 9800},
                    {"name": "منتج D", "quantity": 75, "revenue": 7500},
                    {"name": "منتج E", "quantity": 60, "revenue": 6000}
                ],
                "customers": [
                    {"name": "عميل 1", "orders": 25, "total": 15000},
                    {"name": "عميل 2", "orders": 18, "total": 12500},
                    {"name": "عميل 3", "orders": 22, "total": 11800},
                    {"name": "عميل 4", "orders": 15, "total": 9500},
                    {"name": "عميل 5", "orders": 12, "total": 8200}
                ]
            }
        elif self.report_type == "المخزون":
            return {
                "summary": {
                    "total_items": 1250,
                    "low_stock": 23,
                    "out_of_stock": 5,
                    "total_value": 89000.50
                },
                "categories": {
                    "إلكترونيات": 450,
                    "ملابس": 320,
                    "أدوات منزلية": 280,
                    "كتب": 200
                },
                "low_stock_items": [
                    {"name": "منتج X", "current": 2, "minimum": 10},
                    {"name": "منتج Y", "current": 1, "minimum": 5},
                    {"name": "منتج Z", "current": 3, "minimum": 15}
                ]
            }
        return {}


class AdvancedFilterWidget(QWidget):
    """فلتر متقدم للتقارير"""
    filtersChanged = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.filters = {}
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة الفلتر"""
        self.setFixedWidth(300)
        self.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 8px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # عنوان الفلتر
        title = QLabel("🔍 فلاتر متقدمة")
        title.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
            padding: 10px 0;
            border-bottom: 2px solid #e9ecef;
        """)
        layout.addWidget(title)
        
        # فلتر التاريخ
        date_group = self.create_date_filter()
        layout.addWidget(date_group)
        
        # فلتر المبلغ
        amount_group = self.create_amount_filter()
        layout.addWidget(amount_group)
        
        # فلتر الفئات
        category_group = self.create_category_filter()
        layout.addWidget(category_group)
        
        # فلتر الحالة
        status_group = self.create_status_filter()
        layout.addWidget(status_group)
        
        # فلتر البحث
        search_group = self.create_search_filter()
        layout.addWidget(search_group)
        
        # أزرار التحكم
        buttons_widget = self.create_filter_buttons()
        layout.addWidget(buttons_widget)
        
        layout.addStretch()
    
    def create_date_filter(self):
        """فلتر التاريخ"""
        group = QGroupBox("📅 فترة التقرير")
        group.setStyleSheet(self.get_group_style())
        layout = QVBoxLayout(group)
        
        # خيارات سريعة
        quick_dates = QWidget()
        quick_layout = QVBoxLayout(quick_dates)
        
        self.date_options = QButtonGroup()
        date_choices = [
            ("اليوم", "today"),
            ("آخر 7 أيام", "week"),
            ("آخر 30 يوم", "month"),
            ("آخر 3 أشهر", "quarter"),
            ("مخصص", "custom")
        ]
        
        for i, (text, value) in enumerate(date_choices):
            radio = QRadioButton(text)
            radio.setStyleSheet("font-size: 12px; color: #495057; margin: 2px 0;")
            if i == 2:  # آخر 30 يوم افتراضي
                radio.setChecked(True)
            radio.toggled.connect(lambda checked, v=value: self.on_date_option_changed(v) if checked else None)
            self.date_options.addButton(radio, i)
            quick_layout.addWidget(radio)
        
        # تواريخ مخصصة
        custom_dates = QWidget()
        custom_layout = QFormLayout(custom_dates)
        
        self.start_date = QDateEdit(QDate.currentDate().addDays(-30))
        self.start_date.setStyleSheet(self.get_input_style())
        self.start_date.dateChanged.connect(self.emit_filters)
        
        self.end_date = QDateEdit(QDate.currentDate())
        self.end_date.setStyleSheet(self.get_input_style())
        self.end_date.dateChanged.connect(self.emit_filters)
        
        custom_layout.addRow("من:", self.start_date)
        custom_layout.addRow("إلى:", self.end_date)
        
        # إخفاء التواريخ المخصصة في البداية
        custom_dates.setVisible(False)
        self.custom_dates_widget = custom_dates
        
        layout.addWidget(quick_dates)
        layout.addWidget(custom_dates)
        
        return group
    
    def create_amount_filter(self):
        """فلتر المبلغ"""
        group = QGroupBox("💰 نطاق المبلغ")
        group.setStyleSheet(self.get_group_style())
        layout = QVBoxLayout(group)
        
        # تفعيل فلتر المبلغ
        self.enable_amount_filter = QCheckBox("تفعيل فلتر المبلغ")
        self.enable_amount_filter.setStyleSheet("font-size: 12px; color: #495057;")
        self.enable_amount_filter.toggled.connect(self.toggle_amount_filter)
        layout.addWidget(self.enable_amount_filter)
        
        # نطاق المبلغ
        amount_range = QWidget()
        amount_layout = QFormLayout(amount_range)
        
        self.min_amount = QSpinBox()
        self.min_amount.setRange(0, 1000000)
        self.min_amount.setValue(0)
        self.min_amount.setSuffix(" ج.م")
        self.min_amount.setStyleSheet(self.get_input_style())
        self.min_amount.valueChanged.connect(self.emit_filters)
        
        self.max_amount = QSpinBox()
        self.max_amount.setRange(0, 1000000)
        self.max_amount.setValue(100000)
        self.max_amount.setSuffix(" ج.م")
        self.max_amount.setStyleSheet(self.get_input_style())
        self.max_amount.valueChanged.connect(self.emit_filters)
        
        amount_layout.addRow("الحد الأدنى:", self.min_amount)
        amount_layout.addRow("الحد الأقصى:", self.max_amount)
        
        amount_range.setVisible(False)
        self.amount_range_widget = amount_range
        
        layout.addWidget(amount_range)
        return group
    
    def create_category_filter(self):
        """فلتر الفئات"""
        group = QGroupBox("📂 الفئات")
        group.setStyleSheet(self.get_group_style())
        layout = QVBoxLayout(group)
        
        categories = [
            "إلكترونيات",
            "ملابس",
            "أدوات منزلية",
            "كتب ومجلات",
            "رياضة",
            "جمال وعناية"
        ]
        
        self.category_checkboxes = []
        for category in categories:
            checkbox = QCheckBox(category)
            checkbox.setStyleSheet("font-size: 12px; color: #495057; margin: 2px 0;")
            checkbox.setChecked(True)  # كل الفئات مفعلة افتراضياً
            checkbox.toggled.connect(self.emit_filters)
            self.category_checkboxes.append(checkbox)
            layout.addWidget(checkbox)
        
        # أزرار سريعة
        category_buttons = QWidget()
        category_buttons_layout = QHBoxLayout(category_buttons)
        
        select_all_btn = QPushButton("الكل")
        select_all_btn.setStyleSheet(self.get_small_button_style())
        select_all_btn.clicked.connect(self.select_all_categories)
        
        select_none_btn = QPushButton("لا شيء")
        select_none_btn.setStyleSheet(self.get_small_button_style())
        select_none_btn.clicked.connect(self.select_no_categories)
        
        category_buttons_layout.addWidget(select_all_btn)
        category_buttons_layout.addWidget(select_none_btn)
        
        layout.addWidget(category_buttons)
        return group
    
    def create_status_filter(self):
        """فلتر الحالة"""
        group = QGroupBox("📊 الحالة")
        group.setStyleSheet(self.get_group_style())
        layout = QVBoxLayout(group)
        
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "جميع الحالات",
            "مكتمل",
            "معلق",
            "ملغي",
            "قيد المراجعة"
        ])
        self.status_combo.setStyleSheet(self.get_combo_style())
        self.status_combo.currentTextChanged.connect(self.emit_filters)
        
        layout.addWidget(self.status_combo)
        return group
    
    def create_search_filter(self):
        """فلتر البحث"""
        group = QGroupBox("🔍 البحث النصي")
        group.setStyleSheet(self.get_group_style())
        layout = QVBoxLayout(group)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث في النتائج...")
        self.search_input.setStyleSheet(self.get_input_style())
        self.search_input.textChanged.connect(self.emit_filters)
        
        layout.addWidget(self.search_input)
        return group
    
    def create_filter_buttons(self):
        """أزرار التحكم في الفلاتر"""
        buttons_widget = QWidget()
        layout = QVBoxLayout(buttons_widget)
        layout.setSpacing(8)
        
        # زر تطبيق الفلاتر
        apply_btn = QPushButton("✅ تطبيق الفلاتر")
        apply_btn.setStyleSheet(self.get_button_style("#28a745"))
        apply_btn.clicked.connect(self.apply_filters)
        
        # زر إعادة تعيين
        reset_btn = QPushButton("🔄 إعادة تعيين")
        reset_btn.setStyleSheet(self.get_button_style("#6c757d"))
        reset_btn.clicked.connect(self.reset_filters)
        
        # زر حفظ الفلتر
        save_btn = QPushButton("💾 حفظ كقالب")
        save_btn.setStyleSheet(self.get_button_style("#17a2b8"))
        save_btn.clicked.connect(self.save_filter_template)
        
        layout.addWidget(apply_btn)
        layout.addWidget(reset_btn)
        layout.addWidget(save_btn)
        
        return buttons_widget
    
    def on_date_option_changed(self, option):
        """تغيير خيار التاريخ"""
        if option == "custom":
            self.custom_dates_widget.setVisible(True)
        else:
            self.custom_dates_widget.setVisible(False)
            
            today = QDate.currentDate()
            if option == "today":
                self.start_date.setDate(today)
                self.end_date.setDate(today)
            elif option == "week":
                self.start_date.setDate(today.addDays(-7))
                self.end_date.setDate(today)
            elif option == "month":
                self.start_date.setDate(today.addDays(-30))
                self.end_date.setDate(today)
            elif option == "quarter":
                self.start_date.setDate(today.addDays(-90))
                self.end_date.setDate(today)
        
        self.emit_filters()
    
    def toggle_amount_filter(self, enabled):
        """تفعيل/إلغاء فلتر المبلغ"""
        self.amount_range_widget.setVisible(enabled)
        self.emit_filters()
    
    def select_all_categories(self):
        """تحديد جميع الفئات"""
        for checkbox in self.category_checkboxes:
            checkbox.setChecked(True)
    
    def select_no_categories(self):
        """إلغاء تحديد جميع الفئات"""
        for checkbox in self.category_checkboxes:
            checkbox.setChecked(False)
    
    def emit_filters(self):
        """إرسال الفلاتر المحدثة"""
        self.filters = {
            "date_range": {
                "start": self.start_date.date().toString("yyyy-MM-dd"),
                "end": self.end_date.date().toString("yyyy-MM-dd")
            },
            "amount_filter_enabled": self.enable_amount_filter.isChecked(),
            "amount_range": {
                "min": self.min_amount.value(),
                "max": self.max_amount.value()
            },
            "categories": [cb.text() for cb in self.category_checkboxes if cb.isChecked()],
            "status": self.status_combo.currentText(),
            "search_text": self.search_input.text()
        }
        self.filtersChanged.emit(self.filters)
    
    def apply_filters(self):
        """تطبيق الفلاتر"""
        self.emit_filters()
        QMessageBox.information(self, "تطبيق الفلاتر", "تم تطبيق الفلاتر بنجاح!")
    
    def reset_filters(self):
        """إعادة تعيين الفلاتر"""
        # إعادة تعيين التاريخ
        self.date_options.button(2).setChecked(True)  # آخر 30 يوم
        
        # إعادة تعيين المبلغ
        self.enable_amount_filter.setChecked(False)
        self.min_amount.setValue(0)
        self.max_amount.setValue(100000)
        
        # إعادة تعيين الفئات
        for checkbox in self.category_checkboxes:
            checkbox.setChecked(True)
        
        # إعادة تعيين الحالة
        self.status_combo.setCurrentIndex(0)
        
        # إعادة تعيين البحث
        self.search_input.clear()
        
        self.emit_filters()
    
    def save_filter_template(self):
        """حفظ قالب الفلتر"""
        QMessageBox.information(self, "حفظ القالب", "تم حفظ قالب الفلتر بنجاح!")
    
    # دوال الأنماط
    def get_group_style(self):
        return """
            QGroupBox {
                font-size: 13px;
                font-weight: bold;
                color: #495057;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                margin-top: 8px;
                padding-top: 8px;
                background: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 5px;
                background: white;
            }
        """
    
    def get_input_style(self):
        return """
            QLineEdit, QDateEdit, QSpinBox {
                padding: 6px 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 12px;
                background: white;
            }
            QLineEdit:focus, QDateEdit:focus, QSpinBox:focus {
                border-color: #007bff;
            }
        """
    
    def get_combo_style(self):
        return """
            QComboBox {
                padding: 6px 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 12px;
                background: white;
            }
            QComboBox:focus {
                border-color: #007bff;
            }
        """
    
    def get_button_style(self, color):
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 12px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                opacity: 0.9;
            }}
        """
    
    def get_small_button_style(self):
        return """
            QPushButton {
                background-color: #e9ecef;
                color: #495057;
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 11px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #dee2e6;
            }
        """


class ReportChart(QWidget):
    """رسم بياني للتقارير"""
    
    def __init__(self, title, data, chart_type="line", parent=None):
        super().__init__(parent)
        self.title = title
        self.data = data
        self.chart_type = chart_type
        self.setMinimumHeight(250)
        self.setStyleSheet("""
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        """)
    
    def paintEvent(self, event):
        """رسم الرسم البياني"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # خلفية
        painter.fillRect(self.rect(), QColor("#ffffff"))
        
        # العنوان
        painter.setPen(QPen(QColor("#495057"), 2))
        painter.setFont(QFont("Arial", 14, QFont.Bold))
        title_rect = painter.boundingRect(20, 20, self.width() - 40, 30, Qt.AlignLeft, self.title)
        painter.drawText(title_rect, Qt.AlignLeft, self.title)
        
        if not self.data:
            painter.drawText(self.width()//2 - 60, self.height()//2, "لا توجد بيانات للعرض")
            return
        
        # إعدادات الرسم
        margin = 50
        chart_width = self.width() - 2 * margin
        chart_height = self.height() - 2 * margin - 60
        chart_top = margin + 50
        
        if self.chart_type == "line":
            self.draw_line_chart(painter, margin, chart_top, chart_width, chart_height)
        elif self.chart_type == "bar":
            self.draw_bar_chart(painter, margin, chart_top, chart_width, chart_height)
        elif self.chart_type == "pie":
            self.draw_pie_chart(painter, margin, chart_top, chart_width, chart_height)
    
    def draw_line_chart(self, painter, margin, chart_top, width, height):
        """رسم خط بياني"""
        if len(self.data) < 2:
            return
        
        # تحديد القيم
        values = list(self.data.values())
        max_val = max(values) if values else 1
        min_val = min(values) if values else 0
        
        # رسم المحاور
        painter.setPen(QPen(QColor("#dee2e6"), 1))
        # المحور Y
        painter.drawLine(margin, chart_top, margin, chart_top + height)
        # المحور X
        painter.drawLine(margin, chart_top + height, margin + width, chart_top + height)
        
        # رسم خطوط الشبكة
        for i in range(5):
            y = chart_top + (height * i / 4)
            painter.drawLine(margin, y, margin + width, y)
            
            # قيم المحور Y
            value = max_val - (max_val - min_val) * i / 4
            painter.drawText(5, y + 5, f"{value:.0f}")
        
        # تحديد النقاط
        points = []
        step_x = width / (len(self.data) - 1) if len(self.data) > 1 else width
        
        for i, value in enumerate(values):
            x = margin + i * step_x
            y = chart_top + height - ((value - min_val) / (max_val - min_val) * height) if max_val > min_val else chart_top + height/2
            points.append((x, y))
        
        # رسم الخط
        painter.setPen(QPen(QColor("#007bff"), 3))
        for i in range(len(points) - 1):
            painter.drawLine(points[i][0], points[i][1], points[i+1][0], points[i+1][1])
        
        # رسم النقاط
        painter.setBrush(QBrush(QColor("#007bff")))
        for x, y in points:
            painter.drawEllipse(x - 4, y - 4, 8, 8)
        
        # تسميات المحور X
        painter.setPen(QPen(QColor("#495057"), 1))
        for i, (key, _) in enumerate(self.data.items()):
            x = margin + i * step_x
            painter.drawText(x - 20, chart_top + height + 20, str(key)[-5:])  # آخر 5 أحرف
    
    def draw_bar_chart(self, painter, margin, chart_top, width, height):
        """رسم أعمدة بيانية"""
        if not self.data:
            return
        
        values = list(self.data.values())
        max_val = max(values) if values else 1
        bar_width = width / len(self.data) - 10
        
        colors = ["#007bff", "#28a745", "#ffc107", "#dc3545", "#6f42c1", "#17a2b8"]
        
        # رسم المحاور
        painter.setPen(QPen(QColor("#dee2e6"), 1))
        painter.drawLine(margin, chart_top, margin, chart_top + height)
        painter.drawLine(margin, chart_top + height, margin + width, chart_top + height)
        
        for i, ((key, value), color) in enumerate(zip(self.data.items(), colors * 10)):
            x = margin + i * (bar_width + 10)
            bar_height = (value / max_val) * height
            y = chart_top + height - bar_height
            
            # رسم العمود
            painter.fillRect(x, y, bar_width, bar_height, QColor(color))
            
            # رسم القيمة
            painter.setPen(QPen(QColor("#495057"), 1))
            painter.drawText(x, y - 5, f"{value:.0f}")
            
            # تسمية المحور X
            painter.drawText(x, chart_top + height + 15, str(key)[:8])


class EnhancedReportsWindow(QWidget):
    """نافذة تقارير محسنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.current_report_data = {}
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📊 التقارير المتقدمة")
        self.setMinimumSize(1200, 800)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # الرأس
        header = self.create_enhanced_header()
        main_layout.addWidget(header)
        
        # المحتوى الرئيسي
        content_splitter = QSplitter(Qt.Horizontal)
        
        # الفلاتر الجانبية
        self.filter_widget = AdvancedFilterWidget()
        self.filter_widget.filtersChanged.connect(self.on_filters_changed)
        content_splitter.addWidget(self.filter_widget)
        
        # منطقة التقارير
        reports_area = self.create_reports_area()
        content_splitter.addWidget(reports_area)
        
        content_splitter.setSizes([300, 900])
        main_layout.addWidget(content_splitter)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
    
    def create_enhanced_header(self):
        """إنشاء رأس محسن"""
        header_frame = QFrame()
        header_frame.setFixedHeight(100)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #667eea);
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # معلومات التقرير
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        title = QLabel("📊 مركز التقارير المتقدم")
        title.setStyleSheet("""
            color: white;
            font-size: 22px;
            font-weight: bold;
        """)
        
        subtitle = QLabel("تقارير تفاعلية مع فلاتر متقدمة وتصدير شامل")
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-top: 5px;
        """)
        
        info_layout.addWidget(title)
        info_layout.addWidget(subtitle)
        
        # أزرار التحكم
        controls_widget = QWidget()
        controls_layout = QHBoxLayout(controls_widget)
        controls_layout.setSpacing(10)
        
        # اختيار نوع التقرير
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            "📈 تقرير المبيعات",
            "📦 تقرير المخزون", 
            "👥 تقرير العملاء",
            "💰 التقرير المالي",
            "🏢 تقرير الموردين",
            "🧾 تقرير الفواتير"
        ])
        self.report_type_combo.setStyleSheet("""
            QComboBox {
                background: white;
                color: #495057;
                border: none;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: bold;
                min-width: 180px;
            }
        """)
        self.report_type_combo.currentTextChanged.connect(self.on_report_type_changed)
        
        # زر إنشاء التقرير
        generate_btn = QPushButton("🚀 إنشاء التقرير")
        generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 13px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        generate_btn.clicked.connect(self.generate_report)
        
        # زر التصدير
        export_btn = QPushButton("📤 تصدير")
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 13px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        export_btn.clicked.connect(self.export_report)
        
        controls_layout.addWidget(QLabel("نوع التقرير:"))
        controls_layout.addWidget(self.report_type_combo)
        controls_layout.addWidget(generate_btn)
        controls_layout.addWidget(export_btn)
        
        # تطبيق الأنماط على التسميات
        for i in range(controls_layout.count()):
            widget = controls_layout.itemAt(i).widget()
            if isinstance(widget, QLabel):
                widget.setStyleSheet("color: white; font-weight: bold; font-size: 13px;")
        
        layout.addWidget(info_widget, 6)
        layout.addWidget(controls_widget, 4)
        
        return header_frame
    
    def create_reports_area(self):
        """إنشاء منطقة التقارير"""
        reports_frame = QFrame()
        reports_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(reports_frame)
        layout.setSpacing(15)
        
        # شريط التحميل
        self.loading_widget = self.create_loading_widget()
        layout.addWidget(self.loading_widget)
        self.loading_widget.setVisible(False)
        
        # منطقة محتوى التقرير
        content_scroll = QScrollArea()
        content_scroll.setWidgetResizable(True)
        content_scroll.setStyleSheet("border: none; background: transparent;")
        
        self.report_content = QWidget()
        self.report_layout = QVBoxLayout(self.report_content)
        self.report_layout.setSpacing(20)
        
        # عرض الرسالة الافتراضية
        self.show_welcome_message()
        
        content_scroll.setWidget(self.report_content)
        layout.addWidget(content_scroll)
        
        return reports_frame
    
    def create_loading_widget(self):
        """إنشاء واجهة التحميل"""
        loading_frame = QFrame()
        loading_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #dee2e6;
                padding: 40px;
            }
        """)
        loading_frame.setFixedHeight(200)
        
        layout = QVBoxLayout(loading_frame)
        layout.setAlignment(Qt.AlignCenter)
        
        # أيقونة التحميل
        loading_icon = QLabel("⏳")
        loading_icon.setStyleSheet("font-size: 48px;")
        loading_icon.setAlignment(Qt.AlignCenter)
        
        # رسالة التحميل
        self.loading_message = QLabel("جاري إنشاء التقرير...")
        self.loading_message.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
            margin: 15px 0;
        """)
        self.loading_message.setAlignment(Qt.AlignCenter)
        
        # شريط التقدم
        self.report_progress = QProgressBar()
        self.report_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                color: #495057;
                background: #f8f9fa;
            }
            QProgressBar::chunk {
                background-color: #007bff;
                border-radius: 6px;
            }
        """)
        self.report_progress.setFixedHeight(20)
        
        layout.addWidget(loading_icon)
        layout.addWidget(self.loading_message)
        layout.addWidget(self.report_progress)
        
        return loading_frame
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFixedHeight(40)
        status_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-top: 1px solid #dee2e6;
                padding: 8px 20px;
            }
        """)
        
        layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز لإنشاء التقارير")
        self.status_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 12px;")
        
        self.records_count_label = QLabel("0 سجل")
        self.records_count_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        
        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.records_count_label)
        
        return status_frame
    
    def show_welcome_message(self):
        """عرض رسالة الترحيب"""
        # مسح المحتوى السابق
        for i in reversed(range(self.report_layout.count())):
            self.report_layout.itemAt(i).widget().setParent(None)
        
        welcome_frame = QFrame()
        welcome_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 12px;
                border: 2px dashed #dee2e6;
                padding: 60px;
            }
        """)
        
        layout = QVBoxLayout(welcome_frame)
        layout.setAlignment(Qt.AlignCenter)
        
        icon = QLabel("📊")
        icon.setStyleSheet("font-size: 80px;")
        icon.setAlignment(Qt.AlignCenter)
        
        title = QLabel("مرحباً بك في مركز التقارير")
        title.setStyleSheet("""
            color: #495057;
            font-size: 24px;
            font-weight: bold;
            margin: 20px 0 10px 0;
        """)
        title.setAlignment(Qt.AlignCenter)
        
        description = QLabel("""
        اختر نوع التقرير من القائمة العلوية وطبق الفلاتر المناسبة
        ثم اضغط على "إنشاء التقرير" للحصول على تحليل مفصل
        """)
        description.setStyleSheet("""
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.5;
        """)
        description.setAlignment(Qt.AlignCenter)
        description.setWordWrap(True)
        
        # أزرار سريعة
        quick_buttons = QWidget()
        quick_layout = QHBoxLayout(quick_buttons)
        quick_layout.setAlignment(Qt.AlignCenter)
        
        quick_reports = [
            ("المبيعات اليومية", "#28a745"),
            ("حالة المخزون", "#007bff"),
            ("أداء الفريق", "#ffc107")
        ]
        
        for text, color in quick_reports:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 20px;
                    font-size: 13px;
                    font-weight: bold;
                    margin: 0 5px;
                }}
                QPushButton:hover {{
                    opacity: 0.9;
                }}
            """)
            quick_layout.addWidget(btn)
        
        layout.addWidget(icon)
        layout.addWidget(title)
        layout.addWidget(description)
        layout.addWidget(quick_buttons)
        
        self.report_layout.addWidget(welcome_frame)
    
    def on_report_type_changed(self, report_type):
        """تغيير نوع التقرير"""
        self.status_label.setText(f"نوع التقرير: {report_type}")
    
    def on_filters_changed(self, filters):
        """تغيير الفلاتر"""
        active_filters = []
        if filters.get("amount_filter_enabled"):
            active_filters.append("المبلغ")
        if len(filters.get("categories", [])) < 6:  # ليس كل الفئات
            active_filters.append("الفئات")
        if filters.get("status") != "جميع الحالات":
            active_filters.append("الحالة")
        if filters.get("search_text"):
            active_filters.append("البحث")
        
        if active_filters:
            self.status_label.setText(f"فلاتر مطبقة: {', '.join(active_filters)}")
        else:
            self.status_label.setText("جاهز لإنشاء التقارير")
    
    def generate_report(self):
        """إنشاء التقرير"""
        report_type = self.report_type_combo.currentText().split(" ", 1)[1]  # إزالة الأيقونة
        filters = self.filter_widget.filters
        
        # إظهار شاشة التحميل
        self.loading_widget.setVisible(True)
        self.report_content.setVisible(False)
        
        # بدء إنشاء التقرير
        self.report_generator = ReportGenerator(report_type, filters)
        self.report_generator.progressUpdate.connect(self.on_report_progress)
        self.report_generator.reportGenerated.connect(self.on_report_generated)
        self.report_generator.start()
    
    def on_report_progress(self, progress, message):
        """تحديث تقدم إنشاء التقرير"""
        self.report_progress.setValue(progress)
        self.loading_message.setText(message)
    
    def on_report_generated(self, report_data):
        """عرض التقرير المنشأ"""
        self.current_report_data = report_data
        
        # إخفاء شاشة التحميل
        self.loading_widget.setVisible(False)
        self.report_content.setVisible(True)
        
        # إنشاء محتوى التقرير
        self.display_report(report_data)
        
        # تحديث شريط الحالة
        total_records = len(report_data.get("top_products", [])) + len(report_data.get("customers", []))
        self.records_count_label.setText(f"{total_records} سجل")
        self.status_label.setText("تم إنشاء التقرير بنجاح")
    
    def display_report(self, report_data):
        """عرض محتوى التقرير"""
        # مسح المحتوى السابق
        for i in reversed(range(self.report_layout.count())):
            self.report_layout.itemAt(i).widget().setParent(None)
        
        # عنوان التقرير
        report_title = self.report_type_combo.currentText()
        title_label = QLabel(f"📊 {report_title}")
        title_label.setStyleSheet("""
            color: #495057;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        """)
        self.report_layout.addWidget(title_label)
        
        # الملخص التنفيذي
        if "summary" in report_data:
            summary_widget = self.create_summary_widget(report_data["summary"])
            self.report_layout.addWidget(summary_widget)
        
        # الرسوم البيانية
        charts_widget = self.create_charts_widget(report_data)
        self.report_layout.addWidget(charts_widget)
        
        # الجداول التفصيلية
        tables_widget = self.create_tables_widget(report_data)
        self.report_layout.addWidget(tables_widget)
    
    def create_summary_widget(self, summary_data):
        """إنشاء ملخص التقرير"""
        summary_frame = QFrame()
        summary_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #dee2e6;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(summary_frame)
        
        title = QLabel("📋 الملخص التنفيذي")
        title.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
        """)
        layout.addWidget(title)
        
        # بطاقات الملخص
        summary_grid = QWidget()
        grid_layout = QGridLayout(summary_grid)
        grid_layout.setSpacing(15)
        
        # تحويل بيانات الملخص إلى بطاقات
        row, col = 0, 0
        colors = ["#007bff", "#28a745", "#ffc107", "#dc3545"]
        
        for i, (key, value) in enumerate(summary_data.items()):
            card = self.create_summary_card(key, value, colors[i % len(colors)])
            grid_layout.addWidget(card, row, col)
            
            col += 1
            if col >= 4:
                col = 0
                row += 1
        
        layout.addWidget(summary_grid)
        return summary_frame
    
    def create_summary_card(self, key, value, color):
        """إنشاء بطاقة ملخص"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}, stop:1 {self.darken_color(color)});
                border-radius: 8px;
                padding: 15px;
                min-height: 80px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        
        # القيمة
        value_label = QLabel(str(value))
        value_label.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        """)
        value_label.setAlignment(Qt.AlignCenter)
        
        # المفتاح
        key_label = QLabel(self.translate_key(key))
        key_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 12px;
            font-weight: bold;
        """)
        key_label.setAlignment(Qt.AlignCenter)
        key_label.setWordWrap(True)
        
        layout.addWidget(value_label)
        layout.addWidget(key_label)
        
        return card
    
    def create_charts_widget(self, report_data):
        """إنشاء واجهة الرسوم البيانية"""
        charts_frame = QFrame()
        charts_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #dee2e6;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(charts_frame)
        
        title = QLabel("📈 الرسوم البيانية")
        title.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
        """)
        layout.addWidget(title)
        
        # حاوية الرسوم البيانية
        charts_container = QWidget()
        charts_layout = QHBoxLayout(charts_container)
        charts_layout.setSpacing(15)
        
        # رسم بياني للبيانات اليومية
        if "daily_sales" in report_data:
            daily_chart = ReportChart(
                "المبيعات اليومية",
                report_data["daily_sales"],
                "line"
            )
            charts_layout.addWidget(daily_chart)
        
        # رسم بياني للفئات
        if "categories" in report_data:
            categories_chart = ReportChart(
                "توزيع الفئات",
                report_data["categories"],
                "bar"
            )
            charts_layout.addWidget(categories_chart)
        
        layout.addWidget(charts_container)
        return charts_frame
    
    def create_tables_widget(self, report_data):
        """إنشاء الجداول التفصيلية"""
        tables_frame = QFrame()
        tables_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #dee2e6;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(tables_frame)
        
        title = QLabel("📋 البيانات التفصيلية")
        title.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
        """)
        layout.addWidget(title)
        
        # تبويبات للجداول المختلفة
        tables_tabs = QTabWidget()
        tables_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background: white;
            }
            QTabBar::tab {
                background: #f8f9fa;
                color: #495057;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                border: 1px solid #dee2e6;
            }
            QTabBar::tab:selected {
                background: white;
                color: #007bff;
                font-weight: bold;
                border-bottom: none;
            }
        """)
        
        # جدول المنتجات الأكثر مبيعاً
        if "top_products" in report_data:
            products_table = self.create_products_table(report_data["top_products"])
            tables_tabs.addTab(products_table, "أفضل المنتجات")
        
        # جدول العملاء
        if "customers" in report_data:
            customers_table = self.create_customers_table(report_data["customers"])
            tables_tabs.addTab(customers_table, "العملاء")
        
        # جدول المخزون المنخفض
        if "low_stock_items" in report_data:
            low_stock_table = self.create_low_stock_table(report_data["low_stock_items"])
            tables_tabs.addTab(low_stock_table, "مخزون منخفض")
        
        layout.addWidget(tables_tabs)
        return tables_frame
    
    def create_products_table(self, products_data):
        """إنشاء جدول المنتجات"""
        table = QTableWidget(len(products_data), 3)
        table.setHorizontalHeaderLabels(["اسم المنتج", "الكمية المباعة", "الإيرادات"])
        
        for row, product in enumerate(products_data):
            table.setItem(row, 0, QTableWidgetItem(product["name"]))
            table.setItem(row, 1, QTableWidgetItem(str(product["quantity"])))
            table.setItem(row, 2, QTableWidgetItem(f"{product['revenue']:,.2f} ج.م"))
        
        self.style_table(table)
        return table
    
    def create_customers_table(self, customers_data):
        """إنشاء جدول العملاء"""
        table = QTableWidget(len(customers_data), 3)
        table.setHorizontalHeaderLabels(["اسم العميل", "عدد الطلبات", "إجمالي المشتريات"])
        
        for row, customer in enumerate(customers_data):
            table.setItem(row, 0, QTableWidgetItem(customer["name"]))
            table.setItem(row, 1, QTableWidgetItem(str(customer["orders"])))
            table.setItem(row, 2, QTableWidgetItem(f"{customer['total']:,.2f} ج.م"))
        
        self.style_table(table)
        return table
    
    def create_low_stock_table(self, low_stock_data):
        """إنشاء جدول المخزون المنخفض"""
        table = QTableWidget(len(low_stock_data), 3)
        table.setHorizontalHeaderLabels(["اسم المنتج", "المخزون الحالي", "الحد الأدنى"])
        
        for row, item in enumerate(low_stock_data):
            table.setItem(row, 0, QTableWidgetItem(item["name"]))
            
            current_item = QTableWidgetItem(str(item["current"]))
            current_item.setBackground(QColor("#ffebee"))  # خلفية حمراء فاتحة
            table.setItem(row, 1, current_item)
            
            table.setItem(row, 2, QTableWidgetItem(str(item["minimum"])))
        
        self.style_table(table)
        return table
    
    def style_table(self, table):
        """تنسيق الجدول"""
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: none;
                font-size: 12px;
            }
            QHeaderView::section {
                background: #f8f9fa;
                color: #495057;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
        """)
        
        table.horizontalHeader().setStretchLastSection(True)
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setMaximumHeight(300)
    
    def export_report(self):
        """تصدير التقرير"""
        if not self.current_report_data:
            QMessageBox.warning(self, "تحذير", "لا يوجد تقرير لتصديره! يرجى إنشاء تقرير أولاً.")
            return
        
        # قائمة خيارات التصدير
        export_options = [
            "📄 PDF",
            "📊 Excel",
            "📋 CSV",
            "🖼️ صورة",
            "📧 إرسال بالبريد"
        ]
        
        from PySide6.QtWidgets import QInputDialog
        export_type, ok = QInputDialog.getItem(
            self, "تصدير التقرير", "اختر تنسيق التصدير:", export_options, 0, False
        )
        
        if ok and export_type:
            QMessageBox.information(
                self, "تصدير التقرير", 
                f"تم تصدير التقرير بتنسيق {export_type} بنجاح!\n\nسيتم حفظ الملف في مجلد التقارير."
            )
    
    def translate_key(self, key):
        """ترجمة مفاتيح البيانات"""
        translations = {
            "total_sales": "إجمالي المبيعات",
            "total_orders": "عدد الطلبات",
            "avg_order": "متوسط الطلب",
            "growth": "معدل النمو %",
            "total_items": "إجمالي العناصر",
            "low_stock": "مخزون منخفض",
            "out_of_stock": "نفاد المخزون",
            "total_value": "القيمة الإجمالية"
        }
        return translations.get(key, key)
    
    def darken_color(self, color, factor=0.2):
        """تظليل اللون"""
        try:
            color_obj = QColor(color)
            h, s, l, a = color_obj.getHslF()
            return QColor.fromHslF(h, s, max(0, l - factor), a).name()
        except:
            return "#333333"