"""
اختبار النظام المحسن
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from accounting_app.ui.enhanced_main_window import EnhancedMainWindow

def main():
    """تشغيل النظام المحسن"""
    app = QApplication(sys.argv)
    
    # بيانات مستخدم تجريبية
    user_data = ["1", "admin", "admin123", "المدير العام", "مدير", "2024-01-15"]
    
    # إنشاء النافذة الرئيسية المحسنة
    window = EnhancedMainWindow(user_data)
    window.show()
    
    print("🚀 تم تشغيل النظام المحسن بنجاح!")
    print("📋 الميزات المتاحة:")
    print("   ✅ واجهة إعدادات محسنة مع تنظيم متقدم")
    print("   ✅ لوحة تحكم تفاعلية مع بيانات واضحة")
    print("   ✅ تقارير متقدمة مع فلاتر ذكية")
    print("   ✅ شريط جانبي بأيقونات ومظاهر متعددة")
    print("   ✅ أداء محسن مع Threading")
    print("   ✅ حفظ تلقائي وإدارة أفضل للبيانات")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()