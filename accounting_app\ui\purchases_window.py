"""
صفحة فواتير المشتريات
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                              QTableWidgetItem, QPushButton, QLabel, QFrame,
                              QLineEdit, QComboBox, QDateEdit, QSpinBox,
                              QDoubleSpinBox, QDialog, QFormLayout, QTextEdit,
                              QMessageBox, QHeaderView, QSplitter, QGroupBox,
                              QGridLayout, QScrollArea)
from PySide6.QtCore import Qt, QDate, QTimer
from PySide6.QtGui import QFont, QColor
from accounting_app.ui.styles import AppStyles
from datetime import datetime
import uuid


class PurchasesWindow(QWidget):
    """نافذة فواتير المشتريات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🛒 فواتير المشتريات")
        self.setMinimumSize(1200, 800)
        self.purchases_data = []
        self.suppliers_data = []
        self.init_ui()
        self.load_sample_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        main_layout.addWidget(toolbar)
        
        # المحتوى الرئيسي
        content = self.create_main_content()
        main_layout.addWidget(content)
    
    def create_header(self):
        """إنشاء رأس الصفحة"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ff7043, stop:0.5 #ff5722, stop:1 #e64a19);
                border-radius: 15px;
                padding: 25px;
                color: white;
                min-height: 120px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # المعلومات النصية
        text_section = QWidget()
        text_layout = QVBoxLayout(text_section)
        
        title = QLabel("🛒 إدارة فواتير المشتريات")
        title.setStyleSheet("font-size: 28px; font-weight: bold; color: white; margin-bottom: 10px;")
        
        subtitle = QLabel("إدارة شاملة لجميع فواتير المشتريات من الموردين")
        subtitle.setStyleSheet("font-size: 16px; color: white; opacity: 0.9;")
        
        # إحصائيات سريعة
        stats_layout = QHBoxLayout()
        stats_widgets = [
            self.create_stat_card("إجمالي المشتريات", "2,450,000 ج.م", "💰"),
            self.create_stat_card("عدد الفواتير", "324", "📄"),
            self.create_stat_card("الموردين النشطين", "48", "🏢")
        ]
        
        for widget in stats_widgets:
            stats_layout.addWidget(widget)
        stats_layout.addStretch()
        
        text_layout.addWidget(title)
        text_layout.addWidget(subtitle)
        text_layout.addSpacing(10)
        text_layout.addLayout(stats_layout)
        
        # الأيقونة
        icon_label = QLabel("🛒")
        icon_label.setStyleSheet("""
            font-size: 80px;
            color: white;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 40px;
            padding: 20px;
            min-width: 120px;
            max-width: 120px;
            min-height: 120px;
            max-height: 120px;
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(text_section, 8)
        layout.addWidget(icon_label, 2)
        
        return header_frame
    
    def create_stat_card(self, title, value, icon):
        """إنشاء بطاقة إحصائية"""
        card = QWidget()
        card.setStyleSheet("""
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 10px 15px;
            margin: 5px;
        """)
        
        layout = QHBoxLayout(card)
        layout.setContentsMargins(10, 8, 10, 8)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 20px; color: white;")
        
        text_widget = QWidget()
        text_layout = QVBoxLayout(text_widget)
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(2)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 12px; color: white; opacity: 0.8;")
        
        text_layout.addWidget(value_label)
        text_layout.addWidget(title_label)
        
        layout.addWidget(icon_label)
        layout.addWidget(text_widget)
        
        return card
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        layout.setSpacing(15)
        
        # زر إضافة فاتورة جديدة
        add_btn = QPushButton("➕ إضافة فاتورة جديدة")
        add_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                min-width: 150px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #3d8b40);
            }}
        """)
        add_btn.clicked.connect(self.add_purchase_invoice)
        
        # زر تعديل
        edit_btn = QPushButton("✏️ تعديل")
        edit_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976D2, stop:1 #1565C0);
            }}
        """)
        edit_btn.clicked.connect(self.edit_purchase_invoice)
        
        # زر حذف
        delete_btn = QPushButton("🗑️ حذف")
        delete_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f44336, stop:1 #d32f2f);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d32f2f, stop:1 #c62828);
            }}
        """)
        delete_btn.clicked.connect(self.delete_purchase_invoice)
        
        # مربع البحث
        search_input = QLineEdit()
        search_input.setPlaceholderText("🔍 البحث في الفواتير...")
        search_input.setStyleSheet(f"""
            QLineEdit {{
                padding: 12px 15px;
                border: 2px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                background: white;
                min-width: 200px;
            }}
            QLineEdit:focus {{
                border-color: {AppStyles.PRIMARY_COLOR};
            }}
        """)
        search_input.textChanged.connect(self.search_invoices)
        
        # فلتر المورد
        supplier_filter = QComboBox()
        supplier_filter.addItems(["جميع الموردين", "شركة التقنية المتقدمة", "مؤسسة الأجهزة الحديثة", "شركة البرمجيات العربية"])
        supplier_filter.setStyleSheet(f"""
            QComboBox {{
                padding: 12px 15px;
                border: 2px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                background: white;
                min-width: 180px;
            }}
            QComboBox:focus {{
                border-color: {AppStyles.PRIMARY_COLOR};
            }}
        """)
        supplier_filter.currentTextChanged.connect(self.filter_by_supplier)
        
        layout.addWidget(add_btn)
        layout.addWidget(edit_btn)
        layout.addWidget(delete_btn)
        layout.addStretch()
        layout.addWidget(QLabel("🔍 البحث:"))
        layout.addWidget(search_input)
        layout.addWidget(QLabel("🏢 المورد:"))
        layout.addWidget(supplier_filter)
        
        return toolbar_frame
    
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        splitter = QSplitter(Qt.Horizontal)
        
        # الجدول الرئيسي
        table_frame = self.create_purchases_table()
        
        # لوحة التفاصيل
        details_frame = self.create_details_panel()
        
        splitter.addWidget(table_frame)
        splitter.addWidget(details_frame)
        splitter.setSizes([700, 300])
        
        return splitter
    
    def create_purchases_table(self):
        """إنشاء جدول فواتير المشتريات"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # عنوان الجدول
        title_label = QLabel("📋 قائمة فواتير المشتريات")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        # الجدول
        self.purchases_table = QTableWidget()
        self.purchases_table.setColumnCount(7)
        self.purchases_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "التاريخ", "المورد", "إجمالي المبلغ", 
            "المدفوع", "المتبقي", "الحالة"
        ])
        
        # تنسيق الجدول
        self.purchases_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #e3f2fd;
                font-size: 13px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f5f5f5, stop:1 #e0e0e0);
                color: #333;
                padding: 12px 8px;
                border: 1px solid #ddd;
                font-weight: bold;
                font-size: 14px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
        
        # ضبط عرض الأعمدة
        header = self.purchases_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الفاتورة
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # المورد
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # إجمالي المبلغ
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # المدفوع
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # المتبقي
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # الحالة
        
        self.purchases_table.setAlternatingRowColors(True)
        self.purchases_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.purchases_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        layout.addWidget(title_label)
        layout.addWidget(self.purchases_table)
        
        return frame
    
    def create_details_panel(self):
        """إنشاء لوحة التفاصيل"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # عنوان اللوحة
        title_label = QLabel("📊 تفاصيل الفاتورة")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        # منطقة التفاصيل القابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; }")
        
        self.details_widget = QWidget()
        self.details_layout = QVBoxLayout(self.details_widget)
        
        # رسالة فارغة
        empty_label = QLabel("اختر فاتورة لعرض تفاصيلها")
        empty_label.setStyleSheet("""
            font-size: 16px;
            color: #666;
            text-align: center;
            padding: 50px;
        """)
        empty_label.setAlignment(Qt.AlignCenter)
        self.details_layout.addWidget(empty_label)
        
        scroll_area.setWidget(self.details_widget)
        
        layout.addWidget(title_label)
        layout.addWidget(scroll_area)
        
        return frame
    
    def load_sample_data(self):
        """تحميل البيانات الوهمية"""
        self.suppliers_data = [
            {"id": "SUP001", "name": "شركة التقنية المتقدمة", "phone": "01234567890", "email": "<EMAIL>"},
            {"id": "SUP002", "name": "مؤسسة الأجهزة الحديثة", "phone": "01234567891", "email": "<EMAIL>"},
            {"id": "SUP003", "name": "شركة البرمجيات العربية", "phone": "01234567892", "email": "<EMAIL>"},
        ]
        
        self.purchases_data = [
            {
                "invoice_number": "PUR-001",
                "date": "2024-01-20",
                "supplier": "شركة التقنية المتقدمة",
                "total_amount": 125000,
                "paid_amount": 125000,
                "remaining_amount": 0,
                "status": "مدفوعة",
                "items": [
                    {"name": "لابتوب ديل XPS", "quantity": 5, "unit_price": 25000, "total": 125000}
                ]
            },
            {
                "invoice_number": "PUR-002", 
                "date": "2024-01-19",
                "supplier": "مؤسسة الأجهزة الحديثة",
                "total_amount": 85000,
                "paid_amount": 50000,
                "remaining_amount": 35000,
                "status": "جزئية",
                "items": [
                    {"name": "طابعة ليزر", "quantity": 3, "unit_price": 15000, "total": 45000},
                    {"name": "ماسح ضوئي", "quantity": 2, "unit_price": 20000, "total": 40000}
                ]
            },
            {
                "invoice_number": "PUR-003",
                "date": "2024-01-18", 
                "supplier": "شركة البرمجيات العربية",
                "total_amount": 45000,
                "paid_amount": 0,
                "remaining_amount": 45000,
                "status": "غير مدفوعة",
                "items": [
                    {"name": "ترخيص أوفيس", "quantity": 15, "unit_price": 3000, "total": 45000}
                ]
            }
        ]
        
        self.refresh_table()
    
    def refresh_table(self):
        """تحديث الجدول"""
        self.purchases_table.setRowCount(len(self.purchases_data))
        
        for row, purchase in enumerate(self.purchases_data):
            # رقم الفاتورة
            self.purchases_table.setItem(row, 0, QTableWidgetItem(purchase["invoice_number"]))
            
            # التاريخ
            self.purchases_table.setItem(row, 1, QTableWidgetItem(purchase["date"]))
            
            # المورد
            self.purchases_table.setItem(row, 2, QTableWidgetItem(purchase["supplier"]))
            
            # إجمالي المبلغ
            total_item = QTableWidgetItem(f"{purchase['total_amount']:,.0f} ج.م")
            total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.purchases_table.setItem(row, 3, total_item)
            
            # المدفوع
            paid_item = QTableWidgetItem(f"{purchase['paid_amount']:,.0f} ج.م")
            paid_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.purchases_table.setItem(row, 4, paid_item)
            
            # المتبقي
            remaining_item = QTableWidgetItem(f"{purchase['remaining_amount']:,.0f} ج.م")
            remaining_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.purchases_table.setItem(row, 5, remaining_item)
            
            # الحالة
            status_item = QTableWidgetItem(purchase["status"])
            if purchase["status"] == "مدفوعة":
                status_item.setBackground(QColor("#e8f5e8"))
                status_item.setForeground(QColor("#2e7d32"))
            elif purchase["status"] == "جزئية":
                status_item.setBackground(QColor("#fff3e0"))
                status_item.setForeground(QColor("#ef6c00"))
            else:
                status_item.setBackground(QColor("#ffebee"))
                status_item.setForeground(QColor("#c62828"))
            
            self.purchases_table.setItem(row, 6, status_item)
    
    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        current_row = self.purchases_table.currentRow()
        if current_row >= 0 and current_row < len(self.purchases_data):
            self.show_purchase_details(self.purchases_data[current_row])
    
    def show_purchase_details(self, purchase):
        """عرض تفاصيل الفاتورة"""
        # مسح المحتوى السابق
        for i in reversed(range(self.details_layout.count())):
            self.details_layout.itemAt(i).widget().setParent(None)
        
        # معلومات الفاتورة
        info_group = QGroupBox("📋 معلومات الفاتورة")
        info_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px;
            }
        """)
        
        info_layout = QGridLayout(info_group)
        info_layout.setSpacing(10)
        
        # إضافة المعلومات
        info_data = [
            ("رقم الفاتورة:", purchase["invoice_number"]),
            ("التاريخ:", purchase["date"]),
            ("المورد:", purchase["supplier"]),
            ("إجمالي المبلغ:", f"{purchase['total_amount']:,.0f} ج.م"),
            ("المدفوع:", f"{purchase['paid_amount']:,.0f} ج.م"),
            ("المتبقي:", f"{purchase['remaining_amount']:,.0f} ج.م"),
            ("الحالة:", purchase["status"])
        ]
        
        for i, (label, value) in enumerate(info_data):
            label_widget = QLabel(label)
            label_widget.setStyleSheet("font-weight: 600; color: #555;")
            
            value_widget = QLabel(str(value))
            value_widget.setStyleSheet("color: #333;")
            
            info_layout.addWidget(label_widget, i, 0)
            info_layout.addWidget(value_widget, i, 1)
        
        # أصناف الفاتورة
        items_group = QGroupBox("📦 أصناف الفاتورة")
        items_group.setStyleSheet(info_group.styleSheet())
        
        items_layout = QVBoxLayout(items_group)
        
        # جدول الأصناف
        items_table = QTableWidget()
        items_table.setColumnCount(4)
        items_table.setHorizontalHeaderLabels(["الصنف", "الكمية", "سعر الوحدة", "المجموع"])
        items_table.setMaximumHeight(150)
        
        items_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                font-size: 12px;
                border: 1px solid #ddd;
            }
            QHeaderView::section {
                background: #f5f5f5;
                color: #333;
                padding: 8px;
                border: 1px solid #ddd;
                font-weight: bold;
            }
        """)
        
        items_table.setRowCount(len(purchase["items"]))
        
        for row, item in enumerate(purchase["items"]):
            items_table.setItem(row, 0, QTableWidgetItem(item["name"]))
            items_table.setItem(row, 1, QTableWidgetItem(str(item["quantity"])))
            items_table.setItem(row, 2, QTableWidgetItem(f"{item['unit_price']:,.0f} ج.م"))
            items_table.setItem(row, 3, QTableWidgetItem(f"{item['total']:,.0f} ج.م"))
        
        # ضبط عرض الأعمدة
        header = items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        items_layout.addWidget(items_table)
        
        # إضافة المجموعات
        self.details_layout.addWidget(info_group)
        self.details_layout.addWidget(items_group)
        self.details_layout.addStretch()
    
    def add_purchase_invoice(self):
        """إضافة فاتورة مشتريات جديدة"""
        dialog = PurchaseInvoiceDialog(self.suppliers_data, parent=self)
        if dialog.exec() == QDialog.Accepted:
            invoice_data = dialog.get_invoice_data()
            self.purchases_data.append(invoice_data)
            self.refresh_table()
            QMessageBox.information(self, "نجح", "تم إضافة فاتورة المشتريات بنجاح!")
    
    def edit_purchase_invoice(self):
        """تعديل فاتورة مشتريات"""
        current_row = self.purchases_table.currentRow()
        if current_row >= 0:
            invoice = self.purchases_data[current_row]
            dialog = PurchaseInvoiceDialog(self.suppliers_data, invoice, parent=self)
            if dialog.exec() == QDialog.Accepted:
                updated_data = dialog.get_invoice_data()
                self.purchases_data[current_row] = updated_data
                self.refresh_table()
                QMessageBox.information(self, "نجح", "تم تعديل فاتورة المشتريات بنجاح!")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للتعديل")
    
    def delete_purchase_invoice(self):
        """حذف فاتورة مشتريات"""
        current_row = self.purchases_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(
                self, 
                "تأكيد الحذف", 
                "هل تريد حذف هذه الفاتورة؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                del self.purchases_data[current_row]
                self.refresh_table()
                QMessageBox.information(self, "نجح", "تم حذف فاتورة المشتريات بنجاح!")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للحذف")
    
    def search_invoices(self, text):
        """البحث في الفواتير"""
        # تنفيذ البحث (يمكن تطويره أكثر)
        print(f"البحث عن: {text}")
    
    def filter_by_supplier(self, supplier):
        """فلترة حسب المورد"""
        # تنفيذ الفلترة (يمكن تطويره أكثر)
        print(f"فلترة حسب المورد: {supplier}")


class PurchaseInvoiceDialog(QDialog):
    """نافذة إضافة/تعديل فاتورة مشتريات"""
    
    def __init__(self, suppliers_data, invoice_data=None, parent=None):
        super().__init__(parent)
        self.suppliers_data = suppliers_data
        self.invoice_data = invoice_data
        self.items_data = []
        
        self.setWindowTitle("فاتورة مشتريات جديدة" if invoice_data is None else "تعديل فاتورة مشتريات")
        self.setMinimumSize(800, 600)
        self.init_ui()
        
        if invoice_data:
            self.load_invoice_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # معلومات الفاتورة
        info_group = self.create_invoice_info_group()
        layout.addWidget(info_group)
        
        # أصناف الفاتورة
        items_group = self.create_items_group()
        layout.addWidget(items_group)
        
        # المجاميع
        totals_group = self.create_totals_group()
        layout.addWidget(totals_group)
        
        # الأزرار
        buttons_layout = self.create_buttons()
        layout.addLayout(buttons_layout)
    
    def create_invoice_info_group(self):
        """إنشاء مجموعة معلومات الفاتورة"""
        group = QGroupBox("📋 معلومات الفاتورة")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # رقم الفاتورة
        layout.addWidget(QLabel("رقم الفاتورة:"), 0, 0)
        self.invoice_number_input = QLineEdit()
        self.invoice_number_input.setText(f"PUR-{str(uuid.uuid4())[:8].upper()}")
        self.invoice_number_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.invoice_number_input, 0, 1)
        
        # التاريخ
        layout.addWidget(QLabel("التاريخ:"), 0, 2)
        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.date_input, 0, 3)
        
        # المورد
        layout.addWidget(QLabel("المورد:"), 1, 0)
        self.supplier_combo = QComboBox()
        self.supplier_combo.addItems([supplier["name"] for supplier in self.suppliers_data])
        self.supplier_combo.setStyleSheet(self.get_input_style())
        layout.addWidget(self.supplier_combo, 1, 1)
        
        # الملاحظات
        layout.addWidget(QLabel("الملاحظات:"), 1, 2)
        self.notes_input = QLineEdit()
        self.notes_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.notes_input, 1, 3)
        
        return group
    
    def create_items_group(self):
        """إنشاء مجموعة الأصناف"""
        group = QGroupBox("📦 أصناف الفاتورة")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
        """)
        
        layout = QVBoxLayout(group)
        
        # شريط إضافة صنف
        add_item_layout = QHBoxLayout()
        
        self.item_name_input = QLineEdit()
        self.item_name_input.setPlaceholderText("اسم الصنف")
        self.item_name_input.setStyleSheet(self.get_input_style())
        
        self.item_quantity_input = QSpinBox()
        self.item_quantity_input.setMinimum(1)
        self.item_quantity_input.setMaximum(9999)
        self.item_quantity_input.setValue(1)
        self.item_quantity_input.setStyleSheet(self.get_input_style())
        
        self.item_price_input = QDoubleSpinBox()
        self.item_price_input.setMinimum(0.01)
        self.item_price_input.setMaximum(999999.99)
        self.item_price_input.setDecimals(2)
        self.item_price_input.setSuffix(" ج.م")
        self.item_price_input.setStyleSheet(self.get_input_style())
        
        add_item_btn = QPushButton("➕ إضافة صنف")
        add_item_btn.setStyleSheet(f"""
            QPushButton {{
                background: {AppStyles.PRIMARY_COLOR};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 600;
            }}
            QPushButton:hover {{
                background: {AppStyles.HIGHLIGHT_COLOR};
            }}
        """)
        add_item_btn.clicked.connect(self.add_item)
        
        add_item_layout.addWidget(QLabel("الصنف:"))
        add_item_layout.addWidget(self.item_name_input, 3)
        add_item_layout.addWidget(QLabel("الكمية:"))
        add_item_layout.addWidget(self.item_quantity_input, 1)
        add_item_layout.addWidget(QLabel("السعر:"))
        add_item_layout.addWidget(self.item_price_input, 2)
        add_item_layout.addWidget(add_item_btn, 1)
        
        # جدول الأصناف
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels(["الصنف", "الكمية", "سعر الوحدة", "المجموع", "حذف"])
        
        self.items_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                font-size: 13px;
                border: 1px solid #ddd;
            }
            QHeaderView::section {
                background: #f5f5f5;
                color: #333;
                padding: 10px;
                border: 1px solid #ddd;
                font-weight: bold;
            }
        """)
        
        # ضبط عرض الأعمدة
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        layout.addLayout(add_item_layout)
        layout.addWidget(self.items_table)
        
        return group
    
    def create_totals_group(self):
        """إنشاء مجموعة المجاميع"""
        group = QGroupBox("💰 المجاميع")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # المجموع الفرعي
        layout.addWidget(QLabel("المجموع الفرعي:"), 0, 0)
        self.subtotal_label = QLabel("0.00 ج.م")
        self.subtotal_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #333;")
        layout.addWidget(self.subtotal_label, 0, 1)
        
        # الضريبة
        layout.addWidget(QLabel("الضريبة (%):"), 0, 2)
        self.tax_input = QDoubleSpinBox()
        self.tax_input.setMinimum(0)
        self.tax_input.setMaximum(100)
        self.tax_input.setValue(14)
        self.tax_input.setDecimals(2)
        self.tax_input.setSuffix(" %")
        self.tax_input.setStyleSheet(self.get_input_style())
        self.tax_input.valueChanged.connect(self.calculate_totals)
        layout.addWidget(self.tax_input, 0, 3)
        
        # مبلغ الضريبة
        layout.addWidget(QLabel("مبلغ الضريبة:"), 1, 0)
        self.tax_amount_label = QLabel("0.00 ج.م")
        self.tax_amount_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #333;")
        layout.addWidget(self.tax_amount_label, 1, 1)
        
        # الإجمالي
        layout.addWidget(QLabel("الإجمالي:"), 1, 2)
        self.total_label = QLabel("0.00 ج.م")
        self.total_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #d32f2f;")
        layout.addWidget(self.total_label, 1, 3)
        
        return group
    
    def create_buttons(self):
        """إنشاء أزرار الحوار"""
        layout = QHBoxLayout()
        layout.addStretch()
        
        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: #f44336;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }
            QPushButton:hover {
                background: #d32f2f;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        # زر الحفظ
        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet(f"""
            QPushButton {{
                background: {AppStyles.PRIMARY_COLOR};
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background: {AppStyles.HIGHLIGHT_COLOR};
            }}
        """)
        save_btn.clicked.connect(self.accept)
        
        layout.addWidget(cancel_btn)
        layout.addWidget(save_btn)
        
        return layout
    
    def get_input_style(self):
        """الحصول على نمط عناصر الإدخال"""
        return f"""
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {{
                padding: 10px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 14px;
                background: white;
            }}
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {{
                border-color: {AppStyles.PRIMARY_COLOR};
            }}
        """
    
    def add_item(self):
        """إضافة صنف جديد"""
        name = self.item_name_input.text().strip()
        quantity = self.item_quantity_input.value()
        price = self.item_price_input.value()
        
        if not name:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الصنف")
            return
        
        if price <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر صحيح")
            return
        
        total = quantity * price
        
        item = {
            "name": name,
            "quantity": quantity,
            "unit_price": price,
            "total": total
        }
        
        self.items_data.append(item)
        self.refresh_items_table()
        self.calculate_totals()
        
        # مسح الحقول
        self.item_name_input.clear()
        self.item_quantity_input.setValue(1)
        self.item_price_input.setValue(0.00)
    
    def refresh_items_table(self):
        """تحديث جدول الأصناف"""
        self.items_table.setRowCount(len(self.items_data))
        
        for row, item in enumerate(self.items_data):
            self.items_table.setItem(row, 0, QTableWidgetItem(item["name"]))
            self.items_table.setItem(row, 1, QTableWidgetItem(str(item["quantity"])))
            self.items_table.setItem(row, 2, QTableWidgetItem(f"{item['unit_price']:.2f} ج.م"))
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{item['total']:.2f} ج.م"))
            
            # زر الحذف
            delete_btn = QPushButton("🗑️")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background: #f44336;
                    color: white;
                    border: none;
                    padding: 5px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background: #d32f2f;
                }
            """)
            delete_btn.clicked.connect(lambda checked, r=row: self.delete_item(r))
            self.items_table.setCellWidget(row, 4, delete_btn)
    
    def delete_item(self, row):
        """حذف صنف"""
        if 0 <= row < len(self.items_data):
            del self.items_data[row]
            self.refresh_items_table()
            self.calculate_totals()
    
    def calculate_totals(self):
        """حساب المجاميع"""
        subtotal = sum(item["total"] for item in self.items_data)
        tax_rate = self.tax_input.value()
        tax_amount = subtotal * (tax_rate / 100)
        total = subtotal + tax_amount
        
        self.subtotal_label.setText(f"{subtotal:,.2f} ج.م")
        self.tax_amount_label.setText(f"{tax_amount:,.2f} ج.م")
        self.total_label.setText(f"{total:,.2f} ج.م")
    
    def load_invoice_data(self):
        """تحميل بيانات الفاتورة للتعديل"""
        self.invoice_number_input.setText(self.invoice_data["invoice_number"])
        self.date_input.setDate(QDate.fromString(self.invoice_data["date"], "yyyy-MM-dd"))
        self.supplier_combo.setCurrentText(self.invoice_data["supplier"])
        
        # تحميل الأصناف
        self.items_data = self.invoice_data["items"].copy()
        self.refresh_items_table()
        self.calculate_totals()
    
    def get_invoice_data(self):
        """الحصول على بيانات الفاتورة"""
        subtotal = sum(item["total"] for item in self.items_data)
        tax_rate = self.tax_input.value()
        tax_amount = subtotal * (tax_rate / 100)
        total = subtotal + tax_amount
        
        return {
            "invoice_number": self.invoice_number_input.text(),
            "date": self.date_input.date().toString("yyyy-MM-dd"),
            "supplier": self.supplier_combo.currentText(),
            "total_amount": total,
            "paid_amount": 0,  # يمكن إضافة حقل للمبلغ المدفوع
            "remaining_amount": total,
            "status": "غير مدفوعة",
            "items": self.items_data.copy()
        }


class PurchasesWindow(QWidget):
    """نافذة فواتير المشتريات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🛒 فواتير المشتريات")
        self.setMinimumSize(1200, 800)
        self.purchases_data = []
        self.suppliers_data = []
        self.init_ui()
        self.load_sample_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        main_layout.addWidget(toolbar)
        
        # المحتوى الرئيسي
        content = self.create_main_content()
        main_layout.addWidget(content)
    
    def create_header(self):
        """إنشاء رأس الصفحة"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ff7043, stop:0.5 #ff5722, stop:1 #e64a19);
                border-radius: 15px;
                padding: 25px;
                color: white;
                min-height: 120px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # المعلومات النصية
        text_section = QWidget()
        text_layout = QVBoxLayout(text_section)
        
        title = QLabel("🛒 إدارة فواتير المشتريات")
        title.setStyleSheet("font-size: 28px; font-weight: bold; color: white; margin-bottom: 10px;")
        
        subtitle = QLabel("إدارة شاملة لجميع فواتير المشتريات من الموردين")
        subtitle.setStyleSheet("font-size: 16px; color: white; opacity: 0.9;")
        
        # إحصائيات سريعة
        stats_layout = QHBoxLayout()
        stats_widgets = [
            self.create_stat_card("إجمالي المشتريات", "2,450,000 ج.م", "💰"),
            self.create_stat_card("عدد الفواتير", "324", "📄"),
            self.create_stat_card("الموردين النشطين", "48", "🏢")
        ]
        
        for widget in stats_widgets:
            stats_layout.addWidget(widget)
        stats_layout.addStretch()
        
        text_layout.addWidget(title)
        text_layout.addWidget(subtitle)
        text_layout.addSpacing(10)
        text_layout.addLayout(stats_layout)
        
        # الأيقونة
        icon_label = QLabel("🛒")
        icon_label.setStyleSheet("""
            font-size: 80px;
            color: white;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 40px;
            padding: 20px;
            min-width: 120px;
            max-width: 120px;
            min-height: 120px;
            max-height: 120px;
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(text_section, 8)
        layout.addWidget(icon_label, 2)
        
        return header_frame
    
    def create_stat_card(self, title, value, icon):
        """إنشاء بطاقة إحصائية"""
        card = QWidget()
        card.setStyleSheet("""
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 10px 15px;
            margin: 5px;
        """)
        
        layout = QHBoxLayout(card)
        layout.setContentsMargins(10, 8, 10, 8)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 20px; color: white;")
        
        text_widget = QWidget()
        text_layout = QVBoxLayout(text_widget)
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(2)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 12px; color: white; opacity: 0.8;")
        
        text_layout.addWidget(value_label)
        text_layout.addWidget(title_label)
        
        layout.addWidget(icon_label)
        layout.addWidget(text_widget)
        
        return card
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        layout.setSpacing(15)
        
        # زر إضافة فاتورة جديدة
        add_btn = QPushButton("➕ إضافة فاتورة جديدة")
        add_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                min-width: 150px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #3d8b40);
            }}
        """)
        add_btn.clicked.connect(self.add_purchase_invoice)
        
        # زر تعديل
        edit_btn = QPushButton("✏️ تعديل")
        edit_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976D2, stop:1 #1565C0);
            }}
        """)
        edit_btn.clicked.connect(self.edit_purchase_invoice)
        
        # زر حذف
        delete_btn = QPushButton("🗑️ حذف")
        delete_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f44336, stop:1 #d32f2f);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d32f2f, stop:1 #c62828);
            }}
        """)
        delete_btn.clicked.connect(self.delete_purchase_invoice)
        
        # مربع البحث
        search_input = QLineEdit()
        search_input.setPlaceholderText("🔍 البحث في الفواتير...")
        search_input.setStyleSheet(f"""
            QLineEdit {{
                padding: 12px 15px;
                border: 2px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                background: white;
                min-width: 200px;
            }}
            QLineEdit:focus {{
                border-color: {AppStyles.PRIMARY_COLOR};
            }}
        """)
        search_input.textChanged.connect(self.search_invoices)
        
        # فلتر المورد
        supplier_filter = QComboBox()
        supplier_filter.addItems(["جميع الموردين", "شركة التقنية المتقدمة", "مؤسسة الأجهزة الحديثة", "شركة البرمجيات العربية"])
        supplier_filter.setStyleSheet(f"""
            QComboBox {{
                padding: 12px 15px;
                border: 2px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                background: white;
                min-width: 180px;
            }}
            QComboBox:focus {{
                border-color: {AppStyles.PRIMARY_COLOR};
            }}
        """)
        supplier_filter.currentTextChanged.connect(self.filter_by_supplier)
        
        layout.addWidget(add_btn)
        layout.addWidget(edit_btn)
        layout.addWidget(delete_btn)
        layout.addStretch()
        layout.addWidget(QLabel("🔍 البحث:"))
        layout.addWidget(search_input)
        layout.addWidget(QLabel("🏢 المورد:"))
        layout.addWidget(supplier_filter)
        
        return toolbar_frame
    
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        splitter = QSplitter(Qt.Horizontal)
        
        # الجدول الرئيسي
        table_frame = self.create_purchases_table()
        
        # لوحة التفاصيل
        details_frame = self.create_details_panel()
        
        splitter.addWidget(table_frame)
        splitter.addWidget(details_frame)
        splitter.setSizes([700, 300])
        
        return splitter
    
    def create_purchases_table(self):
        """إنشاء جدول فواتير المشتريات"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # عنوان الجدول
        title_label = QLabel("📋 قائمة فواتير المشتريات")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        # الجدول
        self.purchases_table = QTableWidget()
        self.purchases_table.setColumnCount(7)
        self.purchases_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "التاريخ", "المورد", "إجمالي المبلغ", 
            "المدفوع", "المتبقي", "الحالة"
        ])
        
        # تنسيق الجدول
        self.purchases_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #e3f2fd;
                font-size: 13px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f5f5f5, stop:1 #e0e0e0);
                color: #333;
                padding: 12px 8px;
                border: 1px solid #ddd;
                font-weight: bold;
                font-size: 14px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
        
        # ضبط عرض الأعمدة
        header = self.purchases_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الفاتورة
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # المورد
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # إجمالي المبلغ
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # المدفوع
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # المتبقي
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # الحالة
        
        self.purchases_table.setAlternatingRowColors(True)
        self.purchases_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.purchases_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        layout.addWidget(title_label)
        layout.addWidget(self.purchases_table)
        
        return frame
    
    def create_details_panel(self):
        """إنشاء لوحة التفاصيل"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # عنوان اللوحة
        title_label = QLabel("📊 تفاصيل الفاتورة")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        # منطقة التفاصيل القابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; }")
        
        self.details_widget = QWidget()
        self.details_layout = QVBoxLayout(self.details_widget)
        
        # رسالة فارغة
        empty_label = QLabel("اختر فاتورة لعرض تفاصيلها")
        empty_label.setStyleSheet("""
            font-size: 16px;
            color: #666;
            text-align: center;
            padding: 50px;
        """)
        empty_label.setAlignment(Qt.AlignCenter)
        self.details_layout.addWidget(empty_label)
        
        scroll_area.setWidget(self.details_widget)
        
        layout.addWidget(title_label)
        layout.addWidget(scroll_area)
        
        return frame
    
    def load_sample_data(self):
        """تحميل البيانات الوهمية"""
        self.suppliers_data = [
            {"id": "SUP001", "name": "شركة التقنية المتقدمة", "phone": "01234567890", "email": "<EMAIL>"},
            {"id": "SUP002", "name": "مؤسسة الأجهزة الحديثة", "phone": "01234567891", "email": "<EMAIL>"},
            {"id": "SUP003", "name": "شركة البرمجيات العربية", "phone": "01234567892", "email": "<EMAIL>"},
        ]
        
        self.purchases_data = [
            {
                "invoice_number": "PUR-001",
                "date": "2024-01-20",
                "supplier": "شركة التقنية المتقدمة",
                "total_amount": 125000,
                "paid_amount": 125000,
                "remaining_amount": 0,
                "status": "مدفوعة",
                "items": [
                    {"name": "لابتوب ديل XPS", "quantity": 5, "unit_price": 25000, "total": 125000}
                ]
            },
            {
                "invoice_number": "PUR-002", 
                "date": "2024-01-19",
                "supplier": "مؤسسة الأجهزة الحديثة",
                "total_amount": 85000,
                "paid_amount": 50000,
                "remaining_amount": 35000,
                "status": "جزئية",
                "items": [
                    {"name": "طابعة ليزر", "quantity": 3, "unit_price": 15000, "total": 45000},
                    {"name": "ماسح ضوئي", "quantity": 2, "unit_price": 20000, "total": 40000}
                ]
            },
            {
                "invoice_number": "PUR-003",
                "date": "2024-01-18", 
                "supplier": "شركة البرمجيات العربية",
                "total_amount": 45000,
                "paid_amount": 0,
                "remaining_amount": 45000,
                "status": "غير مدفوعة",
                "items": [
                    {"name": "ترخيص أوفيس", "quantity": 15, "unit_price": 3000, "total": 45000}
                ]
            }
        ]
        
        self.refresh_table()
    
    def refresh_table(self):
        """تحديث الجدول"""
        self.purchases_table.setRowCount(len(self.purchases_data))
        
        for row, purchase in enumerate(self.purchases_data):
            # رقم الفاتورة
            self.purchases_table.setItem(row, 0, QTableWidgetItem(purchase["invoice_number"]))
            
            # التاريخ
            self.purchases_table.setItem(row, 1, QTableWidgetItem(purchase["date"]))
            
            # المورد
            self.purchases_table.setItem(row, 2, QTableWidgetItem(purchase["supplier"]))
            
            # إجمالي المبلغ
            total_item = QTableWidgetItem(f"{purchase['total_amount']:,.0f} ج.م")
            total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.purchases_table.setItem(row, 3, total_item)
            
            # المدفوع
            paid_item = QTableWidgetItem(f"{purchase['paid_amount']:,.0f} ج.م")
            paid_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.purchases_table.setItem(row, 4, paid_item)
            
            # المتبقي
            remaining_item = QTableWidgetItem(f"{purchase['remaining_amount']:,.0f} ج.م")
            remaining_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.purchases_table.setItem(row, 5, remaining_item)
            
            # الحالة
            status_item = QTableWidgetItem(purchase["status"])
            if purchase["status"] == "مدفوعة":
                status_item.setBackground(QColor("#e8f5e8"))
                status_item.setForeground(QColor("#2e7d32"))
            elif purchase["status"] == "جزئية":
                status_item.setBackground(QColor("#fff3e0"))
                status_item.setForeground(QColor("#ef6c00"))
            else:
                status_item.setBackground(QColor("#ffebee"))
                status_item.setForeground(QColor("#c62828"))
            
            self.purchases_table.setItem(row, 6, status_item)
    
    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        current_row = self.purchases_table.currentRow()
        if current_row >= 0 and current_row < len(self.purchases_data):
            self.show_purchase_details(self.purchases_data[current_row])
    
    def show_purchase_details(self, purchase):
        """عرض تفاصيل الفاتورة"""
        # مسح المحتوى السابق
        for i in reversed(range(self.details_layout.count())):
            self.details_layout.itemAt(i).widget().setParent(None)
        
        # معلومات الفاتورة
        info_group = QGroupBox("📋 معلومات الفاتورة")
        info_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px;
            }
        """)
        
        info_layout = QGridLayout(info_group)
        info_layout.setSpacing(10)
        
        # إضافة المعلومات
        info_data = [
            ("رقم الفاتورة:", purchase["invoice_number"]),
            ("التاريخ:", purchase["date"]),
            ("المورد:", purchase["supplier"]),
            ("إجمالي المبلغ:", f"{purchase['total_amount']:,.0f} ج.م"),
            ("المدفوع:", f"{purchase['paid_amount']:,.0f} ج.م"),
            ("المتبقي:", f"{purchase['remaining_amount']:,.0f} ج.م"),
            ("الحالة:", purchase["status"])
        ]
        
        for i, (label, value) in enumerate(info_data):
            label_widget = QLabel(label)
            label_widget.setStyleSheet("font-weight: 600; color: #555;")
            
            value_widget = QLabel(str(value))
            value_widget.setStyleSheet("color: #333;")
            
            info_layout.addWidget(label_widget, i, 0)
            info_layout.addWidget(value_widget, i, 1)
        
        # أصناف الفاتورة
        items_group = QGroupBox("📦 أصناف الفاتورة")
        items_group.setStyleSheet(info_group.styleSheet())
        
        items_layout = QVBoxLayout(items_group)
        
        # جدول الأصناف
        items_table = QTableWidget()
        items_table.setColumnCount(4)
        items_table.setHorizontalHeaderLabels(["الصنف", "الكمية", "سعر الوحدة", "المجموع"])
        items_table.setMaximumHeight(150)
        
        items_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                font-size: 12px;
                border: 1px solid #ddd;
            }
            QHeaderView::section {
                background: #f5f5f5;
                color: #333;
                padding: 8px;
                border: 1px solid #ddd;
                font-weight: bold;
            }
        """)
        
        items_table.setRowCount(len(purchase["items"]))
        
        for row, item in enumerate(purchase["items"]):
            items_table.setItem(row, 0, QTableWidgetItem(item["name"]))
            items_table.setItem(row, 1, QTableWidgetItem(str(item["quantity"])))
            items_table.setItem(row, 2, QTableWidgetItem(f"{item['unit_price']:,.0f} ج.م"))
            items_table.setItem(row, 3, QTableWidgetItem(f"{item['total']:,.0f} ج.م"))
        
        # ضبط عرض الأعمدة
        header = items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        items_layout.addWidget(items_table)
        
        # إضافة المجموعات
        self.details_layout.addWidget(info_group)
        self.details_layout.addWidget(items_group)
        self.details_layout.addStretch()
    
    def add_purchase_invoice(self):
        """إضافة فاتورة مشتريات جديدة"""
        dialog = PurchaseInvoiceDialog(self.suppliers_data, parent=self)
        if dialog.exec() == QDialog.Accepted:
            invoice_data = dialog.get_invoice_data()
            self.purchases_data.append(invoice_data)
            self.refresh_table()
            QMessageBox.information(self, "نجح", "تم إضافة فاتورة المشتريات بنجاح!")
    
    def edit_purchase_invoice(self):
        """تعديل فاتورة مشتريات"""
        current_row = self.purchases_table.currentRow()
        if current_row >= 0:
            invoice = self.purchases_data[current_row]
            dialog = PurchaseInvoiceDialog(self.suppliers_data, invoice, parent=self)
            if dialog.exec() == QDialog.Accepted:
                updated_data = dialog.get_invoice_data()
                self.purchases_data[current_row] = updated_data
                self.refresh_table()
                QMessageBox.information(self, "نجح", "تم تعديل فاتورة المشتريات بنجاح!")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للتعديل")
    
    def delete_purchase_invoice(self):
        """حذف فاتورة مشتريات"""
        current_row = self.purchases_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(
                self, 
                "تأكيد الحذف", 
                "هل تريد حذف هذه الفاتورة؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                del self.purchases_data[current_row]
                self.refresh_table()
                QMessageBox.information(self, "نجح", "تم حذف فاتورة المشتريات بنجاح!")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للحذف")
    
    def search_invoices(self, text):
        """البحث في الفواتير"""
        # تنفيذ البحث (يمكن تطويره أكثر)
        print(f"البحث عن: {text}")
    
    def filter_by_supplier(self, supplier):
        """فلترة حسب المورد"""
        # تنفيذ الفلترة (يمكن تطويره أكثر)
        print(f"فلترة حسب المورد: {supplier}")


class PurchaseInvoiceDialog(QDialog):
    """نافذة إضافة/تعديل فاتورة مشتريات"""
    
    def __init__(self, suppliers_data, invoice_data=None, parent=None):
        super().__init__(parent)
        self.suppliers_data = suppliers_data
        self.invoice_data = invoice_data
        self.items_data = []
        
        self.setWindowTitle("فاتورة مشتريات جديدة" if invoice_data is None else "تعديل فاتورة مشتريات")
        self.setMinimumSize(800, 600)
        self.init_ui()
        
        if invoice_data:
            self.load_invoice_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # معلومات الفاتورة
        info_group = self.create_invoice_info_group()
        layout.addWidget(info_group)
        
        # أصناف الفاتورة
        items_group = self.create_items_group()
        layout.addWidget(items_group)
        
        # المجاميع
        totals_group = self.create_totals_group()
        layout.addWidget(totals_group)
        
        # الأزرار
        buttons_layout = self.create_buttons()
        layout.addLayout(buttons_layout)
    
    def create_invoice_info_group(self):
        """إنشاء مجموعة معلومات الفاتورة"""
        group = QGroupBox("📋 معلومات الفاتورة")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # رقم الفاتورة
        layout.addWidget(QLabel("رقم الفاتورة:"), 0, 0)
        self.invoice_number_input = QLineEdit()
        self.invoice_number_input.setText(f"PUR-{str(uuid.uuid4())[:8].upper()}")
        self.invoice_number_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.invoice_number_input, 0, 1)
        
        # التاريخ
        layout.addWidget(QLabel("التاريخ:"), 0, 2)
        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.date_input, 0, 3)
        
        # المورد
        layout.addWidget(QLabel("المورد:"), 1, 0)
        self.supplier_combo = QComboBox()
        self.supplier_combo.addItems([supplier["name"] for supplier in self.suppliers_data])
        self.supplier_combo.setStyleSheet(self.get_input_style())
        layout.addWidget(self.supplier_combo, 1, 1)
        
        # الملاحظات
        layout.addWidget(QLabel("الملاحظات:"), 1, 2)
        self.notes_input = QLineEdit()
        self.notes_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.notes_input, 1, 3)
        
        return group
    
    def create_items_group(self):
        """إنشاء مجموعة الأصناف"""
        group = QGroupBox("📦 أصناف الفاتورة")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
        """)
        
        layout = QVBoxLayout(group)
        
        # شريط إضافة صنف
        add_item_layout = QHBoxLayout()
        
        self.item_name_input = QLineEdit()
        self.item_name_input.setPlaceholderText("اسم الصنف")
        self.item_name_input.setStyleSheet(self.get_input_style())
        
        self.item_quantity_input = QSpinBox()
        self.item_quantity_input.setMinimum(1)
        self.item_quantity_input.setMaximum(9999)
        self.item_quantity_input.setValue(1)
        self.item_quantity_input.setStyleSheet(self.get_input_style())
        
        self.item_price_input = QDoubleSpinBox()
        self.item_price_input.setMinimum(0.01)
        self.item_price_input.setMaximum(999999.99)
        self.item_price_input.setDecimals(2)
        self.item_price_input.setSuffix(" ج.م")
        self.item_price_input.setStyleSheet(self.get_input_style())
        
        add_item_btn = QPushButton("➕ إضافة صنف")
        add_item_btn.setStyleSheet(f"""
            QPushButton {{
                background: {AppStyles.PRIMARY_COLOR};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 600;
            }}
            QPushButton:hover {{
                background: {AppStyles.HIGHLIGHT_COLOR};
            }}
        """)
        add_item_btn.clicked.connect(self.add_item)
        
        add_item_layout.addWidget(QLabel("الصنف:"))
        add_item_layout.addWidget(self.item_name_input, 3)
        add_item_layout.addWidget(QLabel("الكمية:"))
        add_item_layout.addWidget(self.item_quantity_input, 1)
        add_item_layout.addWidget(QLabel("السعر:"))
        add_item_layout.addWidget(self.item_price_input, 2)
        add_item_layout.addWidget(add_item_btn, 1)
        
        # جدول الأصناف
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels(["الصنف", "الكمية", "سعر الوحدة", "المجموع", "حذف"])
        
        self.items_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                font-size: 13px;
                border: 1px solid #ddd;
            }
            QHeaderView::section {
                background: #f5f5f5;
                color: #333;
                padding: 10px;
                border: 1px solid #ddd;
                font-weight: bold;
            }
        """)
        
        # ضبط عرض الأعمدة
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        layout.addLayout(add_item_layout)
        layout.addWidget(self.items_table)
        
        return group
    
    def create_totals_group(self):
        """إنشاء مجموعة المجاميع"""
        group = QGroupBox("💰 المجاميع")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # المجموع الفرعي
        layout.addWidget(QLabel("المجموع الفرعي:"), 0, 0)
        self.subtotal_label = QLabel("0.00 ج.م")
        self.subtotal_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #333;")
        layout.addWidget(self.subtotal_label, 0, 1)
        
        # الضريبة
        layout.addWidget(QLabel("الضريبة (%):"), 0, 2)
        self.tax_input = QDoubleSpinBox()
        self.tax_input.setMinimum(0)
        self.tax_input.setMaximum(100)
        self.tax_input.setValue(14)
        self.tax_input.setDecimals(2)
        self.tax_input.setSuffix(" %")
        self.tax_input.setStyleSheet(self.get_input_style())
        self.tax_input.valueChanged.connect(self.calculate_totals)
        layout.addWidget(self.tax_input, 0, 3)
        
        # مبلغ الضريبة
        layout.addWidget(QLabel("مبلغ الضريبة:"), 1, 0)
        self.tax_amount_label = QLabel("0.00 ج.م")
        self.tax_amount_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #333;")
        layout.addWidget(self.tax_amount_label, 1, 1)
        
        # الإجمالي
        layout.addWidget(QLabel("الإجمالي:"), 1, 2)
        self.total_label = QLabel("0.00 ج.م")
        self.total_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #d32f2f;")
        layout.addWidget(self.total_label, 1, 3)
        
        return group
    
    def create_buttons(self):
        """إنشاء أزرار الحوار"""
        layout = QHBoxLayout()
        layout.addStretch()
        
        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: #f44336;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }
            QPushButton:hover {
                background: #d32f2f;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        # زر الحفظ
        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet(f"""
            QPushButton {{
                background: {AppStyles.PRIMARY_COLOR};
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background: {AppStyles.HIGHLIGHT_COLOR};
            }}
        """)
        save_btn.clicked.connect(self.accept)
        
        layout.addWidget(cancel_btn)
        layout.addWidget(save_btn)
        
        return layout
    
    def get_input_style(self):
        """الحصول على نمط عناصر الإدخال"""
        return f"""
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {{
                padding: 10px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 14px;
                background: white;
            }}
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {{
                border-color: {AppStyles.PRIMARY_COLOR};
            }}
        """
    
    def add_item(self):
        """إضافة صنف جديد"""
        name = self.item_name_input.text().strip()
        quantity = self.item_quantity_input.value()
        price = self.item_price_input.value()
        
        if not name:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الصنف")
            return
        
        if price <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر صحيح")
            return
        
        total = quantity * price
        
        item = {
            "name": name,
            "quantity": quantity,
            "unit_price": price,
            "total": total
        }
        
        self.items_data.append(item)
        self.refresh_items_table()
        self.calculate_totals()
        
        # مسح الحقول
        self.item_name_input.clear()
        self.item_quantity_input.setValue(1)
        self.item_price_input.setValue(0.00)
    
    def refresh_items_table(self):
        """تحديث جدول الأصناف"""
        self.items_table.setRowCount(len(self.items_data))
        
        for row, item in enumerate(self.items_data):
            self.items_table.setItem(row, 0, QTableWidgetItem(item["name"]))
            self.items_table.setItem(row, 1, QTableWidgetItem(str(item["quantity"])))
            self.items_table.setItem(row, 2, QTableWidgetItem(f"{item['unit_price']:.2f} ج.م"))
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{item['total']:.2f} ج.م"))
            
            # زر الحذف
            delete_btn = QPushButton("🗑️")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background: #f44336;
                    color: white;
                    border: none;
                    padding: 5px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background: #d32f2f;
                }
            """)
            delete_btn.clicked.connect(lambda checked, r=row: self.delete_item(r))
            self.items_table.setCellWidget(row, 4, delete_btn)
    
    def delete_item(self, row):
        """حذف صنف"""
        if 0 <= row < len(self.items_data):
            del self.items_data[row]
            self.refresh_items_table()
            self.calculate_totals()
    
    def calculate_totals(self):
        """حساب المجاميع"""
        subtotal = sum(item["total"] for item in self.items_data)
        tax_rate = self.tax_input.value()
        tax_amount = subtotal * (tax_rate / 100)
        total = subtotal + tax_amount
        
        self.subtotal_label.setText(f"{subtotal:,.2f} ج.م")
        self.tax_amount_label.setText(f"{tax_amount:,.2f} ج.م")
        self.total_label.setText(f"{total:,.2f} ج.م")
    
    def load_invoice_data(self):
        """تحميل بيانات الفاتورة للتعديل"""
        self.invoice_number_input.setText(self.invoice_data["invoice_number"])
        self.date_input.setDate(QDate.fromString(self.invoice_data["date"], "yyyy-MM-dd"))
        self.supplier_combo.setCurrentText(self.invoice_data["supplier"])
        
        # تحميل الأصناف
        self.items_data = self.invoice_data["items"].copy()
        self.refresh_items_table()
        self.calculate_totals()
    
    def get_invoice_data(self):
        """الحصول على بيانات الفاتورة"""
        subtotal = sum(item["total"] for item in self.items_data)
        tax_rate = self.tax_input.value()
        tax_amount = subtotal * (tax_rate / 100)
        total = subtotal + tax_amount
        
        return {
            "invoice_number": self.invoice_number_input.text(),
            "date": self.date_input.date().toString("yyyy-MM-dd"),
            "supplier": self.supplier_combo.currentText(),
            "total_amount": total,
            "paid_amount": 0,  # يمكن إضافة حقل للمبلغ المدفوع
            "remaining_amount": total,
            "status": "غير مدفوعة",
            "items": self.items_data.copy()
        }