"""
ويدجت الرسوم البيانية
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QFrame, QPushButton, QGridLayout)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QPainter, QColor, QPen, QBrush, QFont
import math
import random

from accounting_app.ui.styles import AppStyles


class ChartsWidget(QWidget):
    """ويدجت الرسوم البيانية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
        # تحديث البيانات كل 30 ثانية للمظاهر المتحركة (تقليل التحديث لتحسين الأداء)
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_charts)
        self.timer.start(30000)  # 30 ثانية بدلاً من 5
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(20)
        
        # إنشاء الرسوم البيانية
        charts_container = QFrame()
        charts_container.setStyleSheet(f"""
            background-color: white;
            border-radius: {AppStyles.BORDER_RADIUS}px;
            padding: 20px;
            border: 1px solid {AppStyles.BORDER_COLOR};
        """)
        charts_layout = QGridLayout(charts_container)
        
        # عنوان القسم
        title_label = QLabel("الرسوم البيانية والإحصائيات")
        title_label.setStyleSheet(f"""
            color: {AppStyles.PRIMARY_COLOR};
            font-size: {AppStyles.FONT_SIZE_LARGE}px;
            font-weight: bold;
            margin-bottom: 20px;
        """)
        
        # رسم بياني دائري للمبيعات
        self.pie_chart = PieChartWidget("توزيع المبيعات", [
            ("إلكترونيات", 45, AppStyles.PRIMARY_COLOR),
            ("مكتبية", 30, AppStyles.SUCCESS_COLOR),
            ("إكسسوارات", 25, AppStyles.WARNING_COLOR)
        ])
        
        # رسم بياني خطي للأرباح
        self.line_chart = LineChartWidget("الأرباح الشهرية", [
            15000, 18000, 22000, 19000, 25000, 28000, 32000, 29000, 35000, 38000, 42000, 45000
        ])
        
        # رسم بياني عمودي للمخزون
        self.bar_chart = BarChartWidget("المخزون الحالي", [
            ("لابتوب", 15, AppStyles.PRIMARY_COLOR),
            ("ماوس", 25, AppStyles.INFO_COLOR),
            ("كيبورد", 12, AppStyles.SUCCESS_COLOR),
            ("شاشة", 8, AppStyles.WARNING_COLOR),
            ("طابعة", 6, AppStyles.ERROR_COLOR)
        ])
        
        # إضافة الرسوم إلى التخطيط
        charts_layout.addWidget(title_label, 0, 0, 1, 2)
        charts_layout.addWidget(self.pie_chart, 1, 0)
        charts_layout.addWidget(self.line_chart, 1, 1)
        charts_layout.addWidget(self.bar_chart, 2, 0, 1, 2)
        
        main_layout.addWidget(charts_container)
    
    def update_charts(self):
        """تحديث البيانات في الرسوم البيانية"""
        # تحديث بيانات وهمية لإظهار التفاعل
        if hasattr(self, 'line_chart'):
            self.line_chart.update_data([
                random.randint(10000, 50000) for _ in range(12)
            ])
        
        if hasattr(self, 'bar_chart'):
            self.bar_chart.update_data([
                ("لابتوب", random.randint(5, 25), AppStyles.PRIMARY_COLOR),
                ("ماوس", random.randint(10, 35), AppStyles.INFO_COLOR),
                ("كيبورد", random.randint(8, 20), AppStyles.SUCCESS_COLOR),
                ("شاشة", random.randint(3, 15), AppStyles.WARNING_COLOR),
                ("طابعة", random.randint(2, 12), AppStyles.ERROR_COLOR)
            ])


class PieChartWidget(QWidget):
    """رسم بياني دائري"""
    
    def __init__(self, title, data, parent=None):
        super().__init__(parent)
        self.title = title
        self.data = data
        self.setMinimumSize(300, 250)
        
    def paintEvent(self, event):
        """رسم الرسم البياني الدائري"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # رسم العنوان
        painter.setPen(QColor(AppStyles.TEXT_PRIMARY))
        painter.setFont(QFont("Arial", 12, QFont.Bold))
        painter.drawText(10, 20, self.title)
        
        # حساب المجموع الكلي
        total = sum(item[1] for item in self.data)
        
        # رسم الدائرة
        rect_size = 150
        center_x = self.width() // 2 - rect_size // 2
        center_y = self.height() // 2 - rect_size // 2 + 15
        
        start_angle = 0
        for i, (label, value, color) in enumerate(self.data):
            span_angle = int((value / total) * 360 * 16)  # Qt uses 1/16 degrees
            
            painter.setBrush(QBrush(QColor(color)))
            painter.setPen(QPen(QColor("white"), 2))
            painter.drawPie(center_x, center_y, rect_size, rect_size, start_angle, span_angle)
            
            start_angle += span_angle
        
        # رسم وسائل الإيضاح
        legend_y = center_y + rect_size + 20
        for i, (label, value, color) in enumerate(self.data):
            percentage = (value / total) * 100
            
            # رسم المربع الملون
            painter.setBrush(QBrush(QColor(color)))
            painter.setPen(QPen(QColor(color)))
            painter.drawRect(10, legend_y + i * 20, 15, 15)
            
            # رسم النص
            painter.setPen(QColor(AppStyles.TEXT_PRIMARY))
            painter.setFont(QFont("Arial", 10))
            painter.drawText(35, legend_y + i * 20 + 12, f"{label}: {percentage:.1f}%")


class LineChartWidget(QWidget):
    """رسم بياني خطي"""
    
    def __init__(self, title, data, parent=None):
        super().__init__(parent)
        self.title = title
        self.data = data
        self.setMinimumSize(300, 250)
        
    def update_data(self, new_data):
        """تحديث البيانات"""
        self.data = new_data
        self.update()
    
    def paintEvent(self, event):
        """رسم الرسم البياني الخطي"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # رسم العنوان
        painter.setPen(QColor(AppStyles.TEXT_PRIMARY))
        painter.setFont(QFont("Arial", 12, QFont.Bold))
        painter.drawText(10, 20, self.title)
        
        if not self.data:
            return
        
        # حساب النطاقات
        margin = 40
        chart_width = self.width() - 2 * margin
        chart_height = self.height() - 2 * margin - 30
        
        max_value = max(self.data)
        min_value = min(self.data)
        value_range = max_value - min_value if max_value != min_value else 1
        
        # رسم المحاور
        painter.setPen(QPen(QColor(AppStyles.BORDER_COLOR), 2))
        painter.drawLine(margin, self.height() - margin, self.width() - margin, self.height() - margin)  # X axis
        painter.drawLine(margin, margin + 30, margin, self.height() - margin)  # Y axis
        
        # رسم الخط
        points = []
        for i, value in enumerate(self.data):
            x = margin + (i / (len(self.data) - 1)) * chart_width
            y = self.height() - margin - ((value - min_value) / value_range) * chart_height
            points.append((x, y))
        
        # رسم الخط
        painter.setPen(QPen(QColor(AppStyles.PRIMARY_COLOR), 3))
        for i in range(len(points) - 1):
            painter.drawLine(points[i][0], points[i][1], points[i+1][0], points[i+1][1])
        
        # رسم النقاط
        painter.setBrush(QBrush(QColor(AppStyles.PRIMARY_COLOR)))
        for x, y in points:
            painter.drawEllipse(x - 4, y - 4, 8, 8)


class BarChartWidget(QWidget):
    """رسم بياني عمودي"""
    
    def __init__(self, title, data, parent=None):
        super().__init__(parent)
        self.title = title
        self.data = data
        self.setMinimumSize(400, 200)
        
    def update_data(self, new_data):
        """تحديث البيانات"""
        self.data = new_data
        self.update()
    
    def paintEvent(self, event):
        """رسم الرسم البياني العمودي"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # رسم العنوان
        painter.setPen(QColor(AppStyles.TEXT_PRIMARY))
        painter.setFont(QFont("Arial", 12, QFont.Bold))
        painter.drawText(10, 20, self.title)
        
        if not self.data:
            return
        
        # حساب النطاقات
        margin = 50
        chart_width = self.width() - 2 * margin
        chart_height = self.height() - 2 * margin - 30
        
        max_value = max(item[1] for item in self.data)
        bar_width = chart_width / len(self.data) - 10
        
        # رسم الأعمدة
        for i, (label, value, color) in enumerate(self.data):
            x = margin + i * (chart_width / len(self.data)) + 5
            bar_height = (value / max_value) * chart_height
            y = self.height() - margin - bar_height
            
            # رسم العمود
            painter.setBrush(QBrush(QColor(color)))
            painter.setPen(QPen(QColor(color)))
            painter.drawRect(x, y, bar_width, bar_height)
            
            # رسم القيمة فوق العمود
            painter.setPen(QColor(AppStyles.TEXT_PRIMARY))
            painter.setFont(QFont("Arial", 9))
            painter.drawText(x, y - 5, str(value))
            
            # رسم التسمية أسفل العمود
            painter.drawText(x, self.height() - margin + 15, label[:6])