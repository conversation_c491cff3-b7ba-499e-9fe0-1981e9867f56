"""
نافذة لوحة التحكم الرئيسية
"""
import os
from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QStackedWidget, QToolBar, QStatusBar,
                              QMenu, QMessageBox, QSizePolicy, QApplication)
from PySide6.QtCore import Qt, QSize, Signal
from PySide6.QtGui import QIcon, QPixmap, QFont, QAction, QColor

from accounting_app.bll.user_manager import UserManager
from accounting_app.bll.translator import Translator
from accounting_app.bll.settings_manager import SettingsManager
from accounting_app.ui.styles import AppStyles


class DashboardWindow(QMainWindow):
    """نافذة لوحة التحكم الرئيسية"""
    
    def __init__(self, user_data, parent=None):
        """
        إنشاء نافذة لوحة التحكم
        :param user_data: بيانات المستخدم
        :param parent: النافذة الأم
        """
        try:
            super().__init__(parent)
            self.user_data = user_data
            self.translator = Translator()
            self.settings_manager = SettingsManager()
            
            # تهيئة واجهة المستخدم
            self.init_ui()
        except Exception as e:
            print("Error in DashboardWindow.__init__:", e)
            import traceback
            traceback.print_exc()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        try:
            print("Setting window title")
            # إعداد النافذة
            self.setWindowTitle(self.translator.translate('app_name'))
            print("Setting window size")
            self.setMinimumSize(1200, 800)
            print("Setting window style")
            self.setStyleSheet(AppStyles.MAIN_WINDOW_STYLE)
            
            print("Creating toolbar")
            # إنشاء شريط الأدوات
            self.create_toolbar()
            
            print("Creating status bar")
            # إنشاء شريط الحالة
            self.create_status_bar()
            
            print("Creating main layout")
            # إنشاء التخطيط الرئيسي
            main_widget = QWidget()
            self.setCentralWidget(main_widget)
            main_layout = QHBoxLayout(main_widget)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)
            
            print("Creating sidebar")
            # إنشاء القائمة الجانبية
            self.sidebar_widget = self.create_sidebar()
            
            print("Creating content area")
            # إنشاء منطقة المحتوى
            self.content_widget = self.create_content_area()
            
            # إضافة العناصر إلى التخطيط الرئيسي
            main_layout.addWidget(self.sidebar_widget, 1)
            main_layout.addWidget(self.content_widget, 4)
            
            print("UI initialization completed")
        except Exception as e:
            print("Error in init_ui:", e)
            import traceback
            traceback.print_exc()
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        try:
            print("Creating toolbar")
            toolbar = QToolBar("شريط الأدوات الرئيسي")
            toolbar.setMovable(False)
            toolbar.setIconSize(QSize(24, 24))
            toolbar.setStyleSheet(AppStyles.TOOLBAR_STYLE)
            
            print("Adding settings action")
            # إضافة أيقونة الإعدادات
            settings_action = QAction(self.translator.translate('settings'), self)
            settings_action.triggered.connect(self.show_settings)
            toolbar.addAction(settings_action)
            
            print("Adding logout action")
            # إضافة أيقونة تسجيل الخروج
            logout_action = QAction(self.translator.translate('logout'), self)
            logout_action.triggered.connect(self.logout)
            toolbar.addAction(logout_action)
            
            print("Adding toolbar to main window")
            self.addToolBar(toolbar)
            print("Toolbar created successfully")
        except Exception as e:
            print("Error in create_toolbar:", e)
            import traceback
            traceback.print_exc()
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        try:
            print("Creating status bar")
            status_bar = QStatusBar()
            status_bar.setStyleSheet(AppStyles.STATUSBAR_STYLE)
            
            print("Adding username label to status bar")
            # إضافة اسم المستخدم إلى شريط الحالة
            print(f"User data: {self.user_data}")
            username = self.user_data[3] if self.user_data[3] else self.user_data[1]
            print(f"Username: {username}")
            welcome_text = self.translator.translate('welcome')
            print(f"Welcome text: {welcome_text}")
            username_label = QLabel(f"{welcome_text}, {username}")
            status_bar.addPermanentWidget(username_label)
            
            print("Setting status bar")
            self.setStatusBar(status_bar)
            print("Status bar created successfully")
        except Exception as e:
            print("Error in create_status_bar:", e)
            import traceback
            traceback.print_exc()
    
    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        try:
            print("Creating sidebar widget")
            sidebar_widget = QWidget()
            sidebar_widget.setObjectName("sidebar")
            sidebar_widget.setStyleSheet(f"""
                #sidebar {{
                    background: {AppStyles.SIDEBAR_GRADIENT};
                    min-width: 280px;
                    max-width: 280px;
                    border-right: 1px solid rgba(255, 255, 255, 0.1);
                }}
            """)
            sidebar_layout = QVBoxLayout(sidebar_widget)
            sidebar_layout.setContentsMargins(0, 0, 0, 0)
            sidebar_layout.setSpacing(0)
            
            print("Creating logo widget")
            # إنشاء شعار التطبيق - تصميم عصري
            logo_widget = QWidget()
            logo_widget.setStyleSheet(f"""
                background: rgba(255, 255, 255, 0.1);
                min-height: 120px;
                padding: 20px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            """)
            logo_layout = QVBoxLayout(logo_widget)
            logo_layout.setAlignment(Qt.AlignCenter)
            
            # أيقونة التطبيق
            icon_label = QLabel("💼")
            icon_label.setStyleSheet(f"""
                font-size: 40px;
                color: white;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 25px;
                padding: 10px;
                max-width: 50px;
                max-height: 50px;
            """)
            icon_label.setAlignment(Qt.AlignCenter)
            
            # إضافة شعار التطبيق
            logo_label = QLabel(self.translator.translate('app_name'))
            logo_label.setStyleSheet(f"""
                color: white;
                font-size: {AppStyles.FONT_SIZE_LARGE}px;
                font-weight: bold;
                margin-top: 10px;
            """)
            logo_label.setAlignment(Qt.AlignCenter)
            
            logo_layout.addWidget(icon_label)
            logo_layout.addWidget(logo_label)
            
            print("Creating menu widget")
            # إنشاء قائمة التنقل
            menu_widget = QWidget()
            menu_widget.setStyleSheet(f"""
                padding: 10px;
            """)
            menu_layout = QVBoxLayout(menu_widget)
            menu_layout.setSpacing(5)
            
            print("Creating menu buttons")
            # إنشاء أزرار القائمة
            # زر لوحة التحكم
            dashboard_btn = self.create_menu_button(self.translator.translate('dashboard'), self.show_dashboard)
            
            # زر إدارة المنتجات
            products_btn = self.create_menu_button(self.translator.translate('products_management'), self.show_products)
            
            # زر إدارة المخزون
            inventory_btn = self.create_menu_button(self.translator.translate('inventory_management'), self.show_inventory)
            
            # زر فواتير البيع
            sales_btn = self.create_menu_button(self.translator.translate('sales_invoices'), self.show_sales)
            
            # زر فواتير الشراء
            purchases_btn = self.create_menu_button(self.translator.translate('purchase_invoices'), self.show_purchases)
            
            # زر إدارة العملاء
            customers_btn = self.create_menu_button(self.translator.translate('customers_management'), self.show_customers)
            
            # زر إدارة الموردين
            suppliers_btn = self.create_menu_button(self.translator.translate('suppliers_management'), self.show_suppliers)
            
            # زر التقارير
            reports_btn = self.create_menu_button(self.translator.translate('reports'), self.show_reports)
            
            # زر الإعدادات
            settings_btn = self.create_menu_button(self.translator.translate('settings'), self.show_settings)
            
            # إضافة الأزرار إلى تخطيط القائمة
            menu_layout.addWidget(dashboard_btn)
            menu_layout.addWidget(products_btn)
            menu_layout.addWidget(inventory_btn)
            menu_layout.addWidget(sales_btn)
            menu_layout.addWidget(purchases_btn)
            menu_layout.addWidget(customers_btn)
            menu_layout.addWidget(suppliers_btn)
            menu_layout.addWidget(reports_btn)
            menu_layout.addWidget(settings_btn)
            menu_layout.addStretch()
            
            print("Creating logout button")
            # إضافة زر تسجيل الخروج
            logout_btn = self.create_menu_button(self.translator.translate('logout'), self.logout)
            logout_btn.setStyleSheet(f"""
                background-color: {AppStyles.ERROR_COLOR};
                color: white;
                border: none;
                padding: 10px;
                text-align: center;
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
            """)
            
            # إضافة العناصر إلى تخطيط القائمة الجانبية
            sidebar_layout.addWidget(logo_widget)
            sidebar_layout.addWidget(menu_widget)
            sidebar_layout.addWidget(logout_btn)
            
            print("Sidebar created successfully")
            return sidebar_widget
        except Exception as e:
            print("Error in create_sidebar:", e)
            import traceback
            traceback.print_exc()
            return QWidget()
    
    def create_menu_button(self, text, callback):
        """
        إنشاء زر قائمة
        :param text: نص الزر
        :param callback: الدالة التي سيتم استدعاؤها عند النقر على الزر
        :return: زر القائمة
        """
        try:
            button = QPushButton(text)
            button.setStyleSheet(AppStyles.MENU_BUTTON_STYLE)
            button.setCursor(Qt.PointingHandCursor)
            button.clicked.connect(callback)
            return button
        except Exception as e:
            print(f"Error in create_menu_button for {text}:", e)
            import traceback
            traceback.print_exc()
            return QPushButton(text)
    
    def create_content_area(self):
        """إنشاء منطقة المحتوى"""
        try:
            print("Creating content widget")
            self.content_widget = QWidget()
            self.content_widget.setStyleSheet(f"""
                background-color: {AppStyles.BACKGROUND_COLOR};
            """)
            
            content_layout = QVBoxLayout(self.content_widget)
            content_layout.setContentsMargins(20, 20, 20, 20)
            
            print("Creating page title")
            # إنشاء عنوان الصفحة
            self.page_title = QLabel("لوحة التحكم")
            self.page_title.setStyleSheet(f"""
                color: {AppStyles.PRIMARY_COLOR};
                font-size: {AppStyles.FONT_SIZE_XXLARGE}px;
                font-weight: bold;
                margin-bottom: 20px;
            """)
            
            print("Creating content container")
            # إنشاء حاوية المحتوى
            self.content_container = QStackedWidget()
            
            # إضافة العناصر إلى تخطيط المحتوى
            content_layout.addWidget(self.page_title)
            content_layout.addWidget(self.content_container)
            
            print("Creating dashboard page")
            # إنشاء صفحات المحتوى
            self.create_dashboard_page()
            print("Content area created successfully")
            # هنا يمكنك إضافة المزيد من الصفحات لاحقًا
            return self.content_widget
        except Exception as e:
            print("Error in create_content_area:", e)
            import traceback
            traceback.print_exc()
            return QWidget()
    
    def create_dashboard_page(self):
        """إنشاء صفحة لوحة التحكم"""
        try:
            print("Creating dashboard widget")
            dashboard_widget = QWidget()
            dashboard_layout = QVBoxLayout(dashboard_widget)
            dashboard_layout.setContentsMargins(20, 20, 20, 20)
            dashboard_layout.setSpacing(20)
            
            # إنشاء إطار الترحيب
            welcome_frame = QFrame()
            welcome_frame.setStyleSheet(f"""
                background-color: {AppStyles.PRIMARY_COLOR};
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 20px;
                color: white;
            """)
            welcome_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
            welcome_layout = QHBoxLayout(welcome_frame)
            
            # إضافة رسالة الترحيب والمعلومات
            welcome_info = QWidget()
            welcome_info_layout = QVBoxLayout(welcome_info)
            welcome_info_layout.setContentsMargins(0, 0, 0, 0)
            
            # إضافة رسالة ترحيب
            welcome_label = QLabel(f"مرحبًا بك، {self.user_data[3] if self.user_data[3] else self.user_data[1]}!")
            welcome_label.setStyleSheet(f"""
                color: white;
                font-size: {AppStyles.FONT_SIZE_XXLARGE}px;
                font-weight: bold;
            """)
            
            # إضافة التاريخ والوقت
            from datetime import datetime
            current_date = datetime.now().strftime("%Y/%m/%d")
            date_label = QLabel(f"اليوم: {current_date}")
            date_label.setStyleSheet(f"""
                color: rgba(255, 255, 255, 0.8);
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
            """)
            
            # إضافة وصف للنظام
            description_label = QLabel("مرحبًا بك في نظام المحاسبة المتكامل. استخدم لوحة التحكم للوصول إلى جميع وظائف النظام بسهولة.")
            description_label.setWordWrap(True)
            description_label.setStyleSheet(f"""
                color: rgba(255, 255, 255, 0.9);
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                margin-top: 10px;
            """)
            
            welcome_info_layout.addWidget(welcome_label)
            welcome_info_layout.addWidget(date_label)
            welcome_info_layout.addWidget(description_label)
            
            # إضافة أيقونة أو صورة
            icon_label = QLabel("🏪")
            icon_label.setStyleSheet(f"""
                font-size: 80px;
                color: white;
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 50px;
                padding: 20px;
            """)
            icon_label.setFixedSize(120, 120)
            icon_label.setAlignment(Qt.AlignCenter)
            
            welcome_layout.addWidget(welcome_info, 7)
            welcome_layout.addWidget(icon_label, 3)
            
            print("Creating stats widget")
            # إنشاء البطاقات الإحصائية
            stats_widget = QWidget()
            stats_layout = QHBoxLayout(stats_widget)
            stats_layout.setSpacing(15)
            
            # بطاقة إجمالي المبيعات - تصميم محسن
            sales_card = self.create_modern_stat_card("إجمالي المبيعات", "45,500.00", "ج.م", AppStyles.PRIMARY_COLOR, "📈", "+15%", "من الشهر الماضي")
            
            # بطاقة إجمالي المشتريات
            purchases_card = self.create_modern_stat_card("إجمالي المشتريات", "28,200.00", "ج.م", AppStyles.INFO_COLOR, "🛒", "+8%", "من الشهر الماضي")
            
            # بطاقة الأرباح
            profit_card = self.create_modern_stat_card("صافي الأرباح", "17,300.00", "ج.م", AppStyles.SUCCESS_COLOR, "💰", "+22%", "من الشهر الماضي")
            
            # بطاقة المنتجات
            products_card = self.create_modern_stat_card("إجمالي المنتجات", "248", "منتج", AppStyles.WARNING_COLOR, "📦", "+12", "منتج جديد")
            
            # إضافة البطاقات إلى تخطيط الإحصائيات
            stats_layout.addWidget(sales_card)
            stats_layout.addWidget(purchases_card)
            stats_layout.addWidget(profit_card)
            stats_layout.addWidget(products_card)
            
            # إنشاء قسم الوصول السريع
            quick_access_frame = QFrame()
            quick_access_frame.setStyleSheet(f"""
                background-color: white;
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 20px;
            """)
            quick_access_layout = QVBoxLayout(quick_access_frame)
            
            # عنوان قسم الوصول السريع
            quick_access_title = QLabel("الوصول السريع")
            quick_access_title.setStyleSheet(f"""
                color: {AppStyles.PRIMARY_COLOR};
                font-size: {AppStyles.FONT_SIZE_LARGE}px;
                font-weight: bold;
                margin-bottom: 10px;
            """)
            
            # أزرار الوصول السريع
            quick_buttons_widget = QWidget()
            quick_buttons_layout = QHBoxLayout(quick_buttons_widget)
            quick_buttons_layout.setSpacing(15)
            
            # إنشاء أزرار الوصول السريع
            new_sale_btn = self.create_quick_access_button("فاتورة بيع جديدة", "📝", AppStyles.PRIMARY_COLOR, self.show_sales)
            new_purchase_btn = self.create_quick_access_button("فاتورة شراء جديدة", "🧾", AppStyles.SECONDARY_COLOR, self.show_purchases)
            new_product_btn = self.create_quick_access_button("إضافة منتج جديد", "➕", AppStyles.INFO_COLOR, self.show_products)
            reports_btn = self.create_quick_access_button("عرض التقارير", "📊", AppStyles.ACCENT_COLOR, self.show_reports)
            
            quick_buttons_layout.addWidget(new_sale_btn)
            quick_buttons_layout.addWidget(new_purchase_btn)
            quick_buttons_layout.addWidget(new_product_btn)
            quick_buttons_layout.addWidget(reports_btn)
            
            quick_access_layout.addWidget(quick_access_title)
            quick_access_layout.addWidget(quick_buttons_widget)
            
            # إنشاء قسم آخر المعاملات
            recent_transactions_frame = QFrame()
            recent_transactions_frame.setStyleSheet(f"""
                background-color: white;
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 20px;
            """)
            recent_transactions_layout = QVBoxLayout(recent_transactions_frame)
            
            # عنوان قسم آخر المعاملات
            recent_transactions_title = QLabel("آخر المعاملات")
            recent_transactions_title.setStyleSheet(f"""
                color: {AppStyles.PRIMARY_COLOR};
                font-size: {AppStyles.FONT_SIZE_LARGE}px;
                font-weight: bold;
                margin-bottom: 10px;
            """)
            
            # جدول آخر المعاملات (عرض وهمي)
            transactions_table = self.create_sample_transactions_table()
            
            recent_transactions_layout.addWidget(recent_transactions_title)
            recent_transactions_layout.addWidget(transactions_table)
            
            # إنشاء تخطيط شبكي للرسوم البيانية والإحصائيات
            middle_section = QWidget()
            middle_layout = QHBoxLayout(middle_section)
            middle_layout.setSpacing(20)
            
            # الرسوم البيانية
            try:
                from accounting_app.ui.charts_widget import ChartsWidget
                charts_widget = ChartsWidget()
                charts_widget.setMaximumHeight(400)
            except Exception as e:
                print(f"Error loading charts: {e}")
                charts_widget = QLabel("الرسوم البيانية قيد التحميل...")
                charts_widget.setStyleSheet(f"""
                    background-color: white;
                    border-radius: {AppStyles.BORDER_RADIUS}px;
                    padding: 50px;
                    color: {AppStyles.TEXT_SECONDARY};
                    font-size: {AppStyles.FONT_SIZE_LARGE}px;
                    border: 1px solid {AppStyles.BORDER_COLOR};
                """)
                charts_widget.setAlignment(Qt.AlignCenter)
            
            # معلومات سريعة إضافية
            quick_info_widget = self.create_quick_info_panel()
            
            middle_layout.addWidget(charts_widget, 2)
            middle_layout.addWidget(quick_info_widget, 1)
            
            # إضافة العناصر إلى تخطيط لوحة التحكم
            dashboard_layout.addWidget(welcome_frame)
            dashboard_layout.addWidget(stats_widget)
            dashboard_layout.addWidget(quick_access_frame)
            dashboard_layout.addWidget(middle_section)
            dashboard_layout.addWidget(recent_transactions_frame)
            
            print("Adding dashboard page to content container")
            # إضافة صفحة لوحة التحكم إلى حاوية المحتوى
            self.content_container.addWidget(dashboard_widget)
            print("Dashboard page created successfully")
        except Exception as e:
            print("Error in create_dashboard_page:", e)
            import traceback
            traceback.print_exc()
    
    def create_quick_access_button(self, text, icon, color, callback):
        """
        إنشاء زر وصول سريع
        :param text: نص الزر
        :param icon: أيقونة الزر
        :param color: لون الزر
        :param callback: الدالة التي سيتم استدعاؤها عند النقر على الزر
        :return: إطار الزر
        """
        try:
            button_frame = QFrame()
            button_frame.setCursor(Qt.PointingHandCursor)
            button_frame.setStyleSheet(f"""
                background-color: white;
                border: 1px solid {color}30;
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 15px;
            """)
            button_frame.setMinimumWidth(150)
            button_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
            
            # تخطيط الزر
            button_layout = QVBoxLayout(button_frame)
            button_layout.setAlignment(Qt.AlignCenter)
            
            # أيقونة الزر
            icon_label = QLabel(icon)
            icon_label.setStyleSheet(f"""
                font-size: 32px;
                color: {color};
                background-color: {color}20;
                border-radius: 25px;
                padding: 10px;
            """)
            icon_label.setFixedSize(50, 50)
            icon_label.setAlignment(Qt.AlignCenter)
            
            # نص الزر
            text_label = QLabel(text)
            text_label.setStyleSheet(f"""
                color: {AppStyles.TEXT_PRIMARY};
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                font-weight: bold;
                margin-top: 10px;
            """)
            text_label.setAlignment(Qt.AlignCenter)
            
            button_layout.addWidget(icon_label, alignment=Qt.AlignCenter)
            button_layout.addWidget(text_label, alignment=Qt.AlignCenter)
            
            # ربط حدث النقر بالدالة المحددة
            button_frame.mousePressEvent = lambda event: callback()
            
            return button_frame
        except Exception as e:
            print("Error in create_quick_access_button:", e)
            import traceback
            traceback.print_exc()
            return QFrame()
            
    def create_sample_transactions_table(self):
        """
        إنشاء جدول وهمي لآخر المعاملات
        :return: جدول المعاملات
        """
        try:
            from PySide6.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView
            
            # إنشاء الجدول
            table = QTableWidget()
            table.setColumnCount(5)
            table.setRowCount(5)
            
            # تعيين عناوين الأعمدة
            headers = ["رقم الفاتورة", "النوع", "التاريخ", "العميل/المورد", "المبلغ"]
            table.setHorizontalHeaderLabels(headers)
            
            # تعيين بيانات وهمية
            data = [
                ["INV-001", "بيع", "2023/06/20", "أحمد محمد", "1,200.00 ج.م"],
                ["INV-002", "بيع", "2023/06/19", "محمد علي", "850.00 ج.م"],
                ["PUR-001", "شراء", "2023/06/18", "شركة الأمل", "2,500.00 ج.م"],
                ["INV-003", "بيع", "2023/06/17", "سارة أحمد", "720.00 ج.م"],
                ["PUR-002", "شراء", "2023/06/16", "مؤسسة النور", "1,800.00 ج.م"]
            ]
            
            # إضافة البيانات إلى الجدول
            for row, row_data in enumerate(data):
                for col, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(cell_data)
                    # تعيين لون خلفية مختلف حسب نوع المعاملة
                    if col == 1:
                        if cell_data == "بيع":
                            item.setBackground(QColor(AppStyles.PRIMARY_COLOR + "20"))
                        else:
                            item.setBackground(QColor(AppStyles.SECONDARY_COLOR + "20"))
                    table.setItem(row, col, item)
            
            # تنسيق الجدول
            table.setStyleSheet(AppStyles.TABLE_STYLE)
            table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
            table.setAlternatingRowColors(True)
            table.setSelectionBehavior(QTableWidget.SelectRows)
            table.setEditTriggers(QTableWidget.NoEditTriggers)
            
            return table
        except Exception as e:
            print("Error in create_sample_transactions_table:", e)
            import traceback
            traceback.print_exc()
            from PySide6.QtWidgets import QLabel
            return QLabel("حدث خطأ في عرض الجدول")
    
    def show_dashboard(self):
        """عرض صفحة لوحة التحكم"""
        try:
            self.page_title.setText("لوحة التحكم")
            
            # التحقق مما إذا كانت صفحة لوحة التحكم موجودة بالفعل
            if hasattr(self, 'home_page'):
                self.content_container.setCurrentWidget(self.home_page)
                return
                
            # إنشاء صفحة لوحة التحكم
            self.home_page = QWidget()
            home_layout = QVBoxLayout(self.home_page)
            
            # إنشاء صف البطاقات الإحصائية
            stats_container = QFrame()
            stats_container.setStyleSheet(f"""
                background-color: white;
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 15px;
                border: 1px solid {AppStyles.BORDER_COLOR};
            """)
            stats_layout = QHBoxLayout(stats_container)
            
            # إنشاء بطاقات الإحصائيات
            total_sales_card = self.create_stat_card("إجمالي المبيعات", "125,000 ج.م", AppStyles.PRIMARY_COLOR, "📈", "+15% من الشهر الماضي")
            total_purchases_card = self.create_stat_card("إجمالي المشتريات", "85,000 ج.م", AppStyles.INFO_COLOR, "🛒", "+8% من الشهر الماضي")
            total_profit_card = self.create_stat_card("صافي الربح", "40,000 ج.م", AppStyles.SUCCESS_COLOR, "💰", "+22% من الشهر الماضي")
            total_customers_card = self.create_stat_card("عدد العملاء", "48", AppStyles.ACCENT_COLOR, "👥", "+5 عملاء جدد")
            
            stats_layout.addWidget(total_sales_card)
            stats_layout.addWidget(total_purchases_card)
            stats_layout.addWidget(total_profit_card)
            stats_layout.addWidget(total_customers_card)
            
            # إنشاء جدول أحدث المعاملات
            transactions_container = QFrame()
            transactions_container.setStyleSheet(f"""
                background-color: white;
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 15px;
                border: 1px solid {AppStyles.BORDER_COLOR};
            """)
            transactions_layout = QVBoxLayout(transactions_container)
            
            transactions_title = QLabel("أحدث المعاملات")
            transactions_title.setStyleSheet(f"""
                color: {AppStyles.PRIMARY_COLOR};
                font-size: {AppStyles.FONT_SIZE_LARGE}px;
                font-weight: bold;
            """)
            
            # إنشاء جدول المعاملات
            from PySide6.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView
            
            transactions_table = QTableWidget()
            transactions_table.setColumnCount(5)
            transactions_table.setRowCount(5)
            
            # تعيين عناوين الأعمدة
            headers = ["رقم الفاتورة", "التاريخ", "العميل", "المبلغ", "الحالة"]
            transactions_table.setHorizontalHeaderLabels(headers)
            
            # تعيين بيانات وهمية
            data = [
                ["INV-1001", "2023/06/15", "أحمد محمد", "12,500.00 ج.م", "مدفوعة"],
                ["INV-1002", "2023/06/14", "محمد علي", "8,750.00 ج.م", "مدفوعة"],
                ["INV-1003", "2023/06/13", "سارة أحمد", "5,200.00 ج.م", "مدفوعة جزئياً"],
                ["INV-1004", "2023/06/12", "خالد محمود", "15,000.00 ج.م", "غير مدفوعة"],
                ["INV-1005", "2023/06/11", "نورا السيد", "3,800.00 ج.م", "مدفوعة"]
            ]
            
            # إضافة البيانات إلى الجدول
            for row, row_data in enumerate(data):
                for col, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(cell_data)
                    # تعيين لون خلفية مختلف حسب حالة الفاتورة
                    if col == 4:
                        if cell_data == "مدفوعة":
                            item.setBackground(QColor(AppStyles.SUCCESS_COLOR + "20"))
                        elif cell_data == "مدفوعة جزئياً":
                            item.setBackground(QColor(AppStyles.WARNING_COLOR + "20"))
                        else:
                            item.setBackground(QColor(AppStyles.ERROR_COLOR + "20"))
                    transactions_table.setItem(row, col, item)
            
            # تنسيق الجدول
            transactions_table.setStyleSheet(AppStyles.TABLE_STYLE)
            transactions_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
            transactions_table.setAlternatingRowColors(True)
            transactions_table.setSelectionBehavior(QTableWidget.SelectRows)
            transactions_table.setEditTriggers(QTableWidget.NoEditTriggers)
            
            # إضافة العناصر إلى تخطيط المعاملات
            transactions_layout.addWidget(transactions_title)
            transactions_layout.addWidget(transactions_table)
            
            # إضافة العناصر إلى تخطيط لوحة التحكم
            home_layout.addWidget(stats_container)
            home_layout.addWidget(transactions_container)
            
            # إضافة الصفحة إلى حاوية المحتوى
            self.content_container.addWidget(self.home_page)
            self.content_container.setCurrentWidget(self.home_page)
        except Exception as e:
            print("Error in show_dashboard:", e)
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض لوحة التحكم: {str(e)}")
    
    def create_stat_card(self, title, value, color, icon="", subtitle=""):
        """
        إنشاء بطاقة إحصائية
        :param title: عنوان البطاقة
        :param value: قيمة البطاقة
        :param color: لون البطاقة
        :param icon: أيقونة البطاقة
        :param subtitle: نص إضافي أسفل القيمة
        :return: إطار البطاقة
        """
        try:
            card_frame = QFrame()
            card_frame.setStyleSheet(f"""
                background-color: white;
                border-radius: {AppStyles.BORDER_RADIUS}px;
                box-shadow: {AppStyles.SHADOW_SMALL};
            """)
            card_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
            card_frame.setMinimumHeight(180)
            
            card_layout = QVBoxLayout(card_frame)
            card_layout.setContentsMargins(20, 20, 20, 20)
            
            # إنشاء الجزء العلوي من البطاقة (العنوان والأيقونة)
            header_widget = QWidget()
            header_layout = QHBoxLayout(header_widget)
            header_layout.setContentsMargins(0, 0, 0, 0)
            
            title_label = QLabel(title)
            title_label.setStyleSheet(f"""
                color: {AppStyles.TEXT_SECONDARY};
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                font-weight: bold;
            """)
            
            icon_label = QLabel(icon)
            icon_label.setStyleSheet(f"""
                font-size: 24px;
                color: {color};
                background-color: {color}20;
                border-radius: 15px;
                padding: 5px;
            """)
            icon_label.setFixedSize(30, 30)
            icon_label.setAlignment(Qt.AlignCenter)
            
            header_layout.addWidget(title_label, 7)
            header_layout.addWidget(icon_label, 3)
            
            # إنشاء القيمة
            value_label = QLabel(value)
            value_label.setStyleSheet(f"""
                color: {AppStyles.TEXT_PRIMARY};
                font-size: {AppStyles.FONT_SIZE_XXLARGE}px;
                font-weight: bold;
            """)
            
            # إنشاء النص الإضافي
            subtitle_label = QLabel(subtitle)
            subtitle_label.setStyleSheet(f"""
                color: {color};
                font-size: {AppStyles.FONT_SIZE_SMALL}px;
                font-weight: bold;
            """)
            
            # إضافة خط أفقي
            line = QFrame()
            line.setFrameShape(QFrame.HLine)
            line.setStyleSheet(f"background-color: {color}30;")
            line.setFixedHeight(2)
            
            # إنشاء زر "عرض التفاصيل"
            details_btn = QPushButton("عرض التفاصيل")
            details_btn.setCursor(Qt.PointingHandCursor)
            details_btn.setStyleSheet(f"""
                background-color: transparent;
                color: {color};
                border: none;
                text-align: left;
                padding: 5px 0;
                font-size: {AppStyles.FONT_SIZE_SMALL}px;
            """)
            
            # إضافة العناصر إلى تخطيط البطاقة
            card_layout.addWidget(header_widget)
            card_layout.addWidget(value_label)
            card_layout.addWidget(subtitle_label)
            card_layout.addWidget(line)
            card_layout.addWidget(details_btn)
            
            return card_frame
        except Exception as e:
            print("Error in create_stat_card:", e)
            import traceback
            traceback.print_exc()
            return QFrame()
    
    def create_modern_stat_card(self, title, value, unit, color, icon, change, change_desc):
        """
        إنشاء بطاقة إحصائية عصرية
        :param title: عنوان البطاقة
        :param value: القيمة الرئيسية
        :param unit: وحدة القياس
        :param color: لون البطاقة
        :param icon: أيقونة البطاقة
        :param change: نسبة التغيير
        :param change_desc: وصف التغيير
        :return: إطار البطاقة
        """
        try:
            card_frame = QFrame()
            card_frame.setStyleSheet(f"""
                QFrame {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                        stop:0 {color}, 
                        stop:1 {AppStyles.HIGHLIGHT_COLOR});
                    border-radius: {AppStyles.BORDER_RADIUS}px;
                    border: 1px solid rgba(255, 255, 255, 0.1);
                }}
            """)
            card_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
            card_frame.setMinimumHeight(160)
            
            card_layout = QVBoxLayout(card_frame)
            card_layout.setContentsMargins(20, 20, 20, 20)
            card_layout.setSpacing(10)
            
            # الصف العلوي - العنوان والأيقونة
            header_widget = QWidget()
            header_layout = QHBoxLayout(header_widget)
            header_layout.setContentsMargins(0, 0, 0, 0)
            
            title_label = QLabel(title)
            title_label.setStyleSheet(f"""
                color: white;
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                font-weight: 600;
                opacity: 0.9;
            """)
            
            icon_label = QLabel(icon)
            icon_label.setStyleSheet(f"""
                font-size: 28px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 20px;
                padding: 8px;
                max-width: 40px;
                max-height: 40px;
            """)
            icon_label.setAlignment(Qt.AlignCenter)
            
            header_layout.addWidget(title_label)
            header_layout.addStretch()
            header_layout.addWidget(icon_label)
            
            # الصف الأوسط - القيمة الرئيسية
            value_widget = QWidget()
            value_layout = QHBoxLayout(value_widget)
            value_layout.setContentsMargins(0, 0, 0, 0)
            
            value_label = QLabel(value)
            value_label.setStyleSheet(f"""
                color: white;
                font-size: {AppStyles.FONT_SIZE_XXLARGE}px;
                font-weight: bold;
            """)
            
            unit_label = QLabel(unit)
            unit_label.setStyleSheet(f"""
                color: white;
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                font-weight: 500;
                opacity: 0.8;
                margin-left: 5px;
            """)
            
            value_layout.addWidget(value_label)
            value_layout.addWidget(unit_label)
            value_layout.addStretch()
            
            # الصف السفلي - التغيير
            change_widget = QWidget()
            change_layout = QHBoxLayout(change_widget)
            change_layout.setContentsMargins(0, 0, 0, 0)
            
            change_label = QLabel(change)
            change_label.setStyleSheet(f"""
                color: white;
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                font-weight: bold;
                background: rgba(255, 255, 255, 0.2);
                padding: 4px 8px;
                border-radius: 8px;
            """)
            
            desc_label = QLabel(change_desc)
            desc_label.setStyleSheet(f"""
                color: white;
                font-size: {AppStyles.FONT_SIZE_SMALL}px;
                opacity: 0.8;
                margin-left: 10px;
            """)
            
            change_layout.addWidget(change_label)
            change_layout.addWidget(desc_label)
            change_layout.addStretch()
            
            # إضافة العناصر إلى البطاقة
            card_layout.addWidget(header_widget)
            card_layout.addWidget(value_widget)
            card_layout.addStretch()
            card_layout.addWidget(change_widget)
            
            return card_frame
        except Exception as e:
            print("Error in create_modern_stat_card:", e)
            import traceback
            traceback.print_exc()
            return QFrame()
    
    def create_quick_info_panel(self):
        """إنشاء لوحة المعلومات السريعة"""
        try:
            panel = QFrame()
            panel.setStyleSheet(f"""
                background-color: white;
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 20px;
                border: 1px solid {AppStyles.BORDER_COLOR};
            """)
            
            layout = QVBoxLayout(panel)
            
            # عنوان اللوحة
            title = QLabel("معلومات سريعة")
            title.setStyleSheet(f"""
                color: {AppStyles.PRIMARY_COLOR};
                font-size: {AppStyles.FONT_SIZE_LARGE}px;
                font-weight: bold;
                margin-bottom: 20px;
            """)
            
            # معلومات سريعة
            info_items = [
                ("📊", "إجمالي المعاملات اليوم", "45"),
                ("👥", "عملاء جدد هذا الأسبوع", "12"),
                ("📦", "منتجات تحتاج إعادة طلب", "8"),
                ("💰", "متوسط قيمة الفاتورة", "2,150 ج.م"),
                ("🎯", "نسبة تحقيق الهدف الشهري", "78%"),
            ]
            
            for icon, text, value in info_items:
                item_widget = QWidget()
                item_layout = QHBoxLayout(item_widget)
                item_layout.setContentsMargins(0, 5, 0, 5)
                
                # الأيقونة
                icon_label = QLabel(icon)
                icon_label.setStyleSheet(f"""
                    font-size: 20px;
                    min-width: 30px;
                """)
                
                # النص
                text_label = QLabel(text)
                text_label.setStyleSheet(f"""
                    color: {AppStyles.TEXT_SECONDARY};
                    font-size: {AppStyles.FONT_SIZE_SMALL}px;
                """)
                
                # القيمة
                value_label = QLabel(value)
                value_label.setStyleSheet(f"""
                    color: {AppStyles.PRIMARY_COLOR};
                    font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                    font-weight: bold;
                """)
                
                item_layout.addWidget(icon_label)
                item_layout.addWidget(text_label, 1)
                item_layout.addWidget(value_label)
                
                layout.addWidget(item_widget)
            
            layout.addWidget(title)
            for item in info_items:
                item_widget = QWidget()
                item_layout = QHBoxLayout(item_widget)
                item_layout.setContentsMargins(0, 5, 0, 5)
                
                icon_label = QLabel(item[0])
                icon_label.setStyleSheet("font-size: 20px; min-width: 30px;")
                
                text_widget = QWidget()
                text_layout = QVBoxLayout(text_widget)
                text_layout.setContentsMargins(0, 0, 0, 0)
                text_layout.setSpacing(2)
                
                text_label = QLabel(item[1])
                text_label.setStyleSheet(f"""
                    color: {AppStyles.TEXT_SECONDARY};
                    font-size: {AppStyles.FONT_SIZE_SMALL}px;
                """)
                
                value_label = QLabel(item[2])
                value_label.setStyleSheet(f"""
                    color: {AppStyles.PRIMARY_COLOR};
                    font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                    font-weight: bold;
                """)
                
                text_layout.addWidget(text_label)
                text_layout.addWidget(value_label)
                
                item_layout.addWidget(icon_label)
                item_layout.addWidget(text_widget)
                
                layout.addWidget(item_widget)
                
                # خط فاصل
                if item != info_items[-1]:
                    line = QFrame()
                    line.setFrameShape(QFrame.HLine)
                    line.setStyleSheet(f"color: {AppStyles.BORDER_COLOR};")
                    layout.addWidget(line)
            
            layout.addStretch()
            
            return panel
        except Exception as e:
            print("Error in create_quick_info_panel:", e)
            return QFrame()
    
    def show_products(self):
        """عرض صفحة إدارة المنتجات"""
        try:
            self.page_title.setText(self.translator.translate('products_management'))
            
            # التحقق مما إذا كانت صفحة المنتجات موجودة بالفعل
            if hasattr(self, 'products_page'):
                self.content_container.setCurrentWidget(self.products_page)
                return
            
            # إنشاء صفحة المنتجات
            from accounting_app.ui.products_window import ProductsWindow
            self.products_page = ProductsWindow()
            
            # إضافة الصفحة إلى حاوية المحتوى
            self.content_container.addWidget(self.products_page)
            self.content_container.setCurrentWidget(self.products_page)
        except Exception as e:
            print("Error in show_products:", e)
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض صفحة المنتجات: {str(e)}")
    
    def show_inventory(self):
        """عرض صفحة إدارة المخزون"""
        try:
            self.page_title.setText(self.translator.translate('inventory_management'))
            
            # التحقق مما إذا كانت صفحة المخزون موجودة بالفعل
            if hasattr(self, 'inventory_page'):
                self.content_container.setCurrentWidget(self.inventory_page)
                return
            
            # إنشاء صفحة المخزون
            from accounting_app.ui.inventory_window import InventoryWindow
            self.inventory_page = InventoryWindow()
            
            # إضافة الصفحة إلى حاوية المحتوى
            self.content_container.addWidget(self.inventory_page)
            self.content_container.setCurrentWidget(self.inventory_page)
        except Exception as e:
            print("Error in show_inventory:", e)
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض صفحة المخزون: {str(e)}")
    
    def show_sales(self):
        """عرض صفحة فواتير البيع"""
        try:
            self.page_title.setText(self.translator.translate('sales_invoices'))
            
            # التحقق مما إذا كانت صفحة المبيعات موجودة بالفعل
            if hasattr(self, 'sales_page'):
                self.content_container.setCurrentWidget(self.sales_page)
                return
            
            # إنشاء صفحة المبيعات
            from accounting_app.ui.sales_window import SalesWindow
            self.sales_page = SalesWindow()
            
            # إضافة الصفحة إلى حاوية المحتوى
            self.content_container.addWidget(self.sales_page)
            self.content_container.setCurrentWidget(self.sales_page)
        except Exception as e:
            print("Error in show_sales:", e)
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض صفحة فواتير البيع: {str(e)}")
    
    def show_purchases(self):
        """عرض صفحة فواتير الشراء"""
        try:
            self.page_title.setText("فواتير الشراء")
            QMessageBox.information(self, "إشعار", "صفحة فواتير الشراء قيد التطوير")
        except Exception as e:
            print("Error in show_purchases:", e)
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض صفحة فواتير الشراء: {str(e)}")
    
    def show_customers(self):
        """عرض صفحة إدارة العملاء"""
        try:
            self.page_title.setText(self.translator.translate('customers_management'))
            
            # التحقق مما إذا كانت صفحة العملاء موجودة بالفعل
            if hasattr(self, 'customers_page'):
                self.content_container.setCurrentWidget(self.customers_page)
                return
            
            # إنشاء صفحة العملاء
            from accounting_app.ui.customers_window import CustomersWindow
            self.customers_page = CustomersWindow()
            
            # إضافة الصفحة إلى حاوية المحتوى
            self.content_container.addWidget(self.customers_page)
            self.content_container.setCurrentWidget(self.customers_page)
        except Exception as e:
            print("Error in show_customers:", e)
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض صفحة العملاء: {str(e)}")
    
    def show_suppliers(self):
        """عرض صفحة إدارة الموردين"""
        try:
            self.page_title.setText("إدارة الموردين")
            QMessageBox.information(self, "إشعار", "صفحة إدارة الموردين قيد التطوير")
        except Exception as e:
            print("Error in show_suppliers:", e)
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض صفحة الموردين: {str(e)}")
    
    def show_reports(self):
        """عرض صفحة التقارير"""
        try:
            self.page_title.setText(self.translator.translate('reports'))
            
            # التحقق مما إذا كانت صفحة التقارير موجودة بالفعل
            if hasattr(self, 'reports_page'):
                self.content_container.setCurrentWidget(self.reports_page)
                return
            
            # إنشاء صفحة التقارير المحسنة
            from accounting_app.ui.fixed_reports_window import ClearReportsWindow
            self.reports_page = ClearReportsWindow()
            
            # إضافة الصفحة إلى حاوية المحتوى
            self.content_container.addWidget(self.reports_page)
            self.content_container.setCurrentWidget(self.reports_page)
        except Exception as e:
            print("Error in show_reports:", e)
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض صفحة التقارير: {str(e)}")
    
    def show_settings(self):
        """عرض صفحة الإعدادات"""
        try:
            self.page_title.setText(self.translator.translate('settings'))
            
            # التحقق مما إذا كانت صفحة الإعدادات موجودة بالفعل
            if hasattr(self, 'settings_page'):
                self.content_container.setCurrentWidget(self.settings_page)
                return
            
            # إنشاء صفحة الإعدادات المحسنة
            from accounting_app.ui.improved_settings_window import ImprovedSettingsWindow
            self.settings_page = ImprovedSettingsWindow()
            
            # إضافة الصفحة إلى حاوية المحتوى
            self.content_container.addWidget(self.settings_page)
            self.content_container.setCurrentWidget(self.settings_page)
        except Exception as e:
            print("Error in show_settings:", e)
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض صفحة الإعدادات: {str(e)}")
    
    def show_purchases(self):
        """عرض صفحة فواتير المشتريات"""
        try:
            self.page_title.setText("فواتير المشتريات")
            
            # التحقق مما إذا كانت صفحة المشتريات موجودة بالفعل
            if hasattr(self, 'purchases_page'):
                self.content_container.setCurrentWidget(self.purchases_page)
                return
            
            # إنشاء صفحة المشتريات
            from accounting_app.ui.purchases_window import PurchasesWindow
            self.purchases_page = PurchasesWindow()
            
            # إضافة الصفحة إلى حاوية المحتوى
            self.content_container.addWidget(self.purchases_page)
            self.content_container.setCurrentWidget(self.purchases_page)
        except Exception as e:
            print("Error in show_purchases:", e)
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض صفحة المشتريات: {str(e)}")
    
    def show_suppliers(self):
        """عرض صفحة الموردين"""
        try:
            self.page_title.setText("إدارة الموردين")
            
            # التحقق مما إذا كانت صفحة الموردين موجودة بالفعل
            if hasattr(self, 'suppliers_page'):
                self.content_container.setCurrentWidget(self.suppliers_page)
                return
            
            # إنشاء صفحة الموردين
            from accounting_app.ui.suppliers_window import SuppliersWindow
            self.suppliers_page = SuppliersWindow()
            
            # إضافة الصفحة إلى حاوية المحتوى
            self.content_container.addWidget(self.suppliers_page)
            self.content_container.setCurrentWidget(self.suppliers_page)
        except Exception as e:
            print("Error in show_suppliers:", e)
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض صفحة الموردين: {str(e)}")
    
    def logout(self):
        """تسجيل الخروج"""
        try:
            reply = QMessageBox.question(self, "تسجيل الخروج", "هل أنت متأكد من رغبتك في تسجيل الخروج؟",
                                        QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.close()
                # هنا يمكنك إضافة كود للعودة إلى شاشة تسجيل الدخول
        except Exception as e:
            print("Error in logout:", e)
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تسجيل الخروج: {str(e)}")
    
    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        try:
            # عرض رسالة تأكيد قبل الإغلاق
            reply = QMessageBox.question(
                self, 
                "تأكيد الإغلاق", 
                "هل تريد فعلاً إغلاق نظام المحاسبة؟\n\nستفقد أي تغييرات غير محفوظة.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # حفظ الإعدادات الأخيرة
                self.save_window_state()
                
                # إيقاف جميع المؤقتات
                if hasattr(self, 'update_timer'):
                    self.update_timer.stop()
                
                # قبول الإغلاق
                event.accept()
                
                # إغلاق التطبيق بالكامل
                QApplication.instance().quit()
            else:
                # رفض الإغلاق
                event.ignore()
                
        except Exception as e:
            print("Error in closeEvent:", e)
            # في حالة الخطأ، السماح بالإغلاق
            event.accept()
            QApplication.instance().quit()
    
    def save_window_state(self):
        """حفظ حالة النافذة"""
        try:
            # يمكنك إضافة كود لحفظ موقع وحجم النافذة هنا
            print("تم حفظ حالة النافذة")
        except Exception as e:
            print("Error saving window state:", e)
    
    def close_application(self):
        """إغلاق التطبيق بالكامل"""
        try:
            reply = QMessageBox.question(
                self,
                "تأكيد الإغلاق",
                "هل تريد فعلاً إغلاق نظام المحاسبة؟\n\nسيتم إغلاق جميع النوافذ والعمليات.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # حفظ البيانات الأخيرة
                self.save_window_state()
                
                # إيقاف المؤقتات
                if hasattr(self, 'update_timer'):
                    self.update_timer.stop()
                
                # إغلاق التطبيق
                QApplication.instance().quit()
                
        except Exception as e:
            print("Error in close_application:", e)
            QApplication.instance().quit()