#!/usr/bin/env python3
"""
🚀 نظام المحاسبة المتقدم - الإصدار النهائي
Advanced Accounting System - Final Version

تطبيق محاسبة شامل ومتطور مع واجهة مستخدم حديثة
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def print_banner():
    """طباعة شعار التطبيق"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║    💼 نظام المحاسبة المتقدم - Advanced Accounting System     ║
    ║                                                              ║
    ║    🌟 الإصدار 2.1 - Version 2.1                            ║
    ║    🏢 شركة التطوير المتقدم - Advanced Development Co.       ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_system_requirements():
    """فحص متطلبات النظام"""
    print("🔍 فحص متطلبات النظام...")
    print("🔍 Checking system requirements...")
    
    requirements = {
        "Python": sys.version_info >= (3, 8),
        "PySide6": False
    }
    
    try:
        import PySide6
        requirements["PySide6"] = True
        print("✅ PySide6 متوفر - PySide6 available")
    except ImportError:
        print("❌ PySide6 غير متوفر - PySide6 not available")
        print("💡 قم بتثبيته: pip install PySide6")
        print("💡 Install it: pip install PySide6")
        return False
    
    if requirements["Python"]:
        print(f"✅ Python {sys.version.split()[0]} متوفر")
    else:
        print("❌ Python 3.8+ مطلوب")
        return False
    
    print("✅ جميع المتطلبات متوفرة!")
    print("✅ All requirements satisfied!")
    return True

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    print_banner()
    
    # فحص المتطلبات
    if not check_system_requirements():
        input("\nاضغط Enter للخروج - Press Enter to exit...")
        return
    
    try:
        print("\n🚀 بدء تشغيل التطبيق...")
        print("🚀 Starting application...")
        
        # استيراد المكتبات المطلوبة
        from PySide6.QtWidgets import (QApplication, QMainWindow, QStackedWidget, 
                                      QHBoxLayout, QWidget, QMessageBox, QSplashScreen)
        from PySide6.QtCore import Qt, QTimer
        from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QBrush, QLinearGradient
        
        print("✅ تم تحميل المكتبات الأساسية")
        
        # استيراد واجهات التطبيق
        from accounting_app.ui.enhanced_sidebar import EnhancedSidebar
        from accounting_app.ui.enhanced_dashboard import EnhancedDashboard
        from accounting_app.ui.enhanced_products_window import EnhancedProductsWindow
        from accounting_app.ui.enhanced_reports_window import EnhancedReportsWindow
        from accounting_app.ui.enhanced_settings_window import EnhancedSettingsWindow
        from accounting_app.ui.simple_purchases_window import SimplePurchasesWindow
        
        print("✅ تم تحميل جميع الواجهات")
        
        class SplashScreen(QSplashScreen):
            """شاشة البداية"""
            def __init__(self):
                # إنشاء صورة للشاشة
                pixmap = QPixmap(500, 300)
                pixmap.fill(Qt.transparent)
                
                painter = QPainter(pixmap)
                painter.setRenderHint(QPainter.Antialiasing)
                
                # خلفية متدرجة
                gradient = QLinearGradient(0, 0, 500, 300)
                gradient.setColorAt(0, QColor("#667eea"))
                gradient.setColorAt(0.5, QColor("#764ba2"))
                gradient.setColorAt(1, QColor("#f093fb"))
                
                painter.fillRect(pixmap.rect(), QBrush(gradient))
                
                # النص
                painter.setPen(QColor("white"))
                painter.setFont(QFont("Arial", 20, QFont.Bold))
                painter.drawText(pixmap.rect(), Qt.AlignCenter, 
                               "💼\nنظام المحاسبة المتقدم\nAdvanced Accounting System\n\nجاري التحميل...")
                
                painter.end()
                
                super().__init__(pixmap)
                self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        
        class MainWindow(QMainWindow):
            """النافذة الرئيسية للتطبيق"""
            
            def __init__(self):
                super().__init__()
                # بيانات مستخدم افتراضية
                self.user_data = (1, "admin", "admin123", "المدير العام", "<EMAIL>")
                self.current_page = "dashboard"
                self.init_ui()
            
            def init_ui(self):
                """إعداد الواجهة الرئيسية"""
                self.setWindowTitle("💼 نظام المحاسبة المتقدم - Advanced Accounting System v2.1")
                self.setMinimumSize(1400, 900)
                
                # محاولة تكبير النافذة
                try:
                    self.showMaximized()
                except:
                    self.resize(1400, 900)
                    self.center_window()
                
                # إعداد الواجهة الرئيسية
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                
                main_layout = QHBoxLayout(central_widget)
                main_layout.setContentsMargins(0, 0, 0, 0)
                main_layout.setSpacing(0)
                
                # الشريط الجانبي المحسن
                self.sidebar = EnhancedSidebar(self.user_data)
                self.sidebar.menuItemClicked.connect(self.on_menu_item_clicked)
                main_layout.addWidget(self.sidebar)
                
                # منطقة المحتوى
                self.content_stack = QStackedWidget()
                self.content_stack.setStyleSheet("""
                    QStackedWidget {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #f8f9fa, stop:1 #e9ecef);
                        border: none;
                    }
                """)
                main_layout.addWidget(self.content_stack)
                
                # إنشاء الصفحات
                self.create_pages()
                
                # عرض لوحة التحكم افتراضياً
                self.show_page("dashboard")
                
                print("✅ تم إعداد الواجهة الرئيسية بنجاح!")
            
            def center_window(self):
                """توسيط النافذة على الشاشة"""
                try:
                    from PySide6.QtWidgets import QApplication
                    screen = QApplication.primaryScreen().geometry()
                    window = self.geometry()
                    x = (screen.width() - window.width()) // 2
                    y = (screen.height() - window.height()) // 2
                    self.move(x, y)
                except:
                    pass
            
            def create_pages(self):
                """إنشاء جميع صفحات التطبيق"""
                try:
                    print("📄 إنشاء الصفحات...")
                    
                    # لوحة التحكم
                    self.dashboard = EnhancedDashboard(self.user_data)
                    self.content_stack.addWidget(self.dashboard)
                    print("  ✅ لوحة التحكم")
                    
                    # صفحة المنتجات
                    self.products_page = EnhancedProductsWindow()
                    self.content_stack.addWidget(self.products_page)
                    print("  ✅ صفحة المنتجات")
                    
                    # صفحة المشتريات
                    self.purchases_page = SimplePurchasesWindow()
                    self.content_stack.addWidget(self.purchases_page)
                    print("  ✅ صفحة المشتريات")
                    
                    # صفحة التقارير
                    self.reports_page = EnhancedReportsWindow()
                    self.content_stack.addWidget(self.reports_page)
                    print("  ✅ صفحة التقارير")
                    
                    # صفحة الإعدادات
                    self.settings_page = EnhancedSettingsWindow()
                    self.content_stack.addWidget(self.settings_page)
                    print("  ✅ صفحة الإعدادات")
                    
                    # خريطة الصفحات
                    self.pages_map = {
                        "dashboard": self.dashboard,
                        "inventory": self.products_page,
                        "purchases": self.purchases_page,
                        "reports": self.reports_page,
                        "settings": self.settings_page
                    }
                    
                    print("✅ تم إنشاء جميع الصفحات بنجاح!")
                    
                except Exception as e:
                    print(f"❌ خطأ في إنشاء الصفحات: {e}")
                    QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء الصفحات:\n{e}")
            
            def on_menu_item_clicked(self, page_name):
                """معالج النقر على عناصر القائمة"""
                if page_name == "logout":
                    self.logout()
                else:
                    self.show_page(page_name)
            
            def show_page(self, page_name):
                """عرض صفحة معينة"""
                if page_name in self.pages_map:
                    page_widget = self.pages_map[page_name]
                    self.content_stack.setCurrentWidget(page_widget)
                    self.current_page = page_name
                    
                    # تحديث الشريط الجانبي
                    self.sidebar.set_active_page(page_name)
                    
                    print(f"📄 تم عرض صفحة: {page_name}")
                else:
                    QMessageBox.information(
                        self, "قريباً", 
                        f"صفحة {page_name} قيد التطوير وستكون متاحة قريباً!\n\n"
                        f"Page {page_name} is under development and will be available soon!"
                    )
            
            def logout(self):
                """تسجيل الخروج"""
                reply = QMessageBox.question(
                    self, "تسجيل الخروج - Logout",
                    "هل تريد الخروج من النظام؟\nDo you want to exit the system?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    print("👋 تم تسجيل الخروج - Logged out")
                    self.close()
            
            def closeEvent(self, event):
                """عند إغلاق النافذة"""
                print("👋 إغلاق التطبيق - Closing application...")
                event.accept()
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("نظام المحاسبة المتقدم")
        app.setApplicationVersion("2.1")
        app.setOrganizationName("شركة التطوير المتقدم")
        
        # تعيين خط افتراضي يدعم العربية
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        
        # شاشة البداية
        splash = SplashScreen()
        splash.show()
        app.processEvents()
        
        # تأخير لعرض شاشة البداية
        QTimer.singleShot(2000, splash.close)
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        
        # إظهار النافذة بعد إغلاق شاشة البداية
        QTimer.singleShot(2100, main_window.show)
        
        # طباعة معلومات النجاح
        print("\n" + "="*60)
        print("🎉 تم تشغيل التطبيق بنجاح!")
        print("🎉 Application launched successfully!")
        print("="*60)
        print("📋 معلومات التطبيق - Application Info:")
        print("   📱 الاسم - Name: نظام المحاسبة المتقدم")
        print("   🔢 الإصدار - Version: 2.1")
        print("   👤 المستخدم - User: المدير العام")
        print("   🌐 اللغة - Language: العربية/English")
        print("="*60)
        print("💡 نصائح الاستخدام - Usage Tips:")
        print("   • استخدم الشريط الجانبي للتنقل بين الصفحات")
        print("   • Use the sidebar to navigate between pages")
        print("   • جميع البيانات تجريبية للعرض")
        print("   • All data is demo for display purposes")
        print("   • يمكنك تجربة جميع الميزات بأمان")
        print("   • You can safely try all features")
        print("="*60)
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"\n❌ خطأ في الاستيراد - Import error: {e}")
        print("💡 تأكد من تثبيت المتطلبات:")
        print("💡 Make sure to install requirements:")
        print("   pip install PySide6")
        input("\nاضغط Enter للخروج - Press Enter to exit...")
        
    except Exception as e:
        print(f"\n❌ خطأ عام - General error: {e}")
        import traceback
        traceback.print_exc()
        input("\nاضغط Enter للخروج - Press Enter to exit...")

if __name__ == "__main__":
    main()
