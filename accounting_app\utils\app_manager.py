"""
مدير التطبيق - إدارة دورة حياة التطبيق والإعدادات
"""
import os
import json
from PySide6.QtCore import QSettings, QStandardPaths
from PySide6.QtWidgets import QApplication


class AppManager:
    """مدير التطبيق الرئيسي"""
    
    def __init__(self):
        self.settings = QSettings("AccountingApp", "Settings")
        self.app_data_dir = self.get_app_data_directory()
        
    def get_app_data_directory(self):
        """الحصول على مجلد بيانات التطبيق"""
        try:
            # إنشاء مجلد البيانات إذا لم يكن موجوداً
            data_dir = os.path.join(
                QStandardPaths.writableLocation(QStandardPaths.AppDataLocation),
                "AccountingApp"
            )
            
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
                
            return data_dir
        except Exception as e:
            print(f"Error creating app data directory: {e}")
            return os.path.join(os.path.expanduser("~"), ".accounting_app")
    
    def save_window_geometry(self, window_name, geometry, state=None):
        """حفظ موقع وحجم النافذة"""
        try:
            self.settings.setValue(f"{window_name}/geometry", geometry)
            if state:
                self.settings.setValue(f"{window_name}/state", state)
            self.settings.sync()
        except Exception as e:
            print(f"Error saving window geometry: {e}")
    
    def restore_window_geometry(self, window, window_name):
        """استعادة موقع وحجم النافذة"""
        try:
            geometry = self.settings.value(f"{window_name}/geometry")
            state = self.settings.value(f"{window_name}/state")
            
            if geometry:
                window.restoreGeometry(geometry)
            if state:
                window.restoreState(state)
        except Exception as e:
            print(f"Error restoring window geometry: {e}")
    
    def save_user_preferences(self, preferences):
        """حفظ تفضيلات المستخدم"""
        try:
            prefs_file = os.path.join(self.app_data_dir, "preferences.json")
            with open(prefs_file, 'w', encoding='utf-8') as f:
                json.dump(preferences, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving preferences: {e}")
    
    def load_user_preferences(self):
        """تحميل تفضيلات المستخدم"""
        try:
            prefs_file = os.path.join(self.app_data_dir, "preferences.json")
            if os.path.exists(prefs_file):
                with open(prefs_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"Error loading preferences: {e}")
            return {}
    
    def cleanup_on_exit(self):
        """تنظيف الموارد عند الإغلاق"""
        try:
            # حفظ الإعدادات الأخيرة
            self.settings.sync()
            
            # تنظيف الملفات المؤقتة إذا لزم الأمر
            temp_dir = os.path.join(self.app_data_dir, "temp")
            if os.path.exists(temp_dir):
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
                
            print("تم تنظيف موارد التطبيق بنجاح")
        except Exception as e:
            print(f"Error during cleanup: {e}")
    
    @staticmethod
    def safe_exit():
        """خروج آمن من التطبيق"""
        try:
            app = QApplication.instance()
            if app:
                # إنشاء مدير التطبيق للتنظيف
                manager = AppManager()
                manager.cleanup_on_exit()
                
                # إغلاق التطبيق
                app.quit()
        except Exception as e:
            print(f"Error during safe exit: {e}")
            # إغلاق قسري في حالة الخطأ
            if QApplication.instance():
                QApplication.instance().quit()