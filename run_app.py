#!/usr/bin/env python3
"""
تشغيل نظام المحاسبة المتقدم
Advanced Accounting System Launcher
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_requirements():
    """فحص المتطلبات"""
    try:
        import PySide6
        print("✅ PySide6 متوفر")
        return True
    except ImportError:
        print("❌ PySide6 غير متوفر")
        print("💡 قم بتثبيته: pip install PySide6")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل نظام المحاسبة المتقدم...")
    print("🚀 Starting Advanced Accounting System...")
    
    # فحص المتطلبات
    if not check_requirements():
        input("اضغط Enter للخروج...")
        return
    
    try:
        from PySide6.QtWidgets import QApplication, QMainWindow, QStackedWidget, QHBoxLayout, QWidget, QMessageBox
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QFont, QIcon
        
        # استيراد الواجهات
        from accounting_app.ui.enhanced_sidebar import EnhancedSidebar
        from accounting_app.ui.enhanced_dashboard import EnhancedDashboard
        from accounting_app.ui.enhanced_products_window import EnhancedProductsWindow
        from accounting_app.ui.enhanced_reports_window import EnhancedReportsWindow
        from accounting_app.ui.enhanced_settings_window import EnhancedSettingsWindow
        from accounting_app.ui.simple_purchases_window import SimplePurchasesWindow
        
        print("✅ تم تحميل جميع الواجهات بنجاح!")
        
        class MainWindow(QMainWindow):
            """النافذة الرئيسية"""
            
            def __init__(self):
                super().__init__()
                # بيانات مستخدم افتراضية
                self.user_data = (1, "admin", "admin123", "المدير", "<EMAIL>")
                self.current_page = "dashboard"
                self.init_ui()
            
            def init_ui(self):
                """إعداد الواجهة"""
                self.setWindowTitle("💼 نظام المحاسبة المتقدم - Advanced Accounting System v2.1")
                self.setMinimumSize(1400, 900)
                
                # محاولة تكبير النافذة
                try:
                    self.showMaximized()
                except:
                    self.resize(1400, 900)
                
                # الواجهة الرئيسية
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                
                main_layout = QHBoxLayout(central_widget)
                main_layout.setContentsMargins(0, 0, 0, 0)
                main_layout.setSpacing(0)
                
                # الشريط الجانبي المحسن
                self.sidebar = EnhancedSidebar(self.user_data)
                self.sidebar.menuItemClicked.connect(self.on_menu_item_clicked)
                main_layout.addWidget(self.sidebar)
                
                # منطقة المحتوى
                self.content_stack = QStackedWidget()
                self.content_stack.setStyleSheet("""
                    QStackedWidget {
                        background: #f8f9fa;
                        border: none;
                    }
                """)
                main_layout.addWidget(self.content_stack)
                
                # إنشاء الصفحات
                self.create_pages()
                
                # عرض لوحة التحكم افتراضياً
                self.show_page("dashboard")
                
                print("✅ تم إعداد الواجهة الرئيسية بنجاح!")
            
            def create_pages(self):
                """إنشاء جميع صفحات التطبيق"""
                try:
                    # لوحة التحكم
                    self.dashboard = EnhancedDashboard(self.user_data)
                    self.content_stack.addWidget(self.dashboard)
                    print("✅ لوحة التحكم")
                    
                    # صفحة المنتجات
                    self.products_page = EnhancedProductsWindow()
                    self.content_stack.addWidget(self.products_page)
                    print("✅ صفحة المنتجات")
                    
                    # صفحة المشتريات
                    self.purchases_page = SimplePurchasesWindow()
                    self.content_stack.addWidget(self.purchases_page)
                    print("✅ صفحة المشتريات")
                    
                    # صفحة التقارير
                    self.reports_page = EnhancedReportsWindow()
                    self.content_stack.addWidget(self.reports_page)
                    print("✅ صفحة التقارير")
                    
                    # صفحة الإعدادات
                    self.settings_page = EnhancedSettingsWindow()
                    self.content_stack.addWidget(self.settings_page)
                    print("✅ صفحة الإعدادات")
                    
                    # خريطة الصفحات
                    self.pages_map = {
                        "dashboard": self.dashboard,
                        "inventory": self.products_page,
                        "purchases": self.purchases_page,
                        "reports": self.reports_page,
                        "settings": self.settings_page
                    }
                    
                    print("✅ تم إنشاء جميع الصفحات بنجاح!")
                    
                except Exception as e:
                    print(f"❌ خطأ في إنشاء الصفحات: {e}")
                    QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء الصفحات:\n{e}")
            
            def on_menu_item_clicked(self, page_name):
                """معالج النقر على عناصر القائمة"""
                if page_name == "logout":
                    self.logout()
                else:
                    self.show_page(page_name)
            
            def show_page(self, page_name):
                """عرض صفحة معينة"""
                if page_name in self.pages_map:
                    page_widget = self.pages_map[page_name]
                    self.content_stack.setCurrentWidget(page_widget)
                    self.current_page = page_name
                    
                    # تحديث الشريط الجانبي
                    self.sidebar.set_active_page(page_name)
                    
                    print(f"✅ تم عرض صفحة: {page_name}")
                else:
                    QMessageBox.information(
                        self, "قريباً", 
                        f"صفحة {page_name} قيد التطوير وستكون متاحة قريباً!"
                    )
            
            def logout(self):
                """تسجيل الخروج"""
                reply = QMessageBox.question(
                    self, "تسجيل الخروج",
                    "هل تريد الخروج من النظام؟",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    print("👋 تم تسجيل الخروج")
                    self.close()
            
            def closeEvent(self, event):
                """عند إغلاق النافذة"""
                print("👋 إغلاق التطبيق...")
                event.accept()
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("نظام المحاسبة المتقدم")
        app.setApplicationVersion("2.1")
        app.setOrganizationName("شركة التطوير المتقدم")
        
        # تعيين خط افتراضي يدعم العربية
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        main_window.show()
        
        print("\n" + "="*50)
        print("🎉 تم تشغيل التطبيق بنجاح!")
        print("🎉 Application launched successfully!")
        print("="*50)
        print("📋 معلومات التطبيق:")
        print("   📱 الاسم: نظام المحاسبة المتقدم")
        print("   🔢 الإصدار: 2.1")
        print("   👤 المستخدم: المدير")
        print("   🌐 اللغة: العربية/English")
        print("="*50)
        print("💡 نصائح الاستخدام:")
        print("   • استخدم الشريط الجانبي للتنقل بين الصفحات")
        print("   • جميع البيانات تجريبية للعرض")
        print("   • يمكنك تجربة جميع الميزات بأمان")
        print("="*50)
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت المتطلبات:")
        print("   pip install PySide6")
        input("اضغط Enter للخروج...")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
