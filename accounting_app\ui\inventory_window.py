"""
صفحة إدارة المخزون
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QTableWidget, QTableWidgetItem, 
                              QHeaderView, QFrame, QLineEdit, QComboBox,
                              QDialog, QFormLayout, QDialogButtonBox, 
                              QMessageBox, QSpinBox, QProgressBar,
                              QGridLayout, QGroupBox)
from PySide6.QtCore import Qt
from PySide6.QtGui import QColor

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class InventoryWindow(QWidget):
    """صفحة إدارة المخزون"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        try:
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)
            
            # إحصائيات سريعة
            stats_frame = self.create_inventory_stats()
            
            # شريط الأدوات
            toolbar_frame = self.create_toolbar()
            
            # جدول المخزون
            inventory_table = self.create_inventory_table()
            
            # إضافة العناصر
            main_layout.addWidget(stats_frame)
            main_layout.addWidget(toolbar_frame)
            main_layout.addWidget(inventory_table)
        except Exception as e:
            print("Error in InventoryWindow.init_ui:", e)
    
    def create_inventory_stats(self):
        """إنشاء إحصائيات المخزون"""
        try:
            stats_frame = QFrame()
            stats_frame.setStyleSheet(f"""
                background: {AppStyles.HEADER_GRADIENT};
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 20px;
                border: 1px solid {AppStyles.BORDER_COLOR};
            """)
            
            stats_layout = QHBoxLayout(stats_frame)
            
            # عنوان القسم
            title_label = QLabel("إحصائيات المخزون")
            title_label.setStyleSheet(f"""
                color: white;
                font-size: {AppStyles.FONT_SIZE_XLARGE}px;
                font-weight: bold;
            """)
            
            # إجمالي المنتجات
            total_products = self.create_stat_box("إجمالي المنتجات", "248", "📦")
            
            # منتجات منخفضة المخزون
            low_stock = self.create_stat_box("منخفض المخزون", "15", "⚠️")
            
            # منتجات نفدت
            out_of_stock = self.create_stat_box("نفد المخزون", "3", "❌")
            
            # قيمة المخزون
            inventory_value = self.create_stat_box("قيمة المخزون", "850,000", "💰")
            
            stats_layout.addWidget(title_label)
            stats_layout.addStretch()
            stats_layout.addWidget(total_products)
            stats_layout.addWidget(low_stock)
            stats_layout.addWidget(out_of_stock)
            stats_layout.addWidget(inventory_value)
            
            return stats_frame
        except Exception as e:
            print("Error in create_inventory_stats:", e)
            return QFrame()
    
    def create_stat_box(self, title, value, icon):
        """إنشاء صندوق إحصائية"""
        box = QFrame()
        box.setStyleSheet(f"""
            background: rgba(255, 255, 255, 0.2);
            border-radius: {AppStyles.BORDER_RADIUS}px;
            padding: 15px;
            min-width: 120px;
        """)
        
        layout = QVBoxLayout(box)
        layout.setAlignment(Qt.AlignCenter)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            font-size: 24px;
            color: white;
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            color: white;
            font-size: {AppStyles.FONT_SIZE_LARGE}px;
            font-weight: bold;
        """)
        value_label.setAlignment(Qt.AlignCenter)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            color: white;
            font-size: {AppStyles.FONT_SIZE_SMALL}px;
            opacity: 0.9;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(title_label)
        
        return box
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        try:
            toolbar_frame = QFrame()
            toolbar_frame.setStyleSheet(f"""
                background-color: white;
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 15px;
                border: 1px solid {AppStyles.BORDER_COLOR};
            """)
            
            toolbar_layout = QHBoxLayout(toolbar_frame)
            
            # أزرار العمليات
            add_stock_btn = QPushButton("إضافة مخزون")
            add_stock_btn.setStyleSheet(AppStyles.BUTTON_STYLE)
            add_stock_btn.clicked.connect(self.add_stock)
            
            remove_stock_btn = QPushButton("خصم مخزون")
            remove_stock_btn.setStyleSheet(AppStyles.BUTTON_SECONDARY_STYLE)
            remove_stock_btn.clicked.connect(self.remove_stock)
            
            transfer_btn = QPushButton("نقل مخزون")
            transfer_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {AppStyles.INFO_COLOR};
                    color: white;
                    border: none;
                    border-radius: {AppStyles.BORDER_RADIUS}px;
                    padding: 10px 20px;
                    font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: #1976D2;
                }}
            """)
            transfer_btn.clicked.connect(self.transfer_stock)
            
            # مرشحات
            filter_label = QLabel("فلترة:")
            filter_combo = QComboBox()
            filter_combo.addItems(["جميع المنتجات", "منخفض المخزون", "نفد المخزون", "مخزون عادي"])
            filter_combo.setStyleSheet(AppStyles.COMBOBOX_STYLE)
            filter_combo.currentTextChanged.connect(self.filter_inventory)
            
            # البحث
            search_line = QLineEdit()
            search_line.setPlaceholderText("البحث في المنتجات...")
            search_line.setStyleSheet(AppStyles.INPUT_STYLE)
            search_line.textChanged.connect(self.search_inventory)
            
            toolbar_layout.addWidget(add_stock_btn)
            toolbar_layout.addWidget(remove_stock_btn)
            toolbar_layout.addWidget(transfer_btn)
            toolbar_layout.addStretch()
            toolbar_layout.addWidget(filter_label)
            toolbar_layout.addWidget(filter_combo)
            toolbar_layout.addWidget(QLabel("البحث:"))
            toolbar_layout.addWidget(search_line)
            
            return toolbar_frame
        except Exception as e:
            print("Error in create_toolbar:", e)
            return QFrame()
    
    def create_inventory_table(self):
        """إنشاء جدول المخزون"""
        try:
            self.inventory_table = QTableWidget()
            self.inventory_table.setColumnCount(8)
            
            headers = ["كود المنتج", "اسم المنتج", "الفئة", "الكمية الحالية", "الحد الأدنى", 
                      "مستوى المخزون", "آخر حركة", "إجراءات"]
            self.inventory_table.setHorizontalHeaderLabels(headers)
            
            self.inventory_table.setStyleSheet(AppStyles.TABLE_STYLE)
            self.inventory_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
            self.inventory_table.setAlternatingRowColors(True)
            self.inventory_table.setSelectionBehavior(QTableWidget.SelectRows)
            
            self.load_sample_inventory()
            
            return self.inventory_table
        except Exception as e:
            print("Error in create_inventory_table:", e)
            return QTableWidget()
    
    def load_sample_inventory(self):
        """تحميل بيانات المخزون الوهمية"""
        try:
            sample_data = [
                ["P001", "لابتوب HP", "إلكترونيات", "15", "5", "85%", "2024-01-20"],
                ["P002", "ماوس لاسلكي", "إكسسوارات", "8", "10", "45%", "2024-01-19"],
                ["P003", "كيبورد ميكانيكي", "إكسسوارات", "22", "5", "95%", "2024-01-18"],
                ["P004", "شاشة 24 بوصة", "إلكترونيات", "3", "3", "30%", "2024-01-17"],
                ["P005", "طابعة Canon", "مكتبية", "0", "2", "0%", "2024-01-16"],
                ["P006", "هارد خارجي", "إلكترونيات", "25", "8", "90%", "2024-01-15"],
                ["P007", "سماعات", "إكسسوارات", "12", "5", "75%", "2024-01-14"],
                ["P008", "كاميرا ويب", "إلكترونيات", "2", "5", "25%", "2024-01-13"],
            ]
            
            self.inventory_table.setRowCount(len(sample_data))
            
            for row, row_data in enumerate(sample_data):
                for col, cell_data in enumerate(row_data):
                    if col == 5:  # عمود مستوى المخزون
                        # إنشاء شريط تقدم
                        progress_widget = QWidget()
                        progress_layout = QHBoxLayout(progress_widget)
                        progress_layout.setContentsMargins(5, 0, 5, 0)
                        
                        progress_bar = QProgressBar()
                        progress_value = int(cell_data.replace('%', ''))
                        progress_bar.setValue(progress_value)
                        progress_bar.setTextVisible(True)
                        progress_bar.setFormat(f"{progress_value}%")
                        
                        # تلوين الشريط حسب المستوى
                        if progress_value <= 30:
                            color = AppStyles.ERROR_COLOR
                        elif progress_value <= 50:
                            color = AppStyles.WARNING_COLOR
                        else:
                            color = AppStyles.SUCCESS_COLOR
                        
                        progress_bar.setStyleSheet(f"""
                            QProgressBar {{
                                border: 1px solid {AppStyles.BORDER_COLOR};
                                border-radius: 5px;
                                text-align: center;
                                background-color: {AppStyles.BACKGROUND_COLOR};
                            }}
                            QProgressBar::chunk {{
                                background-color: {color};
                                border-radius: 5px;
                            }}
                        """)
                        
                        progress_layout.addWidget(progress_bar)
                        self.inventory_table.setCellWidget(row, col, progress_widget)
                    
                    elif col == 7:  # عمود الإجراءات
                        # إنشاء أزرار الإجراءات
                        actions_widget = QWidget()
                        actions_layout = QHBoxLayout(actions_widget)
                        actions_layout.setContentsMargins(5, 0, 5, 0)
                        
                        view_btn = QPushButton("عرض")
                        view_btn.setStyleSheet(f"""
                            QPushButton {{
                                background-color: {AppStyles.INFO_COLOR};
                                color: white;
                                border: none;
                                border-radius: 4px;
                                padding: 4px 8px;
                                font-size: 10px;
                            }}
                        """)
                        
                        edit_btn = QPushButton("تعديل")
                        edit_btn.setStyleSheet(f"""
                            QPushButton {{
                                background-color: {AppStyles.WARNING_COLOR};
                                color: white;
                                border: none;
                                border-radius: 4px;
                                padding: 4px 8px;
                                font-size: 10px;
                            }}
                        """)
                        
                        actions_layout.addWidget(view_btn)
                        actions_layout.addWidget(edit_btn)
                        self.inventory_table.setCellWidget(row, col, actions_widget)
                    
                    else:
                        item = QTableWidgetItem(cell_data)
                        
                        # تلوين الصفوف حسب مستوى المخزون
                        if col == 3:  # عمود الكمية
                            quantity = int(cell_data)
                            min_quantity = int(row_data[4])
                            
                            if quantity == 0:
                                item.setBackground(QColor(AppStyles.ERROR_COLOR + "30"))
                            elif quantity <= min_quantity:
                                item.setBackground(QColor(AppStyles.WARNING_COLOR + "30"))
                            else:
                                item.setBackground(QColor(AppStyles.SUCCESS_COLOR + "20"))
                        
                        self.inventory_table.setItem(row, col, item)
                        
        except Exception as e:
            print("Error in load_sample_inventory:", e)
    
    def add_stock(self):
        """إضافة مخزون"""
        try:
            dialog = StockAdjustmentDialog(self, "إضافة مخزون")
            if dialog.exec() == QDialog.Accepted:
                QMessageBox.information(self, "نجح", "تم إضافة المخزون بنجاح")
                self.refresh_table()
        except Exception as e:
            print("Error in add_stock:", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    def remove_stock(self):
        """خصم مخزون"""
        try:
            dialog = StockAdjustmentDialog(self, "خصم مخزون")
            if dialog.exec() == QDialog.Accepted:
                QMessageBox.information(self, "نجح", "تم خصم المخزون بنجاح")
                self.refresh_table()
        except Exception as e:
            print("Error in remove_stock:", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    def transfer_stock(self):
        """نقل مخزون"""
        try:
            QMessageBox.information(self, "نقل مخزون", "هذه الميزة قيد التطوير")
        except Exception as e:
            print("Error in transfer_stock:", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    def filter_inventory(self, filter_text):
        """فلترة المخزون"""
        try:
            for row in range(self.inventory_table.rowCount()):
                show_row = True
                
                if filter_text == "منخفض المخزون":
                    quantity_item = self.inventory_table.item(row, 3)
                    min_quantity_item = self.inventory_table.item(row, 4)
                    if quantity_item and min_quantity_item:
                        quantity = int(quantity_item.text())
                        min_quantity = int(min_quantity_item.text())
                        show_row = quantity <= min_quantity and quantity > 0
                
                elif filter_text == "نفد المخزون":
                    quantity_item = self.inventory_table.item(row, 3)
                    if quantity_item:
                        quantity = int(quantity_item.text())
                        show_row = quantity == 0
                
                elif filter_text == "مخزون عادي":
                    quantity_item = self.inventory_table.item(row, 3)
                    min_quantity_item = self.inventory_table.item(row, 4)
                    if quantity_item and min_quantity_item:
                        quantity = int(quantity_item.text())
                        min_quantity = int(min_quantity_item.text())
                        show_row = quantity > min_quantity
                
                self.inventory_table.setRowHidden(row, not show_row)
        except Exception as e:
            print("Error in filter_inventory:", e)
    
    def search_inventory(self, text):
        """البحث في المخزون"""
        try:
            for row in range(self.inventory_table.rowCount()):
                match = False
                for col in range(3):  # البحث في الأعمدة الثلاثة الأولى فقط
                    item = self.inventory_table.item(row, col)
                    if item and text.lower() in item.text().lower():
                        match = True
                        break
                self.inventory_table.setRowHidden(row, not match)
        except Exception as e:
            print("Error in search_inventory:", e)
    
    def refresh_table(self):
        """تحديث الجدول"""
        try:
            self.load_sample_inventory()
        except Exception as e:
            print("Error in refresh_table:", e)


class StockAdjustmentDialog(QDialog):
    """نافذة حوار تعديل المخزون"""
    
    def __init__(self, parent=None, operation_type="إضافة مخزون"):
        super().__init__(parent)
        self.operation_type = operation_type
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        try:
            self.setWindowTitle(self.operation_type)
            self.setMinimumSize(500, 400)
            self.setStyleSheet(f"""
                QDialog {{
                    background-color: {AppStyles.BACKGROUND_COLOR};
                }}
            """)
            
            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)
            
            # عنوان
            title_label = QLabel(self.operation_type)
            title_label.setStyleSheet(f"""
                color: {AppStyles.PRIMARY_COLOR};
                font-size: {AppStyles.FONT_SIZE_LARGE}px;
                font-weight: bold;
            """)
            
            # نموذج البيانات
            form_frame = QFrame()
            form_frame.setStyleSheet(f"""
                background-color: white;
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 20px;
                border: 1px solid {AppStyles.BORDER_COLOR};
            """)
            form_layout = QFormLayout(form_frame)
            
            # اختيار المنتج
            self.product_combo = QComboBox()
            self.product_combo.addItems([
                "اختر المنتج", "لابتوب HP", "ماوس لاسلكي", "كيبورد ميكانيكي", "شاشة 24 بوصة"
            ])
            self.product_combo.setStyleSheet(AppStyles.COMBOBOX_STYLE)
            
            # الكمية
            self.quantity_spin = QSpinBox()
            self.quantity_spin.setMinimum(1)
            self.quantity_spin.setMaximum(9999)
            self.quantity_spin.setStyleSheet(AppStyles.INPUT_STYLE)
            
            # السبب
            self.reason_edit = QLineEdit()
            self.reason_edit.setStyleSheet(AppStyles.INPUT_STYLE)
            
            # ملاحظات
            self.notes_edit = QLineEdit()
            self.notes_edit.setStyleSheet(AppStyles.INPUT_STYLE)
            
            form_layout.addRow("المنتج:", self.product_combo)
            form_layout.addRow("الكمية:", self.quantity_spin)
            form_layout.addRow("السبب:", self.reason_edit)
            form_layout.addRow("ملاحظات:", self.notes_edit)
            
            # أزرار الحوار
            button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            button_box.accepted.connect(self.accept)
            button_box.rejected.connect(self.reject)
            button_box.setStyleSheet(f"""
                QPushButton {{
                    {AppStyles.BUTTON_STYLE}
                    min-width: 80px;
                }}
            """)
            
            main_layout.addWidget(title_label)
            main_layout.addWidget(form_frame)
            main_layout.addWidget(button_box)
            
        except Exception as e:
            print("Error in StockAdjustmentDialog.init_ui:", e)