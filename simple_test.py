#!/usr/bin/env python3
"""
اختبار مبسط للتطبيق
Simple Application Test
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import QApplication, QMainWindow, QStackedWidget, QHBoxLayout, QWidget
    from PySide6.QtCore import Qt
    
    # استيراد الواجهات الأساسية
    from accounting_app.ui.enhanced_sidebar import EnhancedSidebar
    from accounting_app.ui.enhanced_dashboard import EnhancedDashboard
    from accounting_app.ui.enhanced_products_window import EnhancedProductsWindow
    from accounting_app.ui.enhanced_reports_window import EnhancedReportsWindow
    from accounting_app.ui.enhanced_settings_window import EnhancedSettingsWindow
    from accounting_app.ui.simple_purchases_window import SimplePurchasesWindow
    
    print("✅ جميع الواجهات تم استيرادها بنجاح!")
    print("✅ All interfaces imported successfully!")
    
    class SimpleMainWindow(QMainWindow):
        """النافذة الرئيسية المبسطة"""
        
        def __init__(self):
            super().__init__()
            # بيانات مستخدم تجريبية
            self.user_data = (1, "admin", "admin123", "المدير", "<EMAIL>")
            self.init_ui()
        
        def init_ui(self):
            """إعداد الواجهة"""
            self.setWindowTitle("💼 نظام المحاسبة المتقدم - اختبار")
            self.setMinimumSize(1200, 800)
            
            # الواجهة الرئيسية
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            main_layout = QHBoxLayout(central_widget)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)
            
            # الشريط الجانبي
            self.sidebar = EnhancedSidebar(self.user_data)
            self.sidebar.menuItemClicked.connect(self.on_menu_item_clicked)
            main_layout.addWidget(self.sidebar)
            
            # منطقة المحتوى
            self.content_stack = QStackedWidget()
            self.content_stack.setStyleSheet("""
                QStackedWidget {
                    background: #f8f9fa;
                    border: none;
                }
            """)
            main_layout.addWidget(self.content_stack)
            
            # إنشاء الصفحات
            self.create_pages()
            
            # عرض لوحة التحكم افتراضياً
            self.show_page("dashboard")
        
        def create_pages(self):
            """إنشاء جميع صفحات التطبيق"""
            try:
                # لوحة التحكم
                self.dashboard = EnhancedDashboard(self.user_data)
                self.content_stack.addWidget(self.dashboard)
                print("✅ تم إنشاء لوحة التحكم")
                
                # صفحة المنتجات
                self.products_page = EnhancedProductsWindow()
                self.content_stack.addWidget(self.products_page)
                print("✅ تم إنشاء صفحة المنتجات")
                
                # صفحة المشتريات المبسطة
                self.purchases_page = SimplePurchasesWindow()
                self.content_stack.addWidget(self.purchases_page)
                print("✅ تم إنشاء صفحة المشتريات")
                
                # صفحة التقارير
                self.reports_page = EnhancedReportsWindow()
                self.content_stack.addWidget(self.reports_page)
                print("✅ تم إنشاء صفحة التقارير")
                
                # صفحة الإعدادات
                self.settings_page = EnhancedSettingsWindow()
                self.content_stack.addWidget(self.settings_page)
                print("✅ تم إنشاء صفحة الإعدادات")
                
                # خريطة الصفحات
                self.pages_map = {
                    "dashboard": self.dashboard,
                    "inventory": self.products_page,
                    "purchases": self.purchases_page,
                    "reports": self.reports_page,
                    "settings": self.settings_page
                }
                
            except Exception as e:
                print(f"❌ خطأ في إنشاء الصفحات: {e}")
        
        def on_menu_item_clicked(self, page_name):
            """معالج النقر على عناصر القائمة"""
            if page_name == "logout":
                self.close()
            else:
                self.show_page(page_name)
        
        def show_page(self, page_name):
            """عرض صفحة معينة"""
            if page_name in self.pages_map:
                page_widget = self.pages_map[page_name]
                self.content_stack.setCurrentWidget(page_widget)
                print(f"✅ تم عرض صفحة: {page_name}")
                
                # تحديث الشريط الجانبي
                self.sidebar.set_active_page(page_name)
            else:
                print(f"⚠️ صفحة غير موجودة: {page_name}")
    
    # تشغيل التطبيق
    app = QApplication(sys.argv)
    app.setApplicationName("نظام المحاسبة المتقدم - اختبار")
    
    # تعيين خط افتراضي
    from PySide6.QtGui import QFont
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    main_window = SimpleMainWindow()
    main_window.show()
    
    print("🚀 التطبيق يعمل بنجاح!")
    print("🚀 Application running successfully!")
    print("📝 بيانات تسجيل الدخول:")
    print("📝 Login credentials:")
    print("   اسم المستخدم / Username: admin")
    print("   كلمة المرور / Password: admin123")
    
    sys.exit(app.exec())
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print(f"❌ Import error: {e}")
    print("💡 تأكد من تثبيت المتطلبات: pip install PySide6")
    print("💡 Make sure to install requirements: pip install PySide6")
    
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    print(f"❌ General error: {e}")
    import traceback
    traceback.print_exc()
