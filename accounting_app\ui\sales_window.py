"""
صفحة فواتير البيع
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QTableWidget, QTableWidgetItem, 
                              QHeaderView, QFrame, QLineEdit, QComboBox,
                              QDialog, QFormLayout, QDialogButtonBox, 
                              QMessageBox, QDoubleSpinBox, QSpinBox,
                              QDateEdit, QTextEdit, QGridLayout)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QColor

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class SalesWindow(QWidget):
    """صفحة فواتير البيع"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        try:
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)
            
            # شريط الأدوات
            toolbar_frame = self.create_toolbar()
            
            # مرشحات البحث
            filters_frame = self.create_filters()
            
            # جدول الفواتير
            invoices_table = self.create_invoices_table()
            
            # إضافة العناصر
            main_layout.addWidget(toolbar_frame)
            main_layout.addWidget(filters_frame)
            main_layout.addWidget(invoices_table)
        except Exception as e:
            print("Error in SalesWindow.init_ui:", e)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        try:
            toolbar_frame = QFrame()
            toolbar_frame.setStyleSheet(f"""
                background: {AppStyles.HEADER_GRADIENT};
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 20px;
                border: 1px solid {AppStyles.BORDER_COLOR};
            """)
            
            toolbar_layout = QHBoxLayout(toolbar_frame)
            
            # العنوان
            title_label = QLabel("إدارة فواتير البيع")
            title_label.setStyleSheet(f"""
                color: white;
                font-size: {AppStyles.FONT_SIZE_XLARGE}px;
                font-weight: bold;
            """)
            
            # الأزرار
            new_invoice_btn = QPushButton("فاتورة جديدة")
            new_invoice_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: white;
                    color: {AppStyles.PRIMARY_COLOR};
                    border: none;
                    border-radius: {AppStyles.BORDER_RADIUS}px;
                    padding: 12px 24px;
                    font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {AppStyles.BACKGROUND_COLOR};
                }}
            """)
            new_invoice_btn.clicked.connect(self.new_invoice)
            
            edit_btn = QPushButton("تعديل")
            edit_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: rgba(255, 255, 255, 0.2);
                    color: white;
                    border: 2px solid white;
                    border-radius: {AppStyles.BORDER_RADIUS}px;
                    padding: 12px 24px;
                    font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: rgba(255, 255, 255, 0.3);
                }}
            """)
            edit_btn.clicked.connect(self.edit_invoice)
            
            delete_btn = QPushButton("حذف")
            delete_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {AppStyles.ERROR_COLOR};
                    color: white;
                    border: none;
                    border-radius: {AppStyles.BORDER_RADIUS}px;
                    padding: 12px 24px;
                    font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: #C62828;
                }}
            """)
            delete_btn.clicked.connect(self.delete_invoice)
            
            print_btn = QPushButton("طباعة")
            print_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {AppStyles.INFO_COLOR};
                    color: white;
                    border: none;
                    border-radius: {AppStyles.BORDER_RADIUS}px;
                    padding: 12px 24px;
                    font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: #1976D2;
                }}
            """)
            print_btn.clicked.connect(self.print_invoice)
            
            toolbar_layout.addWidget(title_label)
            toolbar_layout.addStretch()
            toolbar_layout.addWidget(new_invoice_btn)
            toolbar_layout.addWidget(edit_btn)
            toolbar_layout.addWidget(delete_btn)
            toolbar_layout.addWidget(print_btn)
            
            return toolbar_frame
        except Exception as e:
            print("Error in create_toolbar:", e)
            return QFrame()
    
    def create_filters(self):
        """إنشاء مرشحات البحث"""
        try:
            filters_frame = QFrame()
            filters_frame.setStyleSheet(f"""
                background-color: white;
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 15px;
                border: 1px solid {AppStyles.BORDER_COLOR};
            """)
            
            filters_layout = QHBoxLayout(filters_frame)
            
            # البحث بالرقم
            search_label = QLabel("رقم الفاتورة:")
            search_line = QLineEdit()
            search_line.setPlaceholderText("ابحث برقم الفاتورة...")
            search_line.setStyleSheet(AppStyles.INPUT_STYLE)
            
            # البحث بالعميل
            customer_label = QLabel("العميل:")
            customer_combo = QComboBox()
            customer_combo.addItems(["جميع العملاء", "أحمد محمد", "فاطمة أحمد", "محمد حسن"])
            customer_combo.setStyleSheet(AppStyles.COMBOBOX_STYLE)
            
            # تاريخ من
            date_from_label = QLabel("من تاريخ:")
            date_from = QDateEdit()
            date_from.setDate(QDate.currentDate().addDays(-30))
            date_from.setStyleSheet(AppStyles.INPUT_STYLE)
            
            # تاريخ إلى
            date_to_label = QLabel("إلى تاريخ:")
            date_to = QDateEdit()
            date_to.setDate(QDate.currentDate())
            date_to.setStyleSheet(AppStyles.INPUT_STYLE)
            
            # حالة الفاتورة
            status_label = QLabel("الحالة:")
            status_combo = QComboBox()
            status_combo.addItems(["جميع الحالات", "مدفوعة", "غير مدفوعة", "مدفوعة جزئياً"])
            status_combo.setStyleSheet(AppStyles.COMBOBOX_STYLE)
            
            # زر البحث
            search_btn = QPushButton("بحث")
            search_btn.setStyleSheet(AppStyles.BUTTON_STYLE)
            search_btn.clicked.connect(self.search_invoices)
            
            filters_layout.addWidget(search_label)
            filters_layout.addWidget(search_line)
            filters_layout.addWidget(customer_label)
            filters_layout.addWidget(customer_combo)
            filters_layout.addWidget(date_from_label)
            filters_layout.addWidget(date_from)
            filters_layout.addWidget(date_to_label)
            filters_layout.addWidget(date_to)
            filters_layout.addWidget(status_label)
            filters_layout.addWidget(status_combo)
            filters_layout.addWidget(search_btn)
            
            return filters_frame
        except Exception as e:
            print("Error in create_filters:", e)
            return QFrame()
    
    def create_invoices_table(self):
        """إنشاء جدول الفواتير"""
        try:
            self.invoices_table = QTableWidget()
            self.invoices_table.setColumnCount(7)
            
            headers = ["رقم الفاتورة", "التاريخ", "العميل", "المبلغ الإجمالي", "المدفوع", "المتبقي", "الحالة"]
            self.invoices_table.setHorizontalHeaderLabels(headers)
            
            self.invoices_table.setStyleSheet(AppStyles.TABLE_STYLE)
            self.invoices_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
            self.invoices_table.setAlternatingRowColors(True)
            self.invoices_table.setSelectionBehavior(QTableWidget.SelectRows)
            
            self.load_sample_invoices()
            
            return self.invoices_table
        except Exception as e:
            print("Error in create_invoices_table:", e)
            return QTableWidget()
    
    def load_sample_invoices(self):
        """تحميل بيانات الفواتير الوهمية"""
        try:
            sample_data = [
                ["INV-2024-001", "2024-01-15", "أحمد محمد علي", "15,500.00", "15,500.00", "0.00", "مدفوعة"],
                ["INV-2024-002", "2024-01-16", "فاطمة أحمد", "8,750.00", "5,000.00", "3,750.00", "مدفوعة جزئياً"],
                ["INV-2024-003", "2024-01-17", "محمد حسن", "22,300.00", "0.00", "22,300.00", "غير مدفوعة"],
                ["INV-2024-004", "2024-01-18", "سارة محمود", "12,100.00", "12,100.00", "0.00", "مدفوعة"],
                ["INV-2024-005", "2024-01-19", "علي عبدالله", "18,950.00", "10,000.00", "8,950.00", "مدفوعة جزئياً"],
                ["INV-2024-006", "2024-01-20", "نورا السيد", "6,500.00", "6,500.00", "0.00", "مدفوعة"],
            ]
            
            self.invoices_table.setRowCount(len(sample_data))
            
            for row, row_data in enumerate(sample_data):
                for col, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(cell_data)
                    
                    # تلوين الحالة
                    if col == 6:  # عمود الحالة
                        if cell_data == "مدفوعة":
                            item.setBackground(QColor(AppStyles.SUCCESS_COLOR + "30"))
                        elif cell_data == "مدفوعة جزئياً":
                            item.setBackground(QColor(AppStyles.WARNING_COLOR + "30"))
                        else:
                            item.setBackground(QColor(AppStyles.ERROR_COLOR + "30"))
                    
                    self.invoices_table.setItem(row, col, item)
        except Exception as e:
            print("Error in load_sample_invoices:", e)
    
    def new_invoice(self):
        """إنشاء فاتورة جديدة"""
        try:
            dialog = InvoiceDialog(self)
            if dialog.exec() == QDialog.Accepted:
                QMessageBox.information(self, "نجح", "تم إنشاء الفاتورة بنجاح")
                self.refresh_table()
        except Exception as e:
            print("Error in new_invoice:", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    def edit_invoice(self):
        """تعديل فاتورة"""
        try:
            current_row = self.invoices_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للتعديل")
                return
            
            dialog = InvoiceDialog(self, edit_mode=True)
            if dialog.exec() == QDialog.Accepted:
                QMessageBox.information(self, "نجح", "تم تعديل الفاتورة بنجاح")
                self.refresh_table()
        except Exception as e:
            print("Error in edit_invoice:", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    def delete_invoice(self):
        """حذف فاتورة"""
        try:
            current_row = self.invoices_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للحذف")
                return
            
            invoice_number = self.invoices_table.item(current_row, 0).text()
            reply = QMessageBox.question(self, "تأكيد الحذف", 
                                       f"هل أنت متأكد من حذف الفاتورة '{invoice_number}'؟",
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                self.invoices_table.removeRow(current_row)
                QMessageBox.information(self, "نجح", "تم حذف الفاتورة بنجاح")
        except Exception as e:
            print("Error in delete_invoice:", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    def print_invoice(self):
        """طباعة فاتورة"""
        try:
            current_row = self.invoices_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للطباعة")
                return
            
            QMessageBox.information(self, "طباعة", "جاري إعداد الفاتورة للطباعة...")
        except Exception as e:
            print("Error in print_invoice:", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    def search_invoices(self):
        """البحث في الفواتير"""
        try:
            QMessageBox.information(self, "بحث", "تم تطبيق مرشحات البحث")
        except Exception as e:
            print("Error in search_invoices:", e)
    
    def refresh_table(self):
        """تحديث الجدول"""
        try:
            self.load_sample_invoices()
        except Exception as e:
            print("Error in refresh_table:", e)


class InvoiceDialog(QDialog):
    """نافذة حوار إنشاء/تعديل فاتورة"""
    
    def __init__(self, parent=None, edit_mode=False):
        super().__init__(parent)
        self.edit_mode = edit_mode
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        try:
            title = "تعديل فاتورة" if self.edit_mode else "فاتورة بيع جديدة"
            self.setWindowTitle(title)
            self.setMinimumSize(800, 600)
            self.setStyleSheet(f"""
                QDialog {{
                    background-color: {AppStyles.BACKGROUND_COLOR};
                }}
            """)
            
            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)
            
            # عنوان
            title_label = QLabel(title)
            title_label.setStyleSheet(f"""
                color: {AppStyles.PRIMARY_COLOR};
                font-size: {AppStyles.FONT_SIZE_XLARGE}px;
                font-weight: bold;
                padding: 10px 0;
            """)
            
            # معلومات الفاتورة الأساسية
            invoice_info_frame = self.create_invoice_info_section()
            
            # تفاصيل المنتجات
            products_frame = self.create_products_section()
            
            # الإجماليات
            totals_frame = self.create_totals_section()
            
            # أزرار الحوار
            button_box = QDialogButtonBox(QDialogButtonBox.Save | QDialogButtonBox.Cancel)
            button_box.accepted.connect(self.accept)
            button_box.rejected.connect(self.reject)
            button_box.setStyleSheet(f"""
                QPushButton {{
                    {AppStyles.BUTTON_STYLE}
                    min-width: 100px;
                    padding: 10px 20px;
                }}
            """)
            
            main_layout.addWidget(title_label)
            main_layout.addWidget(invoice_info_frame)
            main_layout.addWidget(products_frame)
            main_layout.addWidget(totals_frame)
            main_layout.addWidget(button_box)
            
        except Exception as e:
            print("Error in InvoiceDialog.init_ui:", e)
    
    def create_invoice_info_section(self):
        """إنشاء قسم معلومات الفاتورة"""
        frame = QFrame()
        frame.setStyleSheet(f"""
            background-color: white;
            border-radius: {AppStyles.BORDER_RADIUS}px;
            padding: 20px;
            border: 1px solid {AppStyles.BORDER_COLOR};
        """)
        
        layout = QGridLayout(frame)
        
        # رقم الفاتورة
        layout.addWidget(QLabel("رقم الفاتورة:"), 0, 0)
        self.invoice_number = QLineEdit("INV-2024-007")
        self.invoice_number.setStyleSheet(AppStyles.INPUT_STYLE)
        layout.addWidget(self.invoice_number, 0, 1)
        
        # التاريخ
        layout.addWidget(QLabel("التاريخ:"), 0, 2)
        self.date_edit = QDateEdit(QDate.currentDate())
        self.date_edit.setStyleSheet(AppStyles.INPUT_STYLE)
        layout.addWidget(self.date_edit, 0, 3)
        
        # العميل
        layout.addWidget(QLabel("العميل:"), 1, 0)
        self.customer_combo = QComboBox()
        self.customer_combo.addItems(["اختر العميل", "أحمد محمد", "فاطمة أحمد", "محمد حسن"])
        self.customer_combo.setStyleSheet(AppStyles.COMBOBOX_STYLE)
        layout.addWidget(self.customer_combo, 1, 1)
        
        # ملاحظات
        layout.addWidget(QLabel("ملاحظات:"), 1, 2)
        self.notes_edit = QLineEdit()
        self.notes_edit.setStyleSheet(AppStyles.INPUT_STYLE)
        layout.addWidget(self.notes_edit, 1, 3)
        
        return frame
    
    def create_products_section(self):
        """إنشاء قسم المنتجات"""
        frame = QFrame()
        frame.setStyleSheet(f"""
            background-color: white;
            border-radius: {AppStyles.BORDER_RADIUS}px;
            padding: 20px;
            border: 1px solid {AppStyles.BORDER_COLOR};
        """)
        
        layout = QVBoxLayout(frame)
        
        # عنوان القسم
        title = QLabel("تفاصيل المنتجات")
        title.setStyleSheet(f"""
            color: {AppStyles.PRIMARY_COLOR};
            font-size: {AppStyles.FONT_SIZE_LARGE}px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # جدول المنتجات
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(5)
        self.products_table.setHorizontalHeaderLabels(["المنتج", "الكمية", "السعر", "الإجمالي", "حذف"])
        self.products_table.setStyleSheet(AppStyles.TABLE_STYLE)
        self.products_table.setMaximumHeight(200)
        
        layout.addWidget(self.products_table)
        
        # زر إضافة منتج
        add_product_btn = QPushButton("إضافة منتج")
        add_product_btn.setStyleSheet(AppStyles.BUTTON_STYLE)
        layout.addWidget(add_product_btn)
        
        return frame
    
    def create_totals_section(self):
        """إنشاء قسم الإجماليات"""
        frame = QFrame()
        frame.setStyleSheet(f"""
            background-color: white;
            border-radius: {AppStyles.BORDER_RADIUS}px;
            padding: 20px;
            border: 1px solid {AppStyles.BORDER_COLOR};
        """)
        
        layout = QGridLayout(frame)
        
        # المجموع الفرعي
        layout.addWidget(QLabel("المجموع الفرعي:"), 0, 0)
        self.subtotal = QLineEdit("0.00")
        self.subtotal.setReadOnly(True)
        self.subtotal.setStyleSheet(AppStyles.INPUT_STYLE)
        layout.addWidget(self.subtotal, 0, 1)
        
        # الخصم
        layout.addWidget(QLabel("الخصم:"), 0, 2)
        self.discount = QDoubleSpinBox()
        self.discount.setMaximum(999999.99)
        self.discount.setStyleSheet(AppStyles.INPUT_STYLE)
        layout.addWidget(self.discount, 0, 3)
        
        # الضريبة
        layout.addWidget(QLabel("الضريبة (14%):"), 1, 0)
        self.tax = QLineEdit("0.00")
        self.tax.setReadOnly(True)
        self.tax.setStyleSheet(AppStyles.INPUT_STYLE)
        layout.addWidget(self.tax, 1, 1)
        
        # الإجمالي النهائي
        layout.addWidget(QLabel("الإجمالي:"), 1, 2)
        self.total = QLineEdit("0.00")
        self.total.setReadOnly(True)
        self.total.setStyleSheet(f"""
            {AppStyles.INPUT_STYLE}
            font-weight: bold;
            color: {AppStyles.PRIMARY_COLOR};
        """)
        layout.addWidget(self.total, 1, 3)
        
        return frame