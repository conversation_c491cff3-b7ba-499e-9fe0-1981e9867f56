"""
نافذة إنشاء حساب جديد
"""
import os
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QLineEdit, QPushButton, QFrame, QMessageBox,
                              QSpacerItem, QSizePolicy, QComboBox)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon, QPixmap, QFont

from accounting_app.bll.user_manager import UserManager
from accounting_app.bll.translator import Translator
from accounting_app.bll.settings_manager import SettingsManager
from accounting_app.ui.styles import AppStyles


class RegisterWindow(QWidget):
    """نافذة إنشاء حساب جديد"""
    
    def __init__(self, parent=None):
        """
        تهيئة نافذة إنشاء حساب جديد
        :param parent: النافذة الأم
        """
        super().__init__(parent)
        self.user_manager = UserManager()
        self.translator = Translator()
        self.settings_manager = SettingsManager()
        
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # إعداد النافذة
        self.setWindowTitle(self.translator.translate('register_title'))
        self.setMinimumSize(800, 600)
        self.setStyleSheet(AppStyles.LOGIN_WINDOW_STYLE)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # القسم الأيسر (الشعار والترحيب)
        left_widget = QWidget()
        left_widget.setStyleSheet(f"background-color: {AppStyles.PRIMARY_COLOR};")
        left_widget.setMinimumWidth(400)
        
        left_layout = QVBoxLayout(left_widget)
        left_layout.setAlignment(Qt.AlignCenter)
        left_layout.setSpacing(20)
        
        # شعار التطبيق
        logo_label = QLabel()
        # هنا يمكنك إضافة شعار التطبيق إذا كان متاحًا
        # logo_pixmap = QPixmap(":/images/logo.png")
        # logo_label.setPixmap(logo_pixmap.scaled(150, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        logo_label.setText("🏪")  # استخدام رمز تعبيري كشعار مؤقت
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("font-size: 100px;")
        
        # عنوان التطبيق
        app_name_label = QLabel(self.translator.translate('app_name'))
        app_name_label.setAlignment(Qt.AlignCenter)
        app_name_label.setStyleSheet(f"""
            color: white;
            font-size: {AppStyles.FONT_SIZE_XXLARGE}px;
            font-weight: bold;
        """)
        
        # وصف التطبيق
        app_desc_label = QLabel("نظام محاسبة متكامل للمحلات التجارية")
        app_desc_label.setAlignment(Qt.AlignCenter)
        app_desc_label.setStyleSheet(f"""
            color: white;
            font-size: {AppStyles.FONT_SIZE_LARGE}px;
        """)
        app_desc_label.setWordWrap(True)
        
        left_layout.addStretch()
        left_layout.addWidget(logo_label)
        left_layout.addWidget(app_name_label)
        left_layout.addWidget(app_desc_label)
        left_layout.addStretch()
        
        # القسم الأيمن (نموذج إنشاء حساب)
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setAlignment(Qt.AlignCenter)
        right_layout.setContentsMargins(50, 50, 50, 50)
        
        # إطار إنشاء حساب
        register_frame = QFrame()
        register_frame.setObjectName("loginFrame")  # استخدام نفس الأسلوب
        register_frame.setStyleSheet(f"""
            QFrame#loginFrame {{
                background-color: white;
                border-radius: {AppStyles.BORDER_RADIUS * 2}px;
                padding: 20px;
            }}
        """)
        register_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        register_layout = QVBoxLayout(register_frame)
        register_layout.setSpacing(15)
        register_layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان إنشاء حساب
        title_label = QLabel(self.translator.translate('register_title'))
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignCenter)
        
        # حقل الاسم الكامل
        full_name_label = QLabel(self.translator.translate('full_name'))
        full_name_label.setStyleSheet(AppStyles.LABEL_STYLE)
        self.full_name_input = QLineEdit()
        self.full_name_input.setPlaceholderText(self.translator.translate('full_name'))
        self.full_name_input.setStyleSheet(AppStyles.INPUT_STYLE)
        self.full_name_input.setMinimumHeight(40)
        
        # حقل اسم المستخدم
        username_label = QLabel(self.translator.translate('username'))
        username_label.setStyleSheet(AppStyles.LABEL_STYLE)
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText(self.translator.translate('username'))
        self.username_input.setStyleSheet(AppStyles.INPUT_STYLE)
        self.username_input.setMinimumHeight(40)
        
        # حقل البريد الإلكتروني
        email_label = QLabel(self.translator.translate('email'))
        email_label.setStyleSheet(AppStyles.LABEL_STYLE)
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText(self.translator.translate('email'))
        self.email_input.setStyleSheet(AppStyles.INPUT_STYLE)
        self.email_input.setMinimumHeight(40)
        
        # حقل رقم الهاتف
        phone_label = QLabel(self.translator.translate('phone'))
        phone_label.setStyleSheet(AppStyles.LABEL_STYLE)
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText(self.translator.translate('phone'))
        self.phone_input.setStyleSheet(AppStyles.INPUT_STYLE)
        self.phone_input.setMinimumHeight(40)
        
        # حقل كلمة المرور
        password_label = QLabel(self.translator.translate('password'))
        password_label.setStyleSheet(AppStyles.LABEL_STYLE)
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText(self.translator.translate('password'))
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(AppStyles.INPUT_STYLE)
        self.password_input.setMinimumHeight(40)
        
        # حقل تأكيد كلمة المرور
        confirm_password_label = QLabel(self.translator.translate('confirm_password'))
        confirm_password_label.setStyleSheet(AppStyles.LABEL_STYLE)
        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setPlaceholderText(self.translator.translate('confirm_password'))
        self.confirm_password_input.setEchoMode(QLineEdit.Password)
        self.confirm_password_input.setStyleSheet(AppStyles.INPUT_STYLE)
        self.confirm_password_input.setMinimumHeight(40)
        
        # حقل الدور
        role_label = QLabel("الدور")
        role_label.setStyleSheet(AppStyles.LABEL_STYLE)
        self.role_input = QComboBox()
        self.role_input.addItems(["مدير", "موظف", "محاسب"])
        self.role_input.setStyleSheet(AppStyles.COMBOBOX_STYLE)
        self.role_input.setMinimumHeight(40)
        
        # زر التسجيل
        self.register_button = QPushButton(self.translator.translate('register_button'))
        self.register_button.setStyleSheet(AppStyles.BUTTON_STYLE)
        self.register_button.setMinimumHeight(40)
        self.register_button.setCursor(Qt.PointingHandCursor)
        self.register_button.clicked.connect(self.register)
        
        # رابط العودة إلى تسجيل الدخول
        self.back_to_login_button = QPushButton(self.translator.translate('back_to_login'))
        self.back_to_login_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #1976D2;
                border: none;
                text-decoration: underline;
            }
            QPushButton:hover {
                color: #1565C0;
            }
        """)
        self.back_to_login_button.setCursor(Qt.PointingHandCursor)
        self.back_to_login_button.clicked.connect(self.back_to_login)
        
        # إضافة العناصر إلى تخطيط إنشاء الحساب
        register_layout.addWidget(title_label)
        register_layout.addItem(QSpacerItem(20, 10))
        register_layout.addWidget(full_name_label)
        register_layout.addWidget(self.full_name_input)
        register_layout.addWidget(username_label)
        register_layout.addWidget(self.username_input)
        register_layout.addWidget(email_label)
        register_layout.addWidget(self.email_input)
        register_layout.addWidget(phone_label)
        register_layout.addWidget(self.phone_input)
        register_layout.addWidget(password_label)
        register_layout.addWidget(self.password_input)
        register_layout.addWidget(confirm_password_label)
        register_layout.addWidget(self.confirm_password_input)
        register_layout.addWidget(role_label)
        register_layout.addWidget(self.role_input)
        register_layout.addItem(QSpacerItem(20, 20))
        register_layout.addWidget(self.register_button)
        register_layout.addItem(QSpacerItem(20, 10))
        register_layout.addWidget(self.back_to_login_button, alignment=Qt.AlignCenter)
        
        # إضافة إطار إنشاء الحساب إلى القسم الأيمن
        right_layout.addStretch()
        right_layout.addWidget(register_frame)
        right_layout.addStretch()
        
        # إضافة القسمين إلى التخطيط الرئيسي
        main_layout.addWidget(left_widget)
        main_layout.addWidget(right_widget)
        
        # تعيين التركيز على حقل الاسم الكامل
        self.full_name_input.setFocus()
    
    def register(self):
        """تسجيل مستخدم جديد"""
        # التحقق من البيانات المدخلة
        full_name = self.full_name_input.text().strip()
        username = self.username_input.text().strip()
        email = self.email_input.text().strip()
        phone = self.phone_input.text().strip()
        password = self.password_input.text().strip()
        confirm_password = self.confirm_password_input.text().strip()
        role = self.role_input.currentText()
        
        # تحويل الدور إلى القيمة المناسبة في قاعدة البيانات
        role_map = {
            "مدير": "admin",
            "موظف": "employee",
            "محاسب": "accountant"
        }
        role_value = role_map.get(role, "employee")
        
        # التحقق من الحقول المطلوبة
        if not full_name or not username or not password or not confirm_password:
            QMessageBox.warning(
                self,
                "خطأ في التسجيل",
                self.translator.translate('required_field')
            )
            return
        
        # التحقق من تطابق كلمات المرور
        if password != confirm_password:
            QMessageBox.warning(
                self,
                "خطأ في التسجيل",
                self.translator.translate('passwords_not_match')
            )
            return
        
        # التحقق من عدم وجود مستخدم بنفس اسم المستخدم
        existing_user = self.user_manager.get_user_by_username(username)
        if existing_user:
            QMessageBox.warning(
                self,
                "خطأ في التسجيل",
                "اسم المستخدم موجود بالفعل، يرجى اختيار اسم مستخدم آخر"
            )
            return
        
        # إنشاء المستخدم الجديد
        user_id = self.user_manager.create_user(username, password, full_name, email, phone, role_value)
        
        if user_id:
            # تم التسجيل بنجاح
            QMessageBox.information(
                self,
                "تم التسجيل بنجاح",
                f"تم إنشاء حساب جديد باسم {full_name} بنجاح. يمكنك الآن تسجيل الدخول."
            )
            # العودة إلى صفحة تسجيل الدخول
            self.back_to_login()
        else:
            # فشل التسجيل
            QMessageBox.critical(
                self,
                "خطأ في التسجيل",
                "حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى."
            )
    
    def back_to_login(self):
        """العودة إلى صفحة تسجيل الدخول"""
        from accounting_app.ui.login_window import LoginWindow
        self.login_window = LoginWindow()
        self.login_window.show()
        self.close()