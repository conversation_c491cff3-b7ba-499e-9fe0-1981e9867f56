"""
واجهة إعدادات محسنة مع تنظيم أفضل وإدارة فعّالة للبيانات
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QLineEdit, QComboBox,
                              QCheckBox, QSpinBox, QDoubleSpinBox, QTextEdit,
                              QTabWidget, QFormLayout, QGroupBox, QSlider,
                              QMessageBox, QFileDialog, QColorDialog, QProgressBar,
                              QSplitter, QScrollArea, QButtonGroup, QRadioButton)
from PySide6.QtCore import Qt, QTimer, QThread, pyqtSignal
from PySide6.QtGui import QColor, QPalette, QFont
import json
import os
from datetime import datetime

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class SettingsWorker(QThread):
    """Worker thread لحفظ الإعدادات بدون تجميد الواجهة"""
    progress = pyqtSignal(int)
    finished = pyqtSignal(bool, str)
    
    def __init__(self, settings_data):
        super().__init__()
        self.settings_data = settings_data
    
    def run(self):
        try:
            # محاكاة عملية الحفظ مع تقدم
            for i in range(0, 101, 10):
                self.progress.emit(i)
                self.msleep(100)  # محاكاة الوقت المطلوب
            
            # حفظ الإعدادات فعلياً
            settings_file = "settings.json"
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings_data, f, ensure_ascii=False, indent=2)
            
            self.finished.emit(True, "تم حفظ الإعدادات بنجاح!")
        except Exception as e:
            self.finished.emit(False, f"خطأ في حفظ الإعدادات: {str(e)}")


class EnhancedSettingsWindow(QWidget):
    """نافذة إعدادات محسنة مع تنظيم أفضل"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.settings_data = {}
        self.changes_made = False
        self.worker = None
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم المحسنة"""
        self.setWindowTitle("⚙️ إعدادات النظام المتقدمة")
        self.setMinimumSize(1000, 700)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # الرأس المحسن
        header = self.create_enhanced_header()
        main_layout.addWidget(header)
        
        # المحتوى الرئيسي مع Splitter
        content_splitter = QSplitter(Qt.Horizontal)
        
        # الفهرس الجانبي
        sidebar = self.create_settings_sidebar()
        content_splitter.addWidget(sidebar)
        
        # منطقة الإعدادات
        settings_area = self.create_settings_area()
        content_splitter.addWidget(settings_area)
        
        content_splitter.setSizes([250, 750])
        main_layout.addWidget(content_splitter)
        
        # شريط الحالة والأزرار
        footer = self.create_enhanced_footer()
        main_layout.addWidget(footer)
    
    def create_enhanced_header(self):
        """إنشاء رأس محسن مع إحصائيات تفاعلية"""
        header_frame = QFrame()
        header_frame.setFixedHeight(140)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.3 #764ba2, stop:0.7 #f093fb, stop:1 #f093fb);
                border-radius: 0px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # المعلومات الأساسية
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        title = QLabel("⚙️ إعدادات النظام المتقدمة")
        title.setStyleSheet("""
            color: white;
            font-size: 26px;
            font-weight: bold;
            margin-bottom: 8px;
        """)
        
        subtitle = QLabel("تحكم كامل في جميع جوانب النظام • حفظ تلقائي • استعادة سريعة")
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-bottom: 15px;
        """)
        
        # شريط إحصائيات سريع
        stats_container = QWidget()
        stats_layout = QHBoxLayout(stats_container)
        stats_layout.setSpacing(20)
        
        stats = [
            ("⚙️", "إعدادات", "25+", "#4CAF50"),
            ("🔧", "تخصيصات", "15+", "#2196F3"),
            ("💾", "نسخ احتياطي", "Auto", "#FF9800"),
            ("🔒", "الأمان", "مفعل", "#9C27B0")
        ]
        
        for icon, label, value, color in stats:
            stat_card = self.create_stat_card(icon, label, value, color)
            stats_layout.addWidget(stat_card)
        
        stats_layout.addStretch()
        
        info_layout.addWidget(title)
        info_layout.addWidget(subtitle)
        info_layout.addWidget(stats_container)
        
        # أيقونة الحالة
        status_widget = QWidget()
        status_layout = QVBoxLayout(status_widget)
        status_layout.setAlignment(Qt.AlignCenter)
        
        status_icon = QLabel("✅")
        status_icon.setStyleSheet("""
            font-size: 50px;
            color: #4CAF50;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 35px;
            padding: 15px;
            border: 2px solid rgba(255, 255, 255, 0.3);
        """)
        status_icon.setAlignment(Qt.AlignCenter)
        status_icon.setFixedSize(70, 70)
        
        status_label = QLabel("النظام محدث")
        status_label.setStyleSheet("""
            color: white;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            margin-top: 5px;
        """)
        
        status_layout.addWidget(status_icon)
        status_layout.addWidget(status_label)
        
        layout.addWidget(info_widget, 8)
        layout.addWidget(status_widget, 2)
        
        return header_frame
    
    def create_stat_card(self, icon, label, value, color):
        """إنشاء بطاقة إحصائية صغيرة"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 8px 12px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }}
        """)
        
        layout = QHBoxLayout(card)
        layout.setContentsMargins(8, 6, 8, 6)
        layout.setSpacing(8)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            font-size: 16px;
            color: {color};
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 4px;
            min-width: 24px;
            max-width: 24px;
            min-height: 24px;
            max-height: 24px;
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        
        text_widget = QWidget()
        text_layout = QVBoxLayout(text_widget)
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(1)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 13px; font-weight: bold;")
        
        label_text = QLabel(label)
        label_text.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 10px;")
        
        text_layout.addWidget(value_label)
        text_layout.addWidget(label_text)
        
        layout.addWidget(icon_label)
        layout.addWidget(text_widget)
        
        return card
    
    def create_settings_sidebar(self):
        """إنشاء الفهرس الجانبي للإعدادات"""
        sidebar_frame = QFrame()
        sidebar_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-right: 1px solid #dee2e6;
                padding: 15px;
            }
        """)
        sidebar_frame.setFixedWidth(250)
        
        layout = QVBoxLayout(sidebar_frame)
        
        # عنوان الفهرس
        title = QLabel("📋 فهرس الإعدادات")
        title.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
            padding: 10px 0;
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 15px;
        """)
        
        # أزرار التنقل
        self.nav_buttons = QButtonGroup()
        nav_items = [
            ("🏢", "معلومات الشركة", "company"),
            ("💰", "إعدادات العملة", "currency"), 
            ("🎨", "المظهر والواجهة", "appearance"),
            ("🗄️", "قاعدة البيانات", "database"),
            ("💾", "النسخ الاحتياطي", "backup"),
            ("🖨️", "الطباعة", "printing"),
            ("🔒", "الأمان", "security"),
            ("🌐", "الشبكة", "network")
        ]
        
        for i, (icon, text, key) in enumerate(nav_items):
            btn = QPushButton(f"{icon} {text}")
            btn.setCheckable(True)
            btn.setStyleSheet("""
                QPushButton {
                    text-align: left;
                    padding: 12px 15px;
                    border: none;
                    border-radius: 8px;
                    font-size: 13px;
                    color: #495057;
                    background: transparent;
                    margin-bottom: 5px;
                }
                QPushButton:hover {
                    background: #e9ecef;
                    color: #343a40;
                }
                QPushButton:checked {
                    background: #007bff;
                    color: white;
                    font-weight: bold;
                }
            """)
            btn.clicked.connect(lambda checked, k=key: self.show_settings_category(k))
            self.nav_buttons.addButton(btn, i)
            layout.addWidget(btn)
        
        # تفعيل الفئة الأولى
        self.nav_buttons.button(0).setChecked(True)
        
        layout.addStretch()
        
        # معلومات الحفظ
        save_info = QLabel("💡 يتم حفظ التغييرات تلقائياً")
        save_info.setStyleSheet("""
            color: #6c757d;
            font-size: 11px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 6px;
            border-left: 3px solid #28a745;
        """)
        save_info.setWordWrap(True)
        layout.addWidget(save_info)
        
        return sidebar_frame
    
    def create_settings_area(self):
        """إنشاء منطقة الإعدادات الرئيسية"""
        self.settings_stack = QWidget()
        self.settings_layout = QVBoxLayout(self.settings_stack)
        self.settings_layout.setContentsMargins(20, 20, 20, 20)
        
        # إنشاء صفحات الإعدادات
        self.settings_pages = {}
        self.create_all_settings_pages()
        
        # عرض الصفحة الأولى
        self.show_settings_category("company")
        
        return self.settings_stack
    
    def create_all_settings_pages(self):
        """إنشاء جميع صفحات الإعدادات"""
        # صفحة معلومات الشركة
        self.settings_pages["company"] = self.create_company_settings()
        
        # صفحة إعدادات العملة
        self.settings_pages["currency"] = self.create_currency_settings()
        
        # صفحة المظهر
        self.settings_pages["appearance"] = self.create_appearance_settings()
        
        # صفحة قاعدة البيانات
        self.settings_pages["database"] = self.create_database_settings()
        
        # صفحة النسخ الاحتياطي
        self.settings_pages["backup"] = self.create_backup_settings()
        
        # صفحة الطباعة
        self.settings_pages["printing"] = self.create_printing_settings()
        
        # صفحة الأمان
        self.settings_pages["security"] = self.create_security_settings()
        
        # صفحة الشبكة
        self.settings_pages["network"] = self.create_network_settings()
    
    def create_company_settings(self):
        """إعدادات معلومات الشركة"""
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setStyleSheet("border: none;")
        
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        # عنوان القسم
        title = QLabel("🏢 معلومات الشركة")
        title.setStyleSheet("""
            color: #007bff;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # مجموعة البيانات الأساسية
        basic_group = QGroupBox("📋 البيانات الأساسية")
        basic_group.setStyleSheet(self.get_group_style())
        basic_layout = QFormLayout(basic_group)
        
        self.company_name = QLineEdit("شركة المحاسبة المتقدمة")
        self.company_name.setStyleSheet(self.get_input_style())
        self.company_name.textChanged.connect(self.mark_changes)
        
        self.company_name_en = QLineEdit("Advanced Accounting Company")
        self.company_name_en.setStyleSheet(self.get_input_style())
        self.company_name_en.textChanged.connect(self.mark_changes)
        
        self.company_address = QTextEdit("القاهرة، مصر\nشارع النصر، المعادي")
        self.company_address.setStyleSheet(self.get_input_style())
        self.company_address.setMaximumHeight(80)
        self.company_address.textChanged.connect(self.mark_changes)
        
        basic_layout.addRow("🏢 اسم الشركة (عربي):", self.company_name)
        basic_layout.addRow("🏢 اسم الشركة (English):", self.company_name_en)
        basic_layout.addRow("📍 العنوان:", self.company_address)
        
        # مجموعة معلومات الاتصال
        contact_group = QGroupBox("📞 معلومات الاتصال")
        contact_group.setStyleSheet(self.get_group_style())
        contact_layout = QFormLayout(contact_group)
        
        self.company_phone = QLineEdit("***********")
        self.company_phone.setStyleSheet(self.get_input_style())
        self.company_phone.textChanged.connect(self.mark_changes)
        
        self.company_fax = QLineEdit("0223456789")
        self.company_fax.setStyleSheet(self.get_input_style())
        self.company_fax.textChanged.connect(self.mark_changes)
        
        self.company_email = QLineEdit("<EMAIL>")
        self.company_email.setStyleSheet(self.get_input_style())
        self.company_email.textChanged.connect(self.mark_changes)
        
        self.company_website = QLineEdit("www.company.com")
        self.company_website.setStyleSheet(self.get_input_style())
        self.company_website.textChanged.connect(self.mark_changes)
        
        contact_layout.addRow("📱 الهاتف:", self.company_phone)
        contact_layout.addRow("📠 الفاكس:", self.company_fax)
        contact_layout.addRow("✉️ البريد الإلكتروني:", self.company_email)
        contact_layout.addRow("🌐 الموقع الإلكتروني:", self.company_website)
        
        # مجموعة المعلومات القانونية
        legal_group = QGroupBox("⚖️ المعلومات القانونية")
        legal_group.setStyleSheet(self.get_group_style())
        legal_layout = QFormLayout(legal_group)
        
        self.tax_number = QLineEdit("*********")
        self.tax_number.setStyleSheet(self.get_input_style())
        self.tax_number.textChanged.connect(self.mark_changes)
        
        self.commercial_register = QLineEdit("*********")
        self.commercial_register.setStyleSheet(self.get_input_style())
        self.commercial_register.textChanged.connect(self.mark_changes)
        
        legal_layout.addRow("🏛️ الرقم الضريبي:", self.tax_number)
        legal_layout.addRow("📋 السجل التجاري:", self.commercial_register)
        
        layout.addWidget(basic_group)
        layout.addWidget(contact_group)
        layout.addWidget(legal_group)
        layout.addStretch()
        
        scroll.setWidget(widget)
        return scroll
    
    def create_currency_settings(self):
        """إعدادات العملة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        title = QLabel("💰 إعدادات العملة والأرقام")
        title.setStyleSheet("""
            color: #007bff;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # مجموعة العملة الأساسية
        currency_group = QGroupBox("💱 العملة الأساسية")
        currency_group.setStyleSheet(self.get_group_style())
        currency_layout = QFormLayout(currency_group)
        
        self.default_currency = QComboBox()
        self.default_currency.addItems([
            "جنيه مصري (ج.م)", "دولار أمريكي ($)", "يورو (€)", 
            "ريال سعودي (ر.س)", "درهم إماراتي (د.إ)"
        ])
        self.default_currency.setStyleSheet(self.get_combo_style())
        self.default_currency.currentTextChanged.connect(self.mark_changes)
        
        self.currency_symbol = QLineEdit("ج.م")
        self.currency_symbol.setStyleSheet(self.get_input_style())
        self.currency_symbol.textChanged.connect(self.mark_changes)
        
        currency_layout.addRow("💰 العملة الافتراضية:", self.default_currency)
        currency_layout.addRow("💲 رمز العملة:", self.currency_symbol)
        
        # مجموعة تنسيق الأرقام
        format_group = QGroupBox("🔢 تنسيق الأرقام")
        format_group.setStyleSheet(self.get_group_style())
        format_layout = QFormLayout(format_group)
        
        self.decimal_places = QSpinBox()
        self.decimal_places.setRange(0, 5)
        self.decimal_places.setValue(2)
        self.decimal_places.setStyleSheet(self.get_input_style())
        self.decimal_places.valueChanged.connect(self.mark_changes)
        
        self.thousands_separator = QComboBox()
        self.thousands_separator.addItems(["فاصلة (,)", "نقطة (.)", "مسافة ( )"])
        self.thousands_separator.setStyleSheet(self.get_combo_style())
        self.thousands_separator.currentTextChanged.connect(self.mark_changes)
        
        format_layout.addRow("🔢 عدد الخانات العشرية:", self.decimal_places)
        format_layout.addRow("📊 فاصل الآلاف:", self.thousands_separator)
        
        layout.addWidget(currency_group)
        layout.addWidget(format_group)
        layout.addStretch()
        
        return widget
    
    def create_appearance_settings(self):
        """إعدادات المظهر"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        title = QLabel("🎨 المظهر والواجهة")
        title.setStyleSheet("""
            color: #007bff;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # مجموعة المظهر
        theme_group = QGroupBox("🌈 مظهر النظام")
        theme_group.setStyleSheet(self.get_group_style())
        theme_layout = QVBoxLayout(theme_group)
        
        # خيارات المظهر
        theme_options = QWidget()
        theme_options_layout = QHBoxLayout(theme_options)
        
        self.theme_light = QRadioButton("☀️ فاتح")
        self.theme_light.setChecked(True)
        self.theme_light.setStyleSheet("font-size: 14px; color: #495057;")
        
        self.theme_dark = QRadioButton("🌙 داكن")
        self.theme_dark.setStyleSheet("font-size: 14px; color: #495057;")
        
        self.theme_auto = QRadioButton("🔄 تلقائي")
        self.theme_auto.setStyleSheet("font-size: 14px; color: #495057;")
        
        theme_options_layout.addWidget(self.theme_light)
        theme_options_layout.addWidget(self.theme_dark)
        theme_options_layout.addWidget(self.theme_auto)
        theme_options_layout.addStretch()
        
        # أزرار الألوان
        colors_widget = QWidget()
        colors_layout = QHBoxLayout(colors_widget)
        
        self.primary_color_btn = QPushButton("اللون الأساسي")
        self.primary_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {AppStyles.PRIMARY_COLOR};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }}
        """)
        self.primary_color_btn.clicked.connect(self.choose_primary_color)
        
        self.secondary_color_btn = QPushButton("اللون الثانوي")
        self.secondary_color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {AppStyles.SECONDARY_COLOR if hasattr(AppStyles, 'SECONDARY_COLOR') else '#6c757d'};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }}
        """)
        self.secondary_color_btn.clicked.connect(self.choose_secondary_color)
        
        colors_layout.addWidget(self.primary_color_btn)
        colors_layout.addWidget(self.secondary_color_btn)
        colors_layout.addStretch()
        
        theme_layout.addWidget(theme_options)
        theme_layout.addWidget(colors_widget)
        
        # مجموعة الخط
        font_group = QGroupBox("🔤 إعدادات الخط")
        font_group.setStyleSheet(self.get_group_style())
        font_layout = QFormLayout(font_group)
        
        self.font_family = QComboBox()
        self.font_family.addItems(["Arial", "Tahoma", "Cairo", "Noto Sans Arabic"])
        self.font_family.setStyleSheet(self.get_combo_style())
        
        self.font_size = QSpinBox()
        self.font_size.setRange(8, 24)
        self.font_size.setValue(12)
        self.font_size.setStyleSheet(self.get_input_style())
        
        font_layout.addRow("📝 نوع الخط:", self.font_family)
        font_layout.addRow("📏 حجم الخط:", self.font_size)
        
        layout.addWidget(theme_group)
        layout.addWidget(font_group)
        layout.addStretch()
        
        return widget
    
    def create_database_settings(self):
        """إعدادات قاعدة البيانات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        title = QLabel("🗄️ إعدادات قاعدة البيانات")
        title.setStyleSheet("""
            color: #007bff;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # مجموعة الاتصال
        connection_group = QGroupBox("🔗 اتصال قاعدة البيانات")
        connection_group.setStyleSheet(self.get_group_style())
        connection_layout = QFormLayout(connection_group)
        
        self.db_path = QLineEdit("./database/accounting.db")
        self.db_path.setStyleSheet(self.get_input_style())
        
        browse_btn = QPushButton("📁 استعراض")
        browse_btn.setStyleSheet(self.get_button_style())
        browse_btn.clicked.connect(self.browse_database_path)
        
        path_widget = QWidget()
        path_layout = QHBoxLayout(path_widget)
        path_layout.addWidget(self.db_path)
        path_layout.addWidget(browse_btn)
        
        connection_layout.addRow("📂 مسار قاعدة البيانات:", path_widget)
        
        # مجموعة الأداء
        performance_group = QGroupBox("⚡ تحسين الأداء")
        performance_group.setStyleSheet(self.get_group_style())
        performance_layout = QVBoxLayout(performance_group)
        
        optimize_btn = QPushButton("🔧 تحسين قاعدة البيانات")
        optimize_btn.setStyleSheet(self.get_button_style())
        optimize_btn.clicked.connect(self.optimize_database)
        
        performance_layout.addWidget(optimize_btn)
        
        layout.addWidget(connection_group)
        layout.addWidget(performance_group)
        layout.addStretch()
        
        return widget
    
    def create_backup_settings(self):
        """إعدادات النسخ الاحتياطي"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        title = QLabel("💾 إعدادات النسخ الاحتياطي")
        title.setStyleSheet("""
            color: #007bff;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # مجموعة النسخ التلقائي
        auto_backup_group = QGroupBox("🔄 النسخ الاحتياطي التلقائي")
        auto_backup_group.setStyleSheet(self.get_group_style())
        auto_backup_layout = QFormLayout(auto_backup_group)
        
        self.auto_backup_enabled = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        self.auto_backup_enabled.setChecked(True)
        self.auto_backup_enabled.setStyleSheet("font-size: 14px; color: #495057;")
        
        self.backup_interval = QComboBox()
        self.backup_interval.addItems(["يومياً", "أسبوعياً", "شهرياً"])
        self.backup_interval.setStyleSheet(self.get_combo_style())
        
        auto_backup_layout.addRow("", self.auto_backup_enabled)
        auto_backup_layout.addRow("⏰ الفترة الزمنية:", self.backup_interval)
        
        # مجموعة مجلد النسخ
        backup_folder_group = QGroupBox("📁 مجلد النسخ الاحتياطي")
        backup_folder_group.setStyleSheet(self.get_group_style())
        backup_folder_layout = QFormLayout(backup_folder_group)
        
        self.backup_folder = QLineEdit("./backups/")
        self.backup_folder.setStyleSheet(self.get_input_style())
        
        browse_backup_btn = QPushButton("📁 اختيار مجلد")
        browse_backup_btn.setStyleSheet(self.get_button_style())
        browse_backup_btn.clicked.connect(self.browse_backup_folder)
        
        folder_widget = QWidget()
        folder_layout = QHBoxLayout(folder_widget)
        folder_layout.addWidget(self.backup_folder)
        folder_layout.addWidget(browse_backup_btn)
        
        backup_folder_layout.addRow("📂 مسار النسخ:", folder_widget)
        
        # أزرار النسخ
        backup_actions = QWidget()
        backup_actions_layout = QHBoxLayout(backup_actions)
        
        create_backup_btn = QPushButton("💾 إنشاء نسخة احتياطية")
        create_backup_btn.setStyleSheet(self.get_button_style())
        create_backup_btn.clicked.connect(self.create_backup)
        
        restore_backup_btn = QPushButton("📥 استعادة نسخة احتياطية")
        restore_backup_btn.setStyleSheet(self.get_button_style("#28a745"))
        restore_backup_btn.clicked.connect(self.restore_backup)
        
        backup_actions_layout.addWidget(create_backup_btn)
        backup_actions_layout.addWidget(restore_backup_btn)
        backup_actions_layout.addStretch()
        
        # شريط التقدم
        self.backup_progress = QProgressBar()
        self.backup_progress.setVisible(False)
        self.backup_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                color: #495057;
            }
            QProgressBar::chunk {
                background-color: #007bff;
                border-radius: 6px;
            }
        """)
        
        layout.addWidget(auto_backup_group)
        layout.addWidget(backup_folder_group)
        layout.addWidget(backup_actions)
        layout.addWidget(self.backup_progress)
        layout.addStretch()
        
        return widget
    
    def create_printing_settings(self):
        """إعدادات الطباعة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        title = QLabel("🖨️ إعدادات الطباعة")
        title.setStyleSheet("""
            color: #007bff;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # مجموعة الطابعة
        printer_group = QGroupBox("🖨️ إعدادات الطابعة")
        printer_group.setStyleSheet(self.get_group_style())
        printer_layout = QFormLayout(printer_group)
        
        self.default_printer = QComboBox()
        self.default_printer.addItems(["طابعة افتراضية", "HP LaserJet", "Canon Pixma"])
        self.default_printer.setStyleSheet(self.get_combo_style())
        
        self.paper_size = QComboBox()
        self.paper_size.addItems(["A4", "A3", "Letter"])
        self.paper_size.setStyleSheet(self.get_combo_style())
        
        printer_layout.addRow("🖨️ الطابعة الافتراضية:", self.default_printer)
        printer_layout.addRow("📄 حجم الورق:", self.paper_size)
        
        layout.addWidget(printer_group)
        layout.addStretch()
        
        return widget
    
    def create_security_settings(self):
        """إعدادات الأمان"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        title = QLabel("🔒 إعدادات الأمان")
        title.setStyleSheet("""
            color: #007bff;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # مجموعة كلمات المرور
        password_group = QGroupBox("🔐 سياسة كلمات المرور")
        password_group.setStyleSheet(self.get_group_style())
        password_layout = QVBoxLayout(password_group)
        
        self.require_complex_password = QCheckBox("طلب كلمة مرور معقدة")
        self.require_complex_password.setChecked(True)
        self.require_complex_password.setStyleSheet("font-size: 14px; color: #495057;")
        
        self.password_expiry = QCheckBox("انتهاء صلاحية كلمة المرور")
        self.password_expiry.setStyleSheet("font-size: 14px; color: #495057;")
        
        password_layout.addWidget(self.require_complex_password)
        password_layout.addWidget(self.password_expiry)
        
        layout.addWidget(password_group)
        layout.addStretch()
        
        return widget
    
    def create_network_settings(self):
        """إعدادات الشبكة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        title = QLabel("🌐 إعدادات الشبكة")
        title.setStyleSheet("""
            color: #007bff;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # مجموعة الاتصال
        connection_group = QGroupBox("🔗 إعدادات الاتصال")
        connection_group.setStyleSheet(self.get_group_style())
        connection_layout = QFormLayout(connection_group)
        
        self.server_url = QLineEdit("https://api.company.com")
        self.server_url.setStyleSheet(self.get_input_style())
        
        self.connection_timeout = QSpinBox()
        self.connection_timeout.setRange(5, 60)
        self.connection_timeout.setValue(30)
        self.connection_timeout.setSuffix(" ثانية")
        self.connection_timeout.setStyleSheet(self.get_input_style())
        
        connection_layout.addRow("🌐 عنوان الخادم:", self.server_url)
        connection_layout.addRow("⏱️ مهلة الاتصال:", self.connection_timeout)
        
        layout.addWidget(connection_group)
        layout.addStretch()
        
        return widget
    
    def create_enhanced_footer(self):
        """إنشاء تذييل محسن مع أزرار التحكم"""
        footer_frame = QFrame()
        footer_frame.setFixedHeight(80)
        footer_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-top: 1px solid #dee2e6;
                padding: 15px 20px;
            }
        """)
        
        layout = QHBoxLayout(footer_frame)
        
        # معلومات الحالة
        status_widget = QWidget()
        status_layout = QVBoxLayout(status_widget)
        status_layout.setContentsMargins(0, 0, 0, 0)
        
        self.status_label = QLabel("✅ النظام جاهز")
        self.status_label.setStyleSheet("""
            color: #28a745;
            font-size: 14px;
            font-weight: bold;
        """)
        
        self.last_saved_label = QLabel("آخر حفظ: لم يتم الحفظ بعد")
        self.last_saved_label.setStyleSheet("""
            color: #6c757d;
            font-size: 12px;
        """)
        
        status_layout.addWidget(self.status_label)
        status_layout.addWidget(self.last_saved_label)
        
        # شريط التقدم
        self.save_progress = QProgressBar()
        self.save_progress.setVisible(False)
        self.save_progress.setFixedHeight(8)
        self.save_progress.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 4px;
                background-color: #e9ecef;
            }
            QProgressBar::chunk {
                background-color: #007bff;
                border-radius: 4px;
            }
        """)
        
        # أزرار التحكم
        buttons_widget = QWidget()
        buttons_layout = QHBoxLayout(buttons_widget)
        buttons_layout.setSpacing(10)
        
        # زر إعادة التعيين
        reset_btn = QPushButton("🔄 إعادة تعيين")
        reset_btn.setStyleSheet(self.get_button_style("#dc3545"))
        reset_btn.clicked.connect(self.reset_settings)
        
        # زر إلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet(self.get_button_style("#6c757d"))
        cancel_btn.clicked.connect(self.cancel_settings)
        
        # زر الحفظ الرئيسي
        self.save_btn = QPushButton("💾 حفظ الإعدادات")
        self.save_btn.setStyleSheet(self.get_button_style("#28a745"))
        self.save_btn.clicked.connect(self.save_settings)
        
        buttons_layout.addWidget(reset_btn)
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(self.save_btn)
        
        layout.addWidget(status_widget)
        layout.addStretch()
        layout.addWidget(self.save_progress)
        layout.addWidget(buttons_widget)
        
        return footer_frame
    
    def show_settings_category(self, category):
        """عرض فئة إعدادات معينة"""
        # مسح المحتوى الحالي
        for i in reversed(range(self.settings_layout.count())):
            self.settings_layout.itemAt(i).widget().setParent(None)
        
        # عرض الصفحة المطلوبة
        if category in self.settings_pages:
            self.settings_layout.addWidget(self.settings_pages[category])
    
    def mark_changes(self):
        """تمييز التغييرات"""
        self.changes_made = True
        self.status_label.setText("⚠️ توجد تغييرات غير محفوظة")
        self.status_label.setStyleSheet("color: #ffc107; font-size: 14px; font-weight: bold;")
        self.save_btn.setStyleSheet(self.get_button_style("#ffc107"))
    
    def save_settings(self):
        """حفظ الإعدادات مع شريط تقدم"""
        if not self.changes_made:
            QMessageBox.information(self, "معلومات", "لا توجد تغييرات لحفظها!")
            return
        
        # إعداد البيانات للحفظ
        self.settings_data = {
            "company": {
                "name": self.company_name.text(),
                "name_en": self.company_name_en.text(),
                "address": self.company_address.toPlainText(),
                "phone": self.company_phone.text(),
                "email": self.company_email.text(),
            },
            "currency": {
                "default": self.default_currency.currentText(),
                "symbol": self.currency_symbol.text(),
                "decimal_places": self.decimal_places.value(),
            },
            "last_saved": datetime.now().isoformat()
        }
        
        # إظهار شريط التقدم
        self.save_progress.setVisible(True)
        self.save_btn.setEnabled(False)
        
        # بدء عملية الحفظ
        self.worker = SettingsWorker(self.settings_data)
        self.worker.progress.connect(self.save_progress.setValue)
        self.worker.finished.connect(self.on_save_finished)
        self.worker.start()
    
    def on_save_finished(self, success, message):
        """التعامل مع انتهاء عملية الحفظ"""
        self.save_progress.setVisible(False)
        self.save_btn.setEnabled(True)
        
        if success:
            self.changes_made = False
            self.status_label.setText("✅ تم الحفظ بنجاح")
            self.status_label.setStyleSheet("color: #28a745; font-size: 14px; font-weight: bold;")
            self.last_saved_label.setText(f"آخر حفظ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.save_btn.setStyleSheet(self.get_button_style("#28a745"))
            
            # إشعار نجاح
            QMessageBox.information(self, "نجح الحفظ", message)
        else:
            # إشعار خطأ
            QMessageBox.critical(self, "خطأ في الحفظ", message)
    
    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            if os.path.exists("settings.json"):
                with open("settings.json", 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # تطبيق الإعدادات المحملة
                if "company" in data:
                    company = data["company"]
                    if hasattr(self, 'company_name'):
                        self.company_name.setText(company.get("name", ""))
                        self.company_name_en.setText(company.get("name_en", ""))
                        self.company_address.setPlainText(company.get("address", ""))
                        self.company_phone.setText(company.get("phone", ""))
                        self.company_email.setText(company.get("email", ""))
                
                if "last_saved" in data:
                    self.last_saved_label.setText(f"آخر حفظ: {data['last_saved']}")
                    
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(
            self, "تأكيد إعادة التعيين", 
            "هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟\n\nسيتم فقدان جميع التخصيصات الحالية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # إعادة تعيين القيم
            self.company_name.setText("شركة المحاسبة المتقدمة")
            self.company_name_en.setText("Advanced Accounting Company")
            self.company_address.setPlainText("القاهرة، مصر\nشارع النصر، المعادي")
            self.company_phone.setText("***********")
            self.company_email.setText("<EMAIL>")
            self.default_currency.setCurrentIndex(0)
            self.decimal_places.setValue(2)
            
            self.mark_changes()
            QMessageBox.information(self, "تم", "تم إعادة تعيين الإعدادات بنجاح!")
    
    def cancel_settings(self):
        """إلغاء التغييرات"""
        if self.changes_made:
            reply = QMessageBox.question(
                self, "تأكيد الإلغاء",
                "توجد تغييرات غير محفوظة. هل تريد إلغاء التغييرات؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.load_settings()  # إعادة تحميل الإعدادات المحفوظة
                self.changes_made = False
                self.status_label.setText("✅ النظام جاهز")
                self.status_label.setStyleSheet("color: #28a745; font-size: 14px; font-weight: bold;")
    
    # دوال الأنماط
    def get_group_style(self):
        return """
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #495057;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 8px;
                background: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px;
                background: white;
            }
        """
    
    def get_input_style(self):
        return """
            QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox {
                padding: 10px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                font-size: 13px;
                background: white;
            }
            QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #007bff;
            }
        """
    
    def get_combo_style(self):
        return """
            QComboBox {
                padding: 10px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                font-size: 13px;
                background: white;
            }
            QComboBox:focus {
                border-color: #007bff;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
        """
    
    def get_button_style(self, color="#007bff"):
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 13px;
                font-weight: bold;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.2)};
            }}
            QPushButton:disabled {{
                background-color: #6c757d;
                opacity: 0.6;
            }}
        """
    
    def darken_color(self, color, factor=0.1):
        """تظليل اللون"""
        try:
            color = QColor(color)
            h, s, l, a = color.getHslF()
            return QColor.fromHslF(h, s, max(0, l - factor), a).name()
        except:
            return "#495057"
    
    # دوال المساعدة للألوان والملفات
    def choose_primary_color(self):
        """اختيار اللون الأساسي"""
        color = QColorDialog.getColor(QColor(AppStyles.PRIMARY_COLOR), self, "اختيار اللون الأساسي")
        if color.isValid():
            self.primary_color_btn.setStyleSheet(self.get_button_style(color.name()))
            self.mark_changes()
    
    def choose_secondary_color(self):
        """اختيار اللون الثانوي"""
        color = QColorDialog.getColor(QColor("#6c757d"), self, "اختيار اللون الثانوي")
        if color.isValid():
            self.secondary_color_btn.setStyleSheet(self.get_button_style(color.name()))
            self.mark_changes()
    
    def browse_database_path(self):
        """استعراض مسار قاعدة البيانات"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار قاعدة البيانات", "", "Database Files (*.db);;All Files (*)"
        )
        if file_path:
            self.db_path.setText(file_path)
            self.mark_changes()
    
    def browse_backup_folder(self):
        """استعراض مجلد النسخ الاحتياطي"""
        folder_path = QFileDialog.getExistingDirectory(self, "اختيار مجلد النسخ الاحتياطي")
        if folder_path:
            self.backup_folder.setText(folder_path)
            self.mark_changes()
    
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        reply = QMessageBox.question(
            self, "تحسين قاعدة البيانات",
            "سيتم تحسين قاعدة البيانات وإزالة البيانات غير المستخدمة.\nهل تريد المتابعة؟",
            QMessageBox.Yes | QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تحسين قاعدة البيانات", "تم تحسين قاعدة البيانات بنجاح!")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        self.backup_progress.setVisible(True)
        self.backup_progress.setValue(0)
        
        # محاكاة عملية النسخ
        timer = QTimer()
        progress = 0
        
        def update_progress():
            nonlocal progress
            progress += 10
            self.backup_progress.setValue(progress)
            if progress >= 100:
                timer.stop()
                self.backup_progress.setVisible(False)
                QMessageBox.information(self, "نسخ احتياطي", "تم إنشاء النسخة الاحتياطية بنجاح!")
        
        timer.timeout.connect(update_progress)
        timer.start(200)
    
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار النسخة الاحتياطية", "", "Backup Files (*.bak);;All Files (*)"
        )
        if file_path:
            reply = QMessageBox.question(
                self, "تأكيد الاستعادة",
                "سيتم استبدال البيانات الحالية بالنسخة الاحتياطية.\nهل تريد المتابعة؟",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                QMessageBox.information(self, "استعادة", "تم استعادة النسخة الاحتياطية بنجاح!")