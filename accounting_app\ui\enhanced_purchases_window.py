"""
صفحة المشتريات المحسنة مع إدارة طلبات الشراء والموردين
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QTableWidget, QTableWidgetItem,
                              QHeaderView, QComboBox, QLineEdit, QSpinBox,
                              QDoubleSpinBox, QTextEdit, QGroupBox, QGridLayout,
                              QScrollArea, QSplitter, QTabWidget, QFormLayout,
                              QMessageBox, QDialog, QDialogButtonBox, QCheckBox,
                              QProgressBar, QFileDialog, QMenu, QApplication,
                              QCalendarWidget, QDateEdit, QListWidget, QListWidgetItem)
from PySide6.QtCore import Qt, QThread, pyqtSignal, QT<PERSON>r, QPropertyAnimation, QEasingCurve, QDate
from PySide6.QtGui import QFont, QColor, QPainter, QPen, QBrush, QPixmap, QAction, QCursor
import json
import random
from datetime import datetime, timedelta

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class PurchaseOrderDialog(QDialog):
    """نافذة إنشاء طلب شراء جديد"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.order_items = []
        self.total_amount = 0.0
        self.init_ui()
        self.load_suppliers()
        self.load_products()
    
    def init_ui(self):
        """إعداد واجهة النافذة"""
        self.setWindowTitle("إنشاء طلب شراء جديد")
        self.setFixedSize(700, 600)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان النافذة
        title = QLabel("📋 إنشاء طلب شراء جديد")
        title.setStyleSheet("""
            color: #17a2b8;
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            background: #e8f4f8;
            border-radius: 8px;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # معلومات الطلب الأساسية
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        info_layout = QGridLayout(info_frame)
        
        # رقم الطلب
        info_layout.addWidget(QLabel("رقم الطلب:"), 0, 0)
        self.order_number = QLineEdit(f"PO-{datetime.now().strftime('%Y%m%d%H%M')}")
        self.order_number.setReadOnly(True)
        self.order_number.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.order_number, 0, 1)
        
        # تاريخ الطلب
        info_layout.addWidget(QLabel("تاريخ الطلب:"), 0, 2)
        self.order_date = QDateEdit()
        self.order_date.setDate(QDate.currentDate())
        self.order_date.setCalendarPopup(True)
        self.order_date.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.order_date, 0, 3)
        
        # المورد
        info_layout.addWidget(QLabel("المورد:"), 1, 0)
        self.supplier_combo = QComboBox()
        self.supplier_combo.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.supplier_combo, 1, 1)
        
        # تاريخ التسليم المتوقع
        info_layout.addWidget(QLabel("تاريخ التسليم:"), 1, 2)
        self.delivery_date = QDateEdit()
        self.delivery_date.setDate(QDate.currentDate().addDays(7))
        self.delivery_date.setCalendarPopup(True)
        self.delivery_date.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.delivery_date, 1, 3)
        
        layout.addWidget(info_frame)
        
        # إضافة المنتجات
        products_frame = QFrame()
        products_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        products_layout = QVBoxLayout(products_frame)
        
        # إضافة منتج جديد
        add_product_layout = QHBoxLayout()
        
        add_product_layout.addWidget(QLabel("المنتج:"))
        self.product_combo = QComboBox()
        self.product_combo.setStyleSheet(self.get_input_style())
        add_product_layout.addWidget(self.product_combo)
        
        add_product_layout.addWidget(QLabel("الكمية:"))
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(1, 9999)
        self.quantity_spin.setStyleSheet(self.get_input_style())
        add_product_layout.addWidget(self.quantity_spin)
        
        add_product_layout.addWidget(QLabel("السعر:"))
        self.price_spin = QDoubleSpinBox()
        self.price_spin.setRange(0, 999999)
        self.price_spin.setSuffix(" ج.م")
        self.price_spin.setStyleSheet(self.get_input_style())
        add_product_layout.addWidget(self.price_spin)
        
        add_btn = QPushButton("➕ إضافة")
        add_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        add_btn.clicked.connect(self.add_product_to_order)
        add_product_layout.addWidget(add_btn)
        
        products_layout.addLayout(add_product_layout)
        
        # جدول المنتجات المطلوبة
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels(["المنتج", "الكمية", "السعر", "الإجمالي", "حذف"])
        self.items_table.setMaximumHeight(200)
        self.items_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-size: 11px;
            }
            QHeaderView::section {
                background: #17a2b8;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # تحديد عرض الأعمدة
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # الكمية
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # السعر
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # الإجمالي
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # حذف
        
        self.items_table.setColumnWidth(1, 70)   # الكمية
        self.items_table.setColumnWidth(2, 80)   # السعر
        self.items_table.setColumnWidth(3, 80)   # الإجمالي
        self.items_table.setColumnWidth(4, 50)   # حذف
        
        products_layout.addWidget(self.items_table)
        
        layout.addWidget(products_frame)
        
        # الإجمالي
        total_frame = QFrame()
        total_frame.setStyleSheet("""
            QFrame {
                background: #e8f4f8;
                border-radius: 6px;
                padding: 10px;
                border: 2px solid #17a2b8;
            }
        """)
        total_layout = QHBoxLayout(total_frame)
        
        total_label = QLabel("إجمالي الطلب:")
        total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #17a2b8;")
        
        self.total_amount_label = QLabel("0.00 ج.م")
        self.total_amount_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #17a2b8;")
        
        total_layout.addWidget(total_label)
        total_layout.addStretch()
        total_layout.addWidget(self.total_amount_label)
        
        layout.addWidget(total_frame)
        
        # ملاحظات
        notes_frame = QFrame()
        notes_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 10px;
                border: 1px solid #e9ecef;
            }
        """)
        notes_layout = QVBoxLayout(notes_frame)
        
        notes_layout.addWidget(QLabel("ملاحظات:"))
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("ملاحظات إضافية للطلب...")
        self.notes_input.setMaximumHeight(60)
        self.notes_input.setStyleSheet(self.get_input_style())
        notes_layout.addWidget(self.notes_input)
        
        layout.addWidget(notes_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("💾 حفظ الطلب")
        save_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        save_btn.clicked.connect(self.save_order)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #545b62;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        suppliers = ["شركة التقنية المتقدمة", "مؤسسة الأجهزة الذكية", "شركة المنسوجات الحديثة", "دار الكتب العلمية"]
        self.supplier_combo.addItems(suppliers)
    
    def load_products(self):
        """تحميل قائمة المنتجات"""
        products = [
            {"name": "لابتوب ديل", "price": 12000},
            {"name": "هاتف ذكي", "price": 6500},
            {"name": "قميص قطني", "price": 80},
            {"name": "كتاب برمجة", "price": 150},
            {"name": "مكواة كهربائية", "price": 200}
        ]
        
        for product in products:
            self.product_combo.addItem(product["name"], product)
    
    def add_product_to_order(self):
        """إضافة منتج إلى الطلب"""
        if self.product_combo.currentIndex() == -1:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج!")
            return
        
        product_data = self.product_combo.currentData()
        quantity = self.quantity_spin.value()
        price = self.price_spin.value()
        
        if price <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر صحيح!")
            return
        
        # البحث عن المنتج في الطلب
        for item in self.order_items:
            if item["name"] == product_data["name"]:
                QMessageBox.warning(self, "تحذير", "هذا المنتج موجود بالفعل في الطلب!")
                return
        
        # إضافة المنتج للطلب
        order_item = {
            "name": product_data["name"],
            "quantity": quantity,
            "price": price,
            "total": quantity * price
        }
        
        self.order_items.append(order_item)
        self.update_items_table()
        
        # إعادة تعيين القيم
        self.quantity_spin.setValue(1)
        self.price_spin.setValue(product_data["price"])
    
    def update_items_table(self):
        """تحديث جدول المنتجات"""
        self.items_table.setRowCount(len(self.order_items))
        self.total_amount = 0.0
        
        for row, item in enumerate(self.order_items):
            # اسم المنتج
            self.items_table.setItem(row, 0, QTableWidgetItem(item["name"]))
            
            # الكمية
            quantity_item = QTableWidgetItem(f"{item['quantity']} قطعة")
            quantity_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 1, quantity_item)
            
            # السعر
            price_item = QTableWidgetItem(f"{item['price']:.2f}")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 2, price_item)
            
            # الإجمالي
            total_item = QTableWidgetItem(f"{item['total']:.2f}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 3, total_item)
            
            # زر الحذف
            delete_btn = QPushButton("🗑️")
            delete_btn.setFixedSize(30, 25)
            delete_btn.setStyleSheet("""
                QPushButton {
                    background: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 12px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background: #c82333;
                }
            """)
            delete_btn.clicked.connect(lambda checked, r=row: self.remove_item(r))
            self.items_table.setCellWidget(row, 4, delete_btn)
            
            self.total_amount += item["total"]
        
        self.total_amount_label.setText(f"{self.total_amount:.2f} ج.م")
    
    def remove_item(self, row):
        """حذف منتج من الطلب"""
        if row < len(self.order_items):
            del self.order_items[row]
            self.update_items_table()
    
    def save_order(self):
        """حفظ طلب الشراء"""
        if not self.order_items:
            QMessageBox.warning(self, "تحذير", "يرجى إضافة منتجات للطلب!")
            return
        
        if self.supplier_combo.currentIndex() == -1:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد!")
            return
        
        order_data = {
            'order_number': self.order_number.text(),
            'order_date': self.order_date.date().toString(),
            'supplier': self.supplier_combo.currentText(),
            'delivery_date': self.delivery_date.date().toString(),
            'items': self.order_items.copy(),
            'total_amount': self.total_amount,
            'notes': self.notes_input.toPlainText().strip()
        }
        
        QMessageBox.information(self, "تم الحفظ", f"تم حفظ طلب الشراء رقم {order_data['order_number']} بنجاح!")
        self.accept()
    
    def get_input_style(self):
        return """
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit, QTextEdit {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 12px;
                background: white;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus, QTextEdit:focus {
                border-color: #17a2b8;
            }
        """


class PurchasesTableWidget(QTableWidget):
    """جدول المشتريات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_table()
        self.load_purchases_data()
    
    def init_table(self):
        """إعداد الجدول"""
        columns = [
            "رقم الطلب", "التاريخ", "المورد", "عدد المنتجات", 
            "الإجمالي", "تاريخ التسليم", "الحالة", "الإجراءات"
        ]
        
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 12px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #495057;
                padding: 12px 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        # إعدادات الجدول
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        self.verticalHeader().setVisible(False)
        
        # تحديد عرض الأعمدة
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)   # رقم الطلب
        header.setSectionResizeMode(1, QHeaderView.Fixed)   # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Stretch) # المورد
        header.setSectionResizeMode(3, QHeaderView.Fixed)   # عدد المنتجات
        header.setSectionResizeMode(4, QHeaderView.Fixed)   # الإجمالي
        header.setSectionResizeMode(5, QHeaderView.Fixed)   # تاريخ التسليم
        header.setSectionResizeMode(6, QHeaderView.Fixed)   # الحالة
        header.setSectionResizeMode(7, QHeaderView.Fixed)   # الإجراءات
        
        self.setColumnWidth(0, 100)  # رقم الطلب
        self.setColumnWidth(1, 100)  # التاريخ
        self.setColumnWidth(3, 100)  # عدد المنتجات
        self.setColumnWidth(4, 100)  # الإجمالي
        self.setColumnWidth(5, 100)  # تاريخ التسليم
        self.setColumnWidth(6, 80)   # الحالة
        self.setColumnWidth(7, 120)  # الإجراءات
    
    def load_purchases_data(self):
        """تحميل بيانات المشتريات"""
        purchases_data = [
            {"order": "PO-001", "date": "2024-01-15", "supplier": "شركة التقنية المتقدمة", 
             "items": 25, "total": 375000, "delivery": "2024-01-22", "status": "مؤكد"},
            {"order": "PO-002", "date": "2024-01-14", "supplier": "مؤسسة الأجهزة الذكية", 
             "items": 15, "total": 120000, "delivery": "2024-01-21", "status": "قيد التسليم"},
            {"order": "PO-003", "date": "2024-01-13", "supplier": "شركة المنسوجات الحديثة", 
             "items": 200, "total": 24000, "delivery": "2024-01-20", "status": "مكتمل"},
            {"order": "PO-004", "date": "2024-01-12", "supplier": "دار الكتب العلمية", 
             "items": 50, "total": 12500, "delivery": "2024-01-19", "status": "مؤكد"},
            {"order": "PO-005", "date": "2024-01-11", "supplier": "شركة الأدوات المنزلية", 
             "items": 30, "total": 9000, "delivery": "2024-01-18", "status": "ملغي"},
        ]
        
        self.setRowCount(len(purchases_data))
        
        for row, purchase in enumerate(purchases_data):
            # رقم الطلب
            order_item = QTableWidgetItem(purchase["order"])
            order_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 0, order_item)
            
            # التاريخ
            date_item = QTableWidgetItem(purchase["date"])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 1, date_item)
            
            # المورد
            self.setItem(row, 2, QTableWidgetItem(purchase["supplier"]))
            
            # عدد المنتجات
            items_item = QTableWidgetItem(f"{purchase['items']} منتج")
            items_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 3, items_item)
            
            # الإجمالي
            total_item = QTableWidgetItem(f"{purchase['total']:,.0f} ج.م")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 4, total_item)
            
            # تاريخ التسليم
            delivery_item = QTableWidgetItem(purchase["delivery"])
            delivery_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 5, delivery_item)
            
            # الحالة
            status_colors = {
                "مؤكد": {"bg": "#fff3cd", "fg": "#856404"},
                "قيد التسليم": {"bg": "#cce5ff", "fg": "#004085"},
                "مكتمل": {"bg": "#d4edda", "fg": "#155724"},
                "ملغي": {"bg": "#f8d7da", "fg": "#721c24"}
            }
            
            status_item = QTableWidgetItem(purchase["status"])
            status_item.setTextAlignment(Qt.AlignCenter)
            color = status_colors.get(purchase["status"], status_colors["مؤكد"])
            status_item.setBackground(QColor(color["bg"]))
            status_item.setForeground(QColor(color["fg"]))
            self.setItem(row, 6, status_item)
            
            # الإجراءات
            actions_widget = self.create_actions_widget(row, purchase)
            self.setCellWidget(row, 7, actions_widget)
    
    def create_actions_widget(self, row, purchase_data):
        """إنشاء أزرار الإجراءات"""
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(5, 2, 5, 2)
        actions_layout.setSpacing(3)
        
        # زر عرض التفاصيل
        view_btn = QPushButton("👁️")
        view_btn.setFixedSize(22, 22)
        view_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        view_btn.setToolTip("عرض التفاصيل")
        view_btn.clicked.connect(lambda: self.view_purchase_details(row, purchase_data))
        
        # زر تحديث الحالة
        update_btn = QPushButton("🔄")
        update_btn.setFixedSize(22, 22)
        update_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        update_btn.setToolTip("تحديث الحالة")
        update_btn.clicked.connect(lambda: self.update_status(row, purchase_data))
        
        # زر طباعة
        print_btn = QPushButton("🖨️")
        print_btn.setFixedSize(22, 22)
        print_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        print_btn.setToolTip("طباعة الطلب")
        print_btn.clicked.connect(lambda: self.print_order(row, purchase_data))
        
        actions_layout.addWidget(view_btn)
        actions_layout.addWidget(update_btn)
        actions_layout.addWidget(print_btn)
        actions_layout.addStretch()
        
        return actions_widget
    
    def view_purchase_details(self, row, purchase_data):
        """عرض تفاصيل طلب الشراء"""
        details_text = f"""
        📋 تفاصيل طلب الشراء:
        
        🔢 رقم الطلب: {purchase_data['order']}
        📅 تاريخ الطلب: {purchase_data['date']}
        🏢 المورد: {purchase_data['supplier']}
        📦 عدد المنتجات: {purchase_data['items']} منتج
        💰 الإجمالي: {purchase_data['total']:,.0f} ج.م
        🚚 تاريخ التسليم: {purchase_data['delivery']}
        ✅ الحالة: {purchase_data['status']}
        """
        
        QMessageBox.information(self, "تفاصيل طلب الشراء", details_text)
    
    def update_status(self, row, purchase_data):
        """تحديث حالة الطلب"""
        statuses = ["مؤكد", "قيد التسليم", "مكتمل", "ملغي"]
        current_status = purchase_data['status']
        
        # نافذة اختيار الحالة الجديدة
        from PySide6.QtWidgets import QInputDialog
        new_status, ok = QInputDialog.getItem(
            self, "تحديث الحالة", "اختر الحالة الجديدة:",
            statuses, statuses.index(current_status), False
        )
        
        if ok and new_status != current_status:
            QMessageBox.information(self, "تحديث الحالة", 
                f"تم تحديث حالة الطلب {purchase_data['order']} إلى: {new_status}")
            self.load_purchases_data()
    
    def print_order(self, row, purchase_data):
        """طباعة طلب الشراء"""
        QMessageBox.information(self, "طباعة", f"تم إرسال طلب الشراء {purchase_data['order']} للطباعة!")


class EnhancedPurchasesWindow(QWidget):
    """نافذة المشتريات المحسنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # المحتوى الرئيسي
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(15, 15, 15, 15)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        content_layout.addWidget(toolbar)
        
        # الجدول
        self.purchases_table = PurchasesTableWidget()
        content_layout.addWidget(self.purchases_table)
        
        main_layout.addWidget(content_frame)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
    
    def create_header(self):
        """إنشاء الرأس"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #17a2b8, stop:0.5 #20c997, stop:1 #17a2b8);
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # معلومات الصفحة
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        title = QLabel("🏭 إدارة المشتريات")
        title.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
        """)
        
        subtitle = QLabel("إدارة طلبات الشراء والموردين مع تحليلات شاملة ومتابعة دقيقة")
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-top: 5px;
        """)
        
        info_layout.addWidget(title)
        info_layout.addWidget(subtitle)
        
        # الإحصائيات السريعة
        stats_widget = self.create_purchases_stats()
        
        layout.addWidget(info_widget, 7)
        layout.addWidget(stats_widget, 3)
        
        return header_frame
    
    def create_purchases_stats(self):
        """إنشاء إحصائيات المشتريات"""
        stats_widget = QWidget()
        stats_layout = QHBoxLayout(stats_widget)
        stats_layout.setSpacing(10)
        
        # طلبات الشهر
        month_orders = self.create_stat_card("📋", "156", "طلبات الشهر")
        stats_layout.addWidget(month_orders)
        
        # القيمة الإجمالية
        total_value = self.create_stat_card("💰", "1.2M", "القيمة الإجمالية")
        stats_layout.addWidget(total_value)
        
        # طلبات معلقة
        pending_orders = self.create_stat_card("⏳", "23", "طلبات معلقة")
        stats_layout.addWidget(pending_orders)
        
        return stats_widget
    
    def create_stat_card(self, icon, value, label):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("color: white; font-size: 20px;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 10px;")
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(label_widget)
        
        return card
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        
        # زر طلب شراء جديد
        new_order_btn = QPushButton("➕ طلب شراء جديد")
        new_order_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        new_order_btn.clicked.connect(self.create_new_order)
        
        # زر استيراد
        import_btn = QPushButton("📥 استيراد")
        import_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        import_btn.clicked.connect(self.import_orders)
        
        # زر تصدير
        export_btn = QPushButton("📤 تصدير")
        export_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        export_btn.clicked.connect(self.export_orders)
        
        # زر التقارير
        reports_btn = QPushButton("📊 التقارير")
        reports_btn.setStyleSheet("""
            QPushButton {
                background: #6f42c1;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #5a32a3;
            }
        """)
        reports_btn.clicked.connect(self.generate_reports)
        
        layout.addWidget(new_order_btn)
        layout.addWidget(import_btn)
        layout.addWidget(export_btn)
        layout.addWidget(reports_btn)
        layout.addStretch()
        
        # فلتر سريع
        filter_combo = QComboBox()
        filter_combo.addItems(["جميع الطلبات", "مؤكد", "قيد التسليم", "مكتمل", "ملغي"])
        filter_combo.setStyleSheet("""
            QComboBox {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
                font-size: 12px;
            }
        """)
        layout.addWidget(QLabel("فلتر:"))
        layout.addWidget(filter_combo)
        
        return toolbar_frame
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFixedHeight(40)
        status_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 8px 15px;
            }
        """)
        
        layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز - نظام المشتريات متاح")
        self.status_label.setStyleSheet("color: #17a2b8; font-weight: bold; font-size: 12px;")
        
        self.orders_count_label = QLabel("5 طلبات")
        self.orders_count_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        
        self.total_value_label = QLabel("إجمالي القيمة: 540,500 ج.م")
        self.total_value_label.setStyleSheet("color: #007bff; font-weight: bold; font-size: 12px;")
        
        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.orders_count_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.total_value_label)
        
        return status_frame
    
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_data)
        self.update_timer.start(60000)  # تحديث كل دقيقة
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.status_label.setText(f"تم التحديث - {datetime.now().strftime('%H:%M')}")
    
    def create_new_order(self):
        """إنشاء طلب شراء جديد"""
        dialog = PurchaseOrderDialog(parent=self)
        if dialog.exec() == QDialog.Accepted:
            self.purchases_table.load_purchases_data()
            self.status_label.setText("تم إنشاء طلب شراء جديد")
    
    def import_orders(self):
        """استيراد طلبات الشراء"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "استيراد طلبات الشراء", "",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;All Files (*)"
        )
        
        if file_path:
            QMessageBox.information(self, "استيراد", f"تم استيراد طلبات الشراء من:\n{file_path}")
    
    def export_orders(self):
        """تصدير طلبات الشراء"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير طلبات الشراء", "purchase_orders.xlsx",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;PDF Files (*.pdf)"
        )
        
        if file_path:
            QMessageBox.information(self, "تصدير", f"تم تصدير طلبات الشراء إلى:\n{file_path}")
    
    def generate_reports(self):
        """إنشاء التقارير"""
        QMessageBox.information(self, "التقارير", "تم إنشاء تقارير المشتريات بنجاح!")"""
صفحة المشتريات المحسنة مع إدارة طلبات الشراء والموردين
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QTableWidget, QTableWidgetItem,
                              QHeaderView, QComboBox, QLineEdit, QSpinBox,
                              QDoubleSpinBox, QTextEdit, QGroupBox, QGridLayout,
                              QScrollArea, QSplitter, QTabWidget, QFormLayout,
                              QMessageBox, QDialog, QDialogButtonBox, QCheckBox,
                              QProgressBar, QFileDialog, QMenu, QApplication,
                              QCalendarWidget, QDateEdit, QListWidget, QListWidgetItem,
                              QTreeWidget, QTreeWidgetItem)
from PySide6.QtCore import Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QDate
from PySide6.QtGui import QFont, QColor, QPainter, QPen, QBrush, QPixmap, QAction, QCursor
import json
import random
from datetime import datetime, timedelta

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class PurchaseOrderDialog(QDialog):
    """نافذة إنشاء طلب شراء جديد"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.order_items = []
        self.total_amount = 0.0
        self.init_ui()
        self.load_suppliers()
        self.load_products()
    
    def init_ui(self):
        """إعداد واجهة النافذة"""
        self.setWindowTitle("إنشاء طلب شراء جديد")
        self.setFixedSize(700, 600)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان النافذة
        title = QLabel("📋 إنشاء طلب شراء جديد")
        title.setStyleSheet("""
            color: #17a2b8;
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            background: #e8f4f8;
            border-radius: 8px;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # معلومات الطلب الأساسية
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        info_layout = QGridLayout(info_frame)
        
        # رقم الطلب
        info_layout.addWidget(QLabel("رقم الطلب:"), 0, 0)
        self.order_number = QLineEdit(f"PO-{datetime.now().strftime('%Y%m%d%H%M')}")
        self.order_number.setReadOnly(True)
        self.order_number.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.order_number, 0, 1)
        
        # تاريخ الطلب
        info_layout.addWidget(QLabel("تاريخ الطلب:"), 0, 2)
        self.order_date = QDateEdit()
        self.order_date.setDate(QDate.currentDate())
        self.order_date.setCalendarPopup(True)
        self.order_date.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.order_date, 0, 3)
        
        # المورد
        info_layout.addWidget(QLabel("المورد:"), 1, 0)
        self.supplier_combo = QComboBox()
        self.supplier_combo.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.supplier_combo, 1, 1)
        
        # تاريخ التسليم المتوقع
        info_layout.addWidget(QLabel("تاريخ التسليم:"), 1, 2)
        self.delivery_date = QDateEdit()
        self.delivery_date.setDate(QDate.currentDate().addDays(7))
        self.delivery_date.setCalendarPopup(True)
        self.delivery_date.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.delivery_date, 1, 3)
        
        layout.addWidget(info_frame)
        
        # إضافة المنتجات
        products_frame = QFrame()
        products_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        products_layout = QVBoxLayout(products_frame)
        
        # إضافة منتج جديد
        add_product_layout = QHBoxLayout()
        
        add_product_layout.addWidget(QLabel("المنتج:"))
        self.product_combo = QComboBox()
        self.product_combo.setStyleSheet(self.get_input_style())
        add_product_layout.addWidget(self.product_combo)
        
        add_product_layout.addWidget(QLabel("الكمية:"))
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(1, 9999)
        self.quantity_spin.setStyleSheet(self.get_input_style())
        add_product_layout.addWidget(self.quantity_spin)
        
        add_product_layout.addWidget(QLabel("السعر:"))
        self.price_spin = QDoubleSpinBox()
        self.price_spin.setRange(0, 999999)
        self.price_spin.setSuffix(" ج.م")
        self.price_spin.setStyleSheet(self.get_input_style())
        add_product_layout.addWidget(self.price_spin)
        
        add_btn = QPushButton("➕ إضافة")
        add_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        add_btn.clicked.connect(self.add_product_to_order)
        add_product_layout.addWidget(add_btn)
        
        products_layout.addLayout(add_product_layout)
        
        # جدول المنتجات المطلوبة
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels(["المنتج", "الكمية", "السعر", "الإجمالي", "حذف"])
        self.items_table.setMaximumHeight(200)
        self.items_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-size: 11px;
            }
            QHeaderView::section {
                background: #17a2b8;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # تحديد عرض الأعمدة
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # الكمية
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # السعر
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # الإجمالي
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # حذف
        
        self.items_table.setColumnWidth(1, 70)   # الكمية
        self.items_table.setColumnWidth(2, 80)   # السعر
        self.items_table.setColumnWidth(3, 80)   # الإجمالي
        self.items_table.setColumnWidth(4, 50)   # حذف
        
        products_layout.addWidget(self.items_table)
        
        layout.addWidget(products_frame)
        
        # الإجمالي
        total_frame = QFrame()
        total_frame.setStyleSheet("""
            QFrame {
                background: #e8f4f8;
                border-radius: 6px;
                padding: 10px;
                border: 2px solid #17a2b8;
            }
        """)
        total_layout = QHBoxLayout(total_frame)
        
        total_label = QLabel("إجمالي الطلب:")
        total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #17a2b8;")
        
        self.total_amount_label = QLabel("0.00 ج.م")
        self.total_amount_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #17a2b8;")
        
        total_layout.addWidget(total_label)
        total_layout.addStretch()
        total_layout.addWidget(self.total_amount_label)
        
        layout.addWidget(total_frame)
        
        # ملاحظات
        notes_frame = QFrame()
        notes_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 10px;
                border: 1px solid #e9ecef;
            }
        """)
        notes_layout = QVBoxLayout(notes_frame)
        
        notes_layout.addWidget(QLabel("ملاحظات:"))
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("ملاحظات إضافية للطلب...")
        self.notes_input.setMaximumHeight(60)
        self.notes_input.setStyleSheet(self.get_input_style())
        notes_layout.addWidget(self.notes_input)
        
        layout.addWidget(notes_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("💾 حفظ الطلب")
        save_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        save_btn.clicked.connect(self.save_order)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #545b62;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        suppliers = ["شركة التقنية المتقدمة", "مؤسسة الأجهزة الذكية", "شركة المنسوجات الحديثة", "دار الكتب العلمية"]
        self.supplier_combo.addItems(suppliers)
    
    def load_products(self):
        """تحميل قائمة المنتجات"""
        products = [
            {"name": "لابتوب ديل", "price": 12000},
            {"name": "هاتف ذكي", "price": 6500},
            {"name": "قميص قطني", "price": 80},
            {"name": "كتاب برمجة", "price": 150},
            {"name": "مكواة كهربائية", "price": 200}
        ]
        
        for product in products:
            self.product_combo.addItem(product["name"], product)
    
    def add_product_to_order(self):
        """إضافة منتج إلى الطلب"""
        if self.product_combo.currentIndex() == -1:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج!")
            return
        
        product_data = self.product_combo.currentData()
        quantity = self.quantity_spin.value()
        price = self.price_spin.value()
        
        if price <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر صحيح!")
            return
        
        # البحث عن المنتج في الطلب
        for item in self.order_items:
            if item["name"] == product_data["name"]:
                QMessageBox.warning(self, "تحذير", "هذا المنتج موجود بالفعل في الطلب!")
                return
        
        # إضافة المنتج للطلب
        order_item = {
            "name": product_data["name"],
            "quantity": quantity,
            "price": price,
            "total": quantity * price
        }
        
        self.order_items.append(order_item)
        self.update_items_table()
        
        # إعادة تعيين القيم
        self.quantity_spin.setValue(1)
        self.price_spin.setValue(product_data["price"])
    
    def update_items_table(self):
        """تحديث جدول المنتجات"""
        self.items_table.setRowCount(len(self.order_items))
        self.total_amount = 0.0
        
        for row, item in enumerate(self.order_items):
            # اسم المنتج
            self.items_table.setItem(row, 0, QTableWidgetItem(item["name"]))
            
            # الكمية
            quantity_item = QTableWidgetItem(f"{item['quantity']} قطعة")
            quantity_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 1, quantity_item)
            
            # السعر
            price_item = QTableWidgetItem(f"{item['price']:.2f}")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 2, price_item)
            
            # الإجمالي
            total_item = QTableWidgetItem(f"{item['total']:.2f}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 3, total_item)
            
            # زر الحذف
            delete_btn = QPushButton("🗑️")
            delete_btn.setFixedSize(30, 25)
            delete_btn.setStyleSheet("""
                QPushButton {
                    background: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 12px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background: #c82333;
                }
            """)
            delete_btn.clicked.connect(lambda checked, r=row: self.remove_item(r))
            self.items_table.setCellWidget(row, 4, delete_btn)
            
            self.total_amount += item["total"]
        
        self.total_amount_label.setText(f"{self.total_amount:.2f} ج.م")
    
    def remove_item(self, row):
        """حذف منتج من الطلب"""
        if row < len(self.order_items):
            del self.order_items[row]
            self.update_items_table()
    
    def save_order(self):
        """حفظ طلب الشراء"""
        if not self.order_items:
            QMessageBox.warning(self, "تحذير", "يرجى إضافة منتجات للطلب!")
            return
        
        if self.supplier_combo.currentIndex() == -1:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد!")
            return
        
        order_data = {
            'order_number': self.order_number.text(),
            'order_date': self.order_date.date().toString(),
            'supplier': self.supplier_combo.currentText(),
            'delivery_date': self.delivery_date.date().toString(),
            'items': self.order_items.copy(),
            'total_amount': self.total_amount,
            'notes': self.notes_input.toPlainText().strip()
        }
        
        QMessageBox.information(self, "تم الحفظ", f"تم حفظ طلب الشراء رقم {order_data['order_number']} بنجاح!")
        self.accept()
    
    def get_input_style(self):
        return """
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit, QTextEdit {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 12px;
                background: white;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus, QTextEdit:focus {
                border-color: #17a2b8;
            }
        """


class PurchasesAnalyticsWidget(QWidget):
    """واجهة تحليلات المشتريات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة التحليلات"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان التحليلات
        title = QLabel("📊 تحليلات المشتريات")
        title.setStyleSheet("""
            color: #17a2b8;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # الإحصائيات الرئيسية
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 15px;
            }
        """)
        stats_layout = QGridLayout(stats_frame)
        
        # إجمالي المشتريات
        total_purchases = self.create_stat_card("💰", "1,234,567", "إجمالي المشتريات", "#17a2b8")
        stats_layout.addWidget(total_purchases, 0, 0)
        
        # عدد الطلبات
        total_orders = self.create_stat_card("📋", "156", "طلبات الشراء", "#28a745")
        stats_layout.addWidget(total_orders, 0, 1)
        
        # متوسط قيمة الطلب
        avg_order = self.create_stat_card("📊", "7,916", "متوسط الطلب", "#ffc107")
        stats_layout.addWidget(avg_order, 1, 0)
        
        # عدد الموردين
        suppliers_count = self.create_stat_card("🏢", "23", "الموردين النشطين", "#6f42c1")
        stats_layout.addWidget(suppliers_count, 1, 1)
        
        layout.addWidget(stats_frame)
        
        # أفضل الموردين
        top_suppliers_frame = QFrame()
        top_suppliers_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 15px;
            }
        """)
        suppliers_layout = QVBoxLayout(top_suppliers_frame)
        
        suppliers_title = QLabel("🏆 أفضل الموردين")
        suppliers_title.setStyleSheet("""
            color: #495057;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        suppliers_layout.addWidget(suppliers_title)
        
        # قائمة أفضل الموردين
        suppliers_data = [
            {"name": "شركة التقنية المتقدمة", "amount": 450000, "orders": 45},
            {"name": "مؤسسة الأجهزة الذكية", "amount": 380000, "orders": 32},
            {"name": "شركة المنسوجات الحديثة", "amount": 280000, "orders": 28},
            {"name": "دار الكتب العلمية", "amount": 124567, "orders": 51}
        ]
        
        for i, supplier in enumerate(suppliers_data):
            supplier_widget = self.create_supplier_item(i+1, supplier)
            suppliers_layout.addWidget(supplier_widget)
        
        layout.addWidget(top_suppliers_frame)
        
        # الرسم البياني (محاكاة)
        chart_frame = QFrame()
        chart_frame.setFixedHeight(150)
        chart_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 2px dashed #dee2e6;
            }
        """)
        
        chart_layout = QVBoxLayout(chart_frame)
        chart_label = QLabel("📈 رسم بياني للمشتريات الشهرية\n(سيتم إضافته قريباً)")
        chart_label.setAlignment(Qt.AlignCenter)
        chart_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        chart_layout.addWidget(chart_label)
        
        layout.addWidget(chart_frame)
        layout.addStretch()
    
    def create_stat_card(self, icon, value, label, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: white;
                border-left: 4px solid {color};
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }}
            QFrame:hover {{
                background: #f8f9fa;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setSpacing(8)
        
        # الأيقونة والقيمة
        top_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"font-size: 24px; color: {color};")
        
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            color: {color};
            font-size: 18px;
            font-weight: bold;
        """)
        value_label.setAlignment(Qt.AlignRight)
        
        top_layout.addWidget(icon_label)
        top_layout.addStretch()
        top_layout.addWidget(value_label)
        
        # التسمية
        label_widget = QLabel(label)
        label_widget.setStyleSheet("""
            color: #6c757d;
            font-size: 11px;
            font-weight: bold;
        """)
        
        layout.addLayout(top_layout)
        layout.addWidget(label_widget)
        
        return card
    
    def create_supplier_item(self, rank, supplier):
        """إنشاء عنصر مورد"""
        item_frame = QFrame()
        item_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 4px;
                padding: 10px;
                margin: 2px 0;
                border: 1px solid #e9ecef;
            }
            QFrame:hover {
                background: #e9ecef;
            }
        """)
        
        layout = QHBoxLayout(item_frame)
        
        # الترتيب
        rank_label = QLabel(f"#{rank}")
        rank_label.setFixedSize(25, 25)
        rank_label.setStyleSheet("""
            background: #17a2b8;
            color: white;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        """)
        rank_label.setAlignment(Qt.AlignCenter)
        
        # معلومات المورد
        info_layout = QVBoxLayout()
        info_layout.setSpacing(2)
        
        name_label = QLabel(supplier["name"])
        name_label.setStyleSheet("font-size: 12px; font-weight: bold; color: #495057;")
        
        details_label = QLabel(f"{supplier['amount']:,.0f} ج.م • {supplier['orders']} طلب")
        details_label.setStyleSheet("font-size: 10px; color: #6c757d;")
        
        info_layout.addWidget(name_label)
        info_layout.addWidget(details_label)
        
        layout.addWidget(rank_label)
        layout.addLayout(info_layout)
        layout.addStretch()
        
        return item_frame


class PurchasesTableWidget(QTableWidget):
    """جدول المشتريات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_table()
        self.load_purchases_data()
    
    def init_table(self):
        """إعداد الجدول"""
        columns = [
            "رقم الطلب", "التاريخ", "المورد", "عدد المنتجات", 
            "الإجمالي", "تاريخ التسليم", "الحالة", "الإجراءات"
        ]
        
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 12px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #495057;
                padding: 12px 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        # إعدادات الجدول
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        self.verticalHeader().setVisible(False)
        
        # تحديد عرض الأعمدة
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)   # رقم الطلب
        header.setSectionResizeMode(1, QHeaderView.Fixed)   # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Stretch) # المورد
        header.setSectionResizeMode(3, QHeaderView.Fixed)   # عدد المنتجات
        header.setSectionResizeMode(4, QHeaderView.Fixed)   # الإجمالي
        header.setSectionResizeMode(5, QHeaderView.Fixed)   # تاريخ التسليم
        header.setSectionResizeMode(6, QHeaderView.Fixed)   # الحالة
        header.setSectionResizeMode(7, QHeaderView.Fixed)   # الإجراءات
        
        self.setColumnWidth(0, 100)  # رقم الطلب
        self.setColumnWidth(1, 100)  # التاريخ
        self.setColumnWidth(3, 100)  # عدد المنتجات
        self.setColumnWidth(4, 100)  # الإجمالي
        self.setColumnWidth(5, 100)  # تاريخ التسليم
        self.setColumnWidth(6, 80)   # الحالة
        self.setColumnWidth(7, 120)  # الإجراءات
    
    def load_purchases_data(self):
        """تحميل بيانات المشتريات"""
        purchases_data = [
            {"order": "PO-001", "date": "2024-01-15", "supplier": "شركة التقنية المتقدمة", 
             "items": 25, "total": 375000, "delivery": "2024-01-22", "status": "مؤكد"},
            {"order": "PO-002", "date": "2024-01-14", "supplier": "مؤسسة الأجهزة الذكية", 
             "items": 15, "total": 120000, "delivery": "2024-01-21", "status": "قيد التسليم"},
            {"order": "PO-003", "date": "2024-01-13", "supplier": "شركة المنسوجات الحديثة", 
             "items": 200, "total": 24000, "delivery": "2024-01-20", "status": "مكتمل"},
            {"order": "PO-004", "date": "2024-01-12", "supplier": "دار الكتب العلمية", 
             "items": 50, "total": 12500, "delivery": "2024-01-19", "status": "مؤكد"},
            {"order": "PO-005", "date": "2024-01-11", "supplier": "شركة الأدوات المنزلية", 
             "items": 30, "total": 9000, "delivery": "2024-01-18", "status": "ملغي"},
        ]
        
        self.setRowCount(len(purchases_data))
        
        for row, purchase in enumerate(purchases_data):
            # رقم الطلب
            order_item = QTableWidgetItem(purchase["order"])
            order_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 0, order_item)
            
            # التاريخ
            date_item = QTableWidgetItem(purchase["date"])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 1, date_item)
            
            # المورد
            self.setItem(row, 2, QTableWidgetItem(purchase["supplier"]))
            
            # عدد المنتجات
            items_item = QTableWidgetItem(f"{purchase['items']} منتج")
            items_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 3, items_item)
            
            # الإجمالي
            total_item = QTableWidgetItem(f"{purchase['total']:,.0f} ج.م")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 4, total_item)
            
            # تاريخ التسليم
            delivery_item = QTableWidgetItem(purchase["delivery"])
            delivery_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 5, delivery_item)
            
            # الحالة
            status_colors = {
                "مؤكد": {"bg": "#fff3cd", "fg": "#856404"},
                "قيد التسليم": {"bg": "#cce5ff", "fg": "#004085"},
                "مكتمل": {"bg": "#d4edda", "fg": "#155724"},
                "ملغي": {"bg": "#f8d7da", "fg": "#721c24"}
            }
            
            status_item = QTableWidgetItem(purchase["status"])
            status_item.setTextAlignment(Qt.AlignCenter)
            color = status_colors.get(purchase["status"], status_colors["مؤكد"])
            status_item.setBackground(QColor(color["bg"]))
            status_item.setForeground(QColor(color["fg"]))
            self.setItem(row, 6, status_item)
            
            # الإجراءات
            actions_widget = self.create_actions_widget(row, purchase)
            self.setCellWidget(row, 7, actions_widget)
    
    def create_actions_widget(self, row, purchase_data):
        """إنشاء أزرار الإجراءات"""
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(5, 2, 5, 2)
        actions_layout.setSpacing(3)
        
        # زر عرض التفاصيل
        view_btn = QPushButton("👁️")
        view_btn.setFixedSize(22, 22)
        view_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        view_btn.setToolTip("عرض التفاصيل")
        view_btn.clicked.connect(lambda: self.view_purchase_details(row, purchase_data))
        
        # زر تحديث الحالة
        update_btn = QPushButton("🔄")
        update_btn.setFixedSize(22, 22)
        update_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        update_btn.setToolTip("تحديث الحالة")
        update_btn.clicked.connect(lambda: self.update_status(row, purchase_data))
        
        # زر طباعة
        print_btn = QPushButton("🖨️")
        print_btn.setFixedSize(22, 22)
        print_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        print_btn.setToolTip("طباعة الطلب")
        print_btn.clicked.connect(lambda: self.print_order(row, purchase_data))
        
        actions_layout.addWidget(view_btn)
        actions_layout.addWidget(update_btn)
        actions_layout.addWidget(print_btn)
        actions_layout.addStretch()
        
        return actions_widget
    
    def view_purchase_details(self, row, purchase_data):
        """عرض تفاصيل طلب الشراء"""
        details_text = f"""
        📋 تفاصيل طلب الشراء:
        
        🔢 رقم الطلب: {purchase_data['order']}
        📅 تاريخ الطلب: {purchase_data['date']}
        🏢 المورد: {purchase_data['supplier']}
        📦 عدد المنتجات: {purchase_data['items']} منتج
        💰 الإجمالي: {purchase_data['total']:,.0f} ج.م
        🚚 تاريخ التسليم: {purchase_data['delivery']}
        ✅ الحالة: {purchase_data['status']}
        """
        
        QMessageBox.information(self, "تفاصيل طلب الشراء", details_text)
    
    def update_status(self, row, purchase_data):
        """تحديث حالة الطلب"""
        statuses = ["مؤكد", "قيد التسليم", "مكتمل", "ملغي"]
        current_status = purchase_data['status']
        
        # نافذة اختيار الحالة الجديدة
        from PySide6.QtWidgets import QInputDialog
        new_status, ok = QInputDialog.getItem(
            self, "تحديث الحالة", "اختر الحالة الجديدة:",
            statuses, statuses.index(current_status), False
        )
        
        if ok and new_status != current_status:
            QMessageBox.information(self, "تحديث الحالة", 
                f"تم تحديث حالة الطلب {purchase_data['order']} إلى: {new_status}")
            self.load_purchases_data()
    
    def print_order(self, row, purchase_data):
        """طباعة طلب الشراء"""
        QMessageBox.information(self, "طباعة", f"تم إرسال طلب الشراء {purchase_data['order']} للطباعة!")


class EnhancedPurchasesWindow(QWidget):
    """نافذة المشتريات المحسنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # المحتوى الرئيسي
        content_splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - التحليلات
        left_panel = self.create_left_panel()
        left_panel.setMaximumWidth(350)
        content_splitter.addWidget(left_panel)
        
        # الجانب الأيمن - إدارة المشتريات
        right_panel = self.create_right_panel()
        content_splitter.addWidget(right_panel)
        
        content_splitter.setSizes([350, 850])
        main_layout.addWidget(content_splitter)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
    
    def create_header(self):
        """إنشاء الرأس"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #17a2b8, stop:0.5 #20c997, stop:1 #17a2b8);
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # معلومات الصفحة
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        title = QLabel("🏭 إدارة المشتريات")
        title.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
        """)
        
        subtitle = QLabel("إدارة طلبات الشراء والموردين مع تحليلات شاملة ومتابعة دقيقة")
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-top: 5px;
        """)
        
        info_layout.addWidget(title)
        info_layout.addWidget(subtitle)
        
        # الإحصائيات السريعة
        stats_widget = self.create_purchases_stats()
        
        layout.addWidget(info_widget, 7)
        layout.addWidget(stats_widget, 3)
        
        return header_frame
    
    def create_purchases_stats(self):
        """إنشاء إحصائيات المشتريات"""
        stats_widget = QWidget()
        stats_layout = QHBoxLayout(stats_widget)
        stats_layout.setSpacing(10)
        
        # طلبات الشهر
        month_orders = self.create_stat_card("📋", "156", "طلبات الشهر")
        stats_layout.addWidget(month_orders)
        
        # القيمة الإجمالية
        total_value = self.create_stat_card("💰", "1.2M", "القيمة الإجمالية")
        stats_layout.addWidget(total_value)
        
        # طلبات معلقة
        pending_orders = self.create_stat_card("⏳", "23", "طلبات معلقة")
        stats_layout.addWidget(pending_orders)
        
        return stats_widget
    
    def create_stat_card(self, icon, value, label):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("color: white; font-size: 20px;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 10px;")
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(label_widget)
        
        return card
    
    def create_left_panel(self):
        """إنشاء اللوحة اليسرى"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(15)
        
        # التحليلات
        self.analytics_widget = PurchasesAnalyticsWidget()
        left_layout.addWidget(self.analytics_widget)
        
        return left_widget
    
    def create_right_panel(self):
        """إنشاء اللوحة اليمنى"""
        right_frame = QFrame()
        right_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        
        layout = QVBoxLayout(right_frame)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # الجدول
        self.purchases_table = PurchasesTableWidget()
        layout.addWidget(self.purchases_table)
        
        return right_frame
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        
        # زر طلب شراء جديد
        new_order_btn = QPushButton("➕ طلب شراء جديد")
        new_order_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        new_order_btn.clicked.connect(self.create_new_order)
        
        # زر استيراد
        import_btn = QPushButton("📥 استيراد")
        import_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        import_btn.clicked.connect(self.import_orders)
        
        # زر تصدير
        export_btn = QPushButton("📤 تصدير")
        export_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        export_btn.clicked.connect(self.export_orders)
        
        # زر التقارير
        reports_btn = QPushButton("📊 التقارير")
        reports_btn.setStyleSheet("""
            QPushButton {
                background: #6f42c1;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #5a32a3;
            }
        """)
        reports_btn.clicked.connect(self.generate_reports)
        
        layout.addWidget(new_order_btn)
        layout.addWidget(import_btn)
        layout.addWidget(export_btn)
        layout.addWidget(reports_btn)
        layout.addStretch()
        
        # فلتر سريع
        filter_combo = QComboBox()
        filter_combo.addItems(["جميع الطلبات", "مؤكد", "قيد التسليم", "مكتمل", "ملغي"])
        filter_combo.setStyleSheet("""
            QComboBox {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
                font-size: 12px;
            }
        """)
        layout.addWidget(QLabel("فلتر:"))
        layout.addWidget(filter_combo)
        
        return toolbar_frame
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFixedHeight(40)
        status_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 8px 15px;
            }
        """)
        
        layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز - نظام المشتريات متاح")
        self.status_label.setStyleSheet("color: #17a2b8; font-weight: bold; font-size: 12px;")
        
        self.orders_count_label = QLabel("5 طلبات")
        self.orders_count_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        
        self.total_value_label = QLabel("إجمالي القيمة: 540,500 ج.م")
        self.total_value_label.setStyleSheet("color: #007bff; font-weight: bold; font-size: 12px;")
        
        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.orders_count_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.total_value_label)
        
        return status_frame
    
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_data)
        self.update_timer.start(60000)  # تحديث كل دقيقة
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.status_label.setText(f"تم التحديث - {datetime.now().strftime('%H:%M')}")
    
    def create_new_order(self):
        """إنشاء طلب شراء جديد"""
        dialog = PurchaseOrderDialog(parent=self)
        if dialog.exec() == QDialog.Accepted:
            self.purchases_table.load_purchases_data()
            self.status_label.setText("تم إنشاء طلب شراء جديد")
    
    def import_orders(self):
        """استيراد طلبات الشراء"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "استيراد طلبات الشراء", "",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;All Files (*)"
        )
        
        if file_path:
            QMessageBox.information(self, "استيراد", f"تم استيراد طلبات الشراء من:\n{file_path}")
    
    def export_orders(self):
        """تصدير طلبات الشراء"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير طلبات الشراء", "purchase_orders.xlsx",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;PDF Files (*.pdf)"
        )
        
        if file_path:
            QMessageBox.information(self, "تصدير", f"تم تصدير طلبات الشراء إلى:\n{file_path}")
    
    def generate_reports(self):
        """إنشاء التقارير"""
        QMessageBox.information(self, "التقارير", "تم إنشاء تقارير المشتريات بنجاح!")"""
صفحة المشتريات المحسنة مع إدارة طلبات الشراء والموردين
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QTableWidget, QTableWidgetItem,
                              QHeaderView, QComboBox, QLineEdit, QSpinBox,
                              QDoubleSpinBox, QTextEdit, QGroupBox, QGridLayout,
                              QScrollArea, QSplitter, QTabWidget, QFormLayout,
                              QMessageBox, QDialog, QDialogButtonBox, QCheckBox,
                              QProgressBar, QFileDialog, QMenu, QApplication,
                              QCalendarWidget, QDateEdit, QListWidget, QListWidgetItem,
                              QTreeWidget, QTreeWidgetItem)
from PySide6.QtCore import Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QDate
from PySide6.QtGui import QFont, QColor, QPainter, QPen, QBrush, QPixmap, QAction, QCursor
import json
import random
from datetime import datetime, timedelta

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class PurchaseOrderDialog(QDialog):
    """نافذة إنشاء طلب شراء جديد"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.order_items = []
        self.total_amount = 0.0
        self.init_ui()
        self.load_suppliers()
        self.load_products()
    
    def init_ui(self):
        """إعداد واجهة النافذة"""
        self.setWindowTitle("إنشاء طلب شراء جديد")
        self.setFixedSize(700, 600)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان النافذة
        title = QLabel("📋 إنشاء طلب شراء جديد")
        title.setStyleSheet("""
            color: #17a2b8;
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            background: #e8f4f8;
            border-radius: 8px;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # معلومات الطلب الأساسية
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        info_layout = QGridLayout(info_frame)
        
        # رقم الطلب
        info_layout.addWidget(QLabel("رقم الطلب:"), 0, 0)
        self.order_number = QLineEdit(f"PO-{datetime.now().strftime('%Y%m%d%H%M')}")
        self.order_number.setReadOnly(True)
        self.order_number.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.order_number, 0, 1)
        
        # تاريخ الطلب
        info_layout.addWidget(QLabel("تاريخ الطلب:"), 0, 2)
        self.order_date = QDateEdit()
        self.order_date.setDate(QDate.currentDate())
        self.order_date.setCalendarPopup(True)
        self.order_date.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.order_date, 0, 3)
        
        # المورد
        info_layout.addWidget(QLabel("المورد:"), 1, 0)
        self.supplier_combo = QComboBox()
        self.supplier_combo.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.supplier_combo, 1, 1)
        
        # تاريخ التسليم المتوقع
        info_layout.addWidget(QLabel("تاريخ التسليم:"), 1, 2)
        self.delivery_date = QDateEdit()
        self.delivery_date.setDate(QDate.currentDate().addDays(7))
        self.delivery_date.setCalendarPopup(True)
        self.delivery_date.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.delivery_date, 1, 3)
        
        layout.addWidget(info_frame)
        
        # إضافة المنتجات
        products_frame = QFrame()
        products_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        products_layout = QVBoxLayout(products_frame)
        
        # إضافة منتج جديد
        add_product_layout = QHBoxLayout()
        
        add_product_layout.addWidget(QLabel("المنتج:"))
        self.product_combo = QComboBox()
        self.product_combo.setStyleSheet(self.get_input_style())
        add_product_layout.addWidget(self.product_combo)
        
        add_product_layout.addWidget(QLabel("الكمية:"))
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(1, 9999)
        self.quantity_spin.setStyleSheet(self.get_input_style())
        add_product_layout.addWidget(self.quantity_spin)
        
        add_product_layout.addWidget(QLabel("السعر:"))
        self.price_spin = QDoubleSpinBox()
        self.price_spin.setRange(0, 999999)
        self.price_spin.setSuffix(" ج.م")
        self.price_spin.setStyleSheet(self.get_input_style())
        add_product_layout.addWidget(self.price_spin)
        
        add_btn = QPushButton("➕ إضافة")
        add_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        add_btn.clicked.connect(self.add_product_to_order)
        add_product_layout.addWidget(add_btn)
        
        products_layout.addLayout(add_product_layout)
        
        # جدول المنتجات المطلوبة
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels(["المنتج", "الكمية", "السعر", "الإجمالي", "حذف"])
        self.items_table.setMaximumHeight(200)
        self.items_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-size: 11px;
            }
            QHeaderView::section {
                background: #17a2b8;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # تحديد عرض الأعمدة
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # الكمية
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # السعر
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # الإجمالي
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # حذف
        
        self.items_table.setColumnWidth(1, 70)   # الكمية
        self.items_table.setColumnWidth(2, 80)   # السعر
        self.items_table.setColumnWidth(3, 80)   # الإجمالي
        self.items_table.setColumnWidth(4, 50)   # حذف
        
        products_layout.addWidget(self.items_table)
        
        layout.addWidget(products_frame)
        
        # الإجمالي
        total_frame = QFrame()
        total_frame.setStyleSheet("""
            QFrame {
                background: #e8f4f8;
                border-radius: 6px;
                padding: 10px;
                border: 2px solid #17a2b8;
            }
        """)
        total_layout = QHBoxLayout(total_frame)
        
        total_label = QLabel("إجمالي الطلب:")
        total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #17a2b8;")
        
        self.total_amount_label = QLabel("0.00 ج.م")
        self.total_amount_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #17a2b8;")
        
        total_layout.addWidget(total_label)
        total_layout.addStretch()
        total_layout.addWidget(self.total_amount_label)
        
        layout.addWidget(total_frame)
        
        # ملاحظات
        notes_frame = QFrame()
        notes_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 10px;
                border: 1px solid #e9ecef;
            }
        """)
        notes_layout = QVBoxLayout(notes_frame)
        
        notes_layout.addWidget(QLabel("ملاحظات:"))
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("ملاحظات إضافية للطلب...")
        self.notes_input.setMaximumHeight(60)
        self.notes_input.setStyleSheet(self.get_input_style())
        notes_layout.addWidget(self.notes_input)
        
        layout.addWidget(notes_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("💾 حفظ الطلب")
        save_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        save_btn.clicked.connect(self.save_order)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #545b62;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        suppliers = ["شركة التقنية المتقدمة", "مؤسسة الأجهزة الذكية", "شركة المنسوجات الحديثة", "دار الكتب العلمية"]
        self.supplier_combo.addItems(suppliers)
    
    def load_products(self):
        """تحميل قائمة المنتجات"""
        products = [
            {"name": "لابتوب ديل", "price": 12000},
            {"name": "هاتف ذكي", "price": 6500},
            {"name": "قميص قطني", "price": 80},
            {"name": "كتاب برمجة", "price": 150},
            {"name": "مكواة كهربائية", "price": 200}
        ]
        
        for product in products:
            self.product_combo.addItem(product["name"], product)
    
    def add_product_to_order(self):
        """إضافة منتج إلى الطلب"""
        if self.product_combo.currentIndex() == -1:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج!")
            return
        
        product_data = self.product_combo.currentData()
        quantity = self.quantity_spin.value()
        price = self.price_spin.value()
        
        if price <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر صحيح!")
            return
        
        # البحث عن المنتج في الطلب
        for item in self.order_items:
            if item["name"] == product_data["name"]:
                QMessageBox.warning(self, "تحذير", "هذا المنتج موجود بالفعل في الطلب!")
                return
        
        # إضافة المنتج للطلب
        order_item = {
            "name": product_data["name"],
            "quantity": quantity,
            "price": price,
            "total": quantity * price
        }
        
        self.order_items.append(order_item)
        self.update_items_table()
        
        # إعادة تعيين القيم
        self.quantity_spin.setValue(1)
        self.price_spin.setValue(product_data["price"])
    
    def update_items_table(self):
        """تحديث جدول المنتجات"""
        self.items_table.setRowCount(len(self.order_items))
        self.total_amount = 0.0
        
        for row, item in enumerate(self.order_items):
            # اسم المنتج
            self.items_table.setItem(row, 0, QTableWidgetItem(item["name"]))
            
            # الكمية
            quantity_item = QTableWidgetItem(f"{item['quantity']} قطعة")
            quantity_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 1, quantity_item)
            
            # السعر
            price_item = QTableWidgetItem(f"{item['price']:.2f}")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 2, price_item)
            
            # الإجمالي
            total_item = QTableWidgetItem(f"{item['total']:.2f}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 3, total_item)
            
            # زر الحذف
            delete_btn = QPushButton("🗑️")
            delete_btn.setFixedSize(30, 25)
            delete_btn.setStyleSheet("""
                QPushButton {
                    background: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 12px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background: #c82333;
                }
            """)
            delete_btn.clicked.connect(lambda checked, r=row: self.remove_item(r))
            self.items_table.setCellWidget(row, 4, delete_btn)
            
            self.total_amount += item["total"]
        
        self.total_amount_label.setText(f"{self.total_amount:.2f} ج.م")
    
    def remove_item(self, row):
        """حذف منتج من الطلب"""
        if row < len(self.order_items):
            del self.order_items[row]
            self.update_items_table()
    
    def save_order(self):
        """حفظ طلب الشراء"""
        if not self.order_items:
            QMessageBox.warning(self, "تحذير", "يرجى إضافة منتجات للطلب!")
            return
        
        if self.supplier_combo.currentIndex() == -1:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد!")
            return
        
        order_data = {
            'order_number': self.order_number.text(),
            'order_date': self.order_date.date().toString(),
            'supplier': self.supplier_combo.currentText(),
            'delivery_date': self.delivery_date.date().toString(),
            'items': self.order_items.copy(),
            'total_amount': self.total_amount,
            'notes': self.notes_input.toPlainText().strip()
        }
        
        QMessageBox.information(self, "تم الحفظ", f"تم حفظ طلب الشراء رقم {order_data['order_number']} بنجاح!")
        self.accept()
    
    def get_input_style(self):
        return """
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit, QTextEdit {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 12px;
                background: white;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus, QTextEdit:focus {
                border-color: #17a2b8;
            }
        """


class PurchasesAnalyticsWidget(QWidget):
    """واجهة تحليلات المشتريات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة التحليلات"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان التحليلات
        title = QLabel("📊 تحليلات المشتريات")
        title.setStyleSheet("""
            color: #17a2b8;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # الإحصائيات الرئيسية
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 15px;
            }
        """)
        stats_layout = QGridLayout(stats_frame)
        
        # إجمالي المشتريات
        total_purchases = self.create_stat_card("💰", "1,234,567", "إجمالي المشتريات", "#17a2b8")
        stats_layout.addWidget(total_purchases, 0, 0)
        
        # عدد الطلبات
        total_orders = self.create_stat_card("📋", "156", "طلبات الشراء", "#28a745")
        stats_layout.addWidget(total_orders, 0, 1)
        
        # متوسط قيمة الطلب
        avg_order = self.create_stat_card("📊", "7,916", "متوسط الطلب", "#ffc107")
        stats_layout.addWidget(avg_order, 1, 0)
        
        # عدد الموردين
        suppliers_count = self.create_stat_card("🏢", "23", "الموردين النشطين", "#6f42c1")
        stats_layout.addWidget(suppliers_count, 1, 1)
        
        layout.addWidget(stats_frame)
        
        # أفضل الموردين
        top_suppliers_frame = QFrame()
        top_suppliers_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 15px;
            }
        """)
        suppliers_layout = QVBoxLayout(top_suppliers_frame)
        
        suppliers_title = QLabel("🏆 أفضل الموردين")
        suppliers_title.setStyleSheet("""
            color: #495057;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        suppliers_layout.addWidget(suppliers_title)
        
        # قائمة أفضل الموردين
        suppliers_data = [
            {"name": "شركة التقنية المتقدمة", "amount": 450000, "orders": 45},
            {"name": "مؤسسة الأجهزة الذكية", "amount": 380000, "orders": 32},
            {"name": "شركة المنسوجات الحديثة", "amount": 280000, "orders": 28},
            {"name": "دار الكتب العلمية", "amount": 124567, "orders": 51}
        ]
        
        for i, supplier in enumerate(suppliers_data):
            supplier_widget = self.create_supplier_item(i+1, supplier)
            suppliers_layout.addWidget(supplier_widget)
        
        layout.addWidget(top_suppliers_frame)
        
        # الرسم البياني (محاكاة)
        chart_frame = QFrame()
        chart_frame.setFixedHeight(150)
        chart_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 2px dashed #dee2e6;
            }
        """)
        
        chart_layout = QVBoxLayout(chart_frame)
        chart_label = QLabel("📈 رسم بياني للمشتريات الشهرية\n(سيتم إضافته قريباً)")
        chart_label.setAlignment(Qt.AlignCenter)
        chart_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        chart_layout.addWidget(chart_label)
        
        layout.addWidget(chart_frame)
        layout.addStretch()
    
    def create_stat_card(self, icon, value, label, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: white;
                border-left: 4px solid {color};
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }}
            QFrame:hover {{
                background: #f8f9fa;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setSpacing(8)
        
        # الأيقونة والقيمة
        top_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"font-size: 24px; color: {color};")
        
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            color: {color};
            font-size: 18px;
            font-weight: bold;
        """)
        value_label.setAlignment(Qt.AlignRight)
        
        top_layout.addWidget(icon_label)
        top_layout.addStretch()
        top_layout.addWidget(value_label)
        
        # التسمية
        label_widget = QLabel(label)
        label_widget.setStyleSheet("""
            color: #6c757d;
            font-size: 11px;
            font-weight: bold;
        """)
        
        layout.addLayout(top_layout)
        layout.addWidget(label_widget)
        
        return card
    
    def create_supplier_item(self, rank, supplier):
        """إنشاء عنصر مورد"""
        item_frame = QFrame()
        item_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 4px;
                padding: 10px;
                margin: 2px 0;
                border: 1px solid #e9ecef;
            }
            QFrame:hover {
                background: #e9ecef;
            }
        """)
        
        layout = QHBoxLayout(item_frame)
        
        # الترتيب
        rank_label = QLabel(f"#{rank}")
        rank_label.setFixedSize(25, 25)
        rank_label.setStyleSheet("""
            background: #17a2b8;
            color: white;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        """)
        rank_label.setAlignment(Qt.AlignCenter)
        
        # معلومات المورد
        info_layout = QVBoxLayout()
        info_layout.setSpacing(2)
        
        name_label = QLabel(supplier["name"])
        name_label.setStyleSheet("font-size: 12px; font-weight: bold; color: #495057;")
        
        details_label = QLabel(f"{supplier['amount']:,.0f} ج.م • {supplier['orders']} طلب")
        details_label.setStyleSheet("font-size: 10px; color: #6c757d;")
        
        info_layout.addWidget(name_label)
        info_layout.addWidget(details_label)
        
        layout.addWidget(rank_label)
        layout.addLayout(info_layout)
        layout.addStretch()
        
        return item_frame


class PurchasesTableWidget(QTableWidget):
    """جدول المشتريات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_table()
        self.load_purchases_data()
    
    def init_table(self):
        """إعداد الجدول"""
        columns = [
            "رقم الطلب", "التاريخ", "المورد", "عدد المنتجات", 
            "الإجمالي", "تاريخ التسليم", "الحالة", "الإجراءات"
        ]
        
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 12px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #495057;
                padding: 12px 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        # إعدادات الجدول
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        self.verticalHeader().setVisible(False)
        
        # تحديد عرض الأعمدة
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)   # رقم الطلب
        header.setSectionResizeMode(1, QHeaderView.Fixed)   # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Stretch) # المورد
        header.setSectionResizeMode(3, QHeaderView.Fixed)   # عدد المنتجات
        header.setSectionResizeMode(4, QHeaderView.Fixed)   # الإجمالي
        header.setSectionResizeMode(5, QHeaderView.Fixed)   # تاريخ التسليم
        header.setSectionResizeMode(6, QHeaderView.Fixed)   # الحالة
        header.setSectionResizeMode(7, QHeaderView.Fixed)   # الإجراءات
        
        self.setColumnWidth(0, 100)  # رقم الطلب
        self.setColumnWidth(1, 100)  # التاريخ
        self.setColumnWidth(3, 100)  # عدد المنتجات
        self.setColumnWidth(4, 100)  # الإجمالي
        self.setColumnWidth(5, 100)  # تاريخ التسليم
        self.setColumnWidth(6, 80)   # الحالة
        self.setColumnWidth(7, 120)  # الإجراءات
    
    def load_purchases_data(self):
        """تحميل بيانات المشتريات"""
        purchases_data = [
            {"order": "PO-001", "date": "2024-01-15", "supplier": "شركة التقنية المتقدمة", 
             "items": 25, "total": 375000, "delivery": "2024-01-22", "status": "مؤكد"},
            {"order": "PO-002", "date": "2024-01-14", "supplier": "مؤسسة الأجهزة الذكية", 
             "items": 15, "total": 120000, "delivery": "2024-01-21", "status": "قيد التسليم"},
            {"order": "PO-003", "date": "2024-01-13", "supplier": "شركة المنسوجات الحديثة", 
             "items": 200, "total": 24000, "delivery": "2024-01-20", "status": "مكتمل"},
            {"order": "PO-004", "date": "2024-01-12", "supplier": "دار الكتب العلمية", 
             "items": 50, "total": 12500, "delivery": "2024-01-19", "status": "مؤكد"},
            {"order": "PO-005", "date": "2024-01-11", "supplier": "شركة الأدوات المنزلية", 
             "items": 30, "total": 9000, "delivery": "2024-01-18", "status": "ملغي"},
        ]
        
        self.setRowCount(len(purchases_data))
        
        for row, purchase in enumerate(purchases_data):
            # رقم الطلب
            order_item = QTableWidgetItem(purchase["order"])
            order_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 0, order_item)
            
            # التاريخ
            date_item = QTableWidgetItem(purchase["date"])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 1, date_item)
            
            # المورد
            self.setItem(row, 2, QTableWidgetItem(purchase["supplier"]))
            
            # عدد المنتجات
            items_item = QTableWidgetItem(f"{purchase['items']} منتج")
            items_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 3, items_item)
            
            # الإجمالي
            total_item = QTableWidgetItem(f"{purchase['total']:,.0f} ج.م")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 4, total_item)
            
            # تاريخ التسليم
            delivery_item = QTableWidgetItem(purchase["delivery"])
            delivery_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 5, delivery_item)
            
            # الحالة
            status_colors = {
                "مؤكد": {"bg": "#fff3cd", "fg": "#856404"},
                "قيد التسليم": {"bg": "#cce5ff", "fg": "#004085"},
                "مكتمل": {"bg": "#d4edda", "fg": "#155724"},
                "ملغي": {"bg": "#f8d7da", "fg": "#721c24"}
            }
            
            status_item = QTableWidgetItem(purchase["status"])
            status_item.setTextAlignment(Qt.AlignCenter)
            color = status_colors.get(purchase["status"], status_colors["مؤكد"])
            status_item.setBackground(QColor(color["bg"]))
            status_item.setForeground(QColor(color["fg"]))
            self.setItem(row, 6, status_item)
            
            # الإجراءات
            actions_widget = self.create_actions_widget(row, purchase)
            self.setCellWidget(row, 7, actions_widget)
    
    def create_actions_widget(self, row, purchase_data):
        """إنشاء أزرار الإجراءات"""
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(5, 2, 5, 2)
        actions_layout.setSpacing(3)
        
        # زر عرض التفاصيل
        view_btn = QPushButton("👁️")
        view_btn.setFixedSize(22, 22)
        view_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        view_btn.setToolTip("عرض التفاصيل")
        view_btn.clicked.connect(lambda: self.view_purchase_details(row, purchase_data))
        
        # زر تحديث الحالة
        update_btn = QPushButton("🔄")
        update_btn.setFixedSize(22, 22)
        update_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        update_btn.setToolTip("تحديث الحالة")
        update_btn.clicked.connect(lambda: self.update_status(row, purchase_data))
        
        # زر طباعة
        print_btn = QPushButton("🖨️")
        print_btn.setFixedSize(22, 22)
        print_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        print_btn.setToolTip("طباعة الطلب")
        print_btn.clicked.connect(lambda: self.print_order(row, purchase_data))
        
        actions_layout.addWidget(view_btn)
        actions_layout.addWidget(update_btn)
        actions_layout.addWidget(print_btn)
        actions_layout.addStretch()
        
        return actions_widget
    
    def view_purchase_details(self, row, purchase_data):
        """عرض تفاصيل طلب الشراء"""
        details_text = f"""
        📋 تفاصيل طلب الشراء:
        
        🔢 رقم الطلب: {purchase_data['order']}
        📅 تاريخ الطلب: {purchase_data['date']}
        🏢 المورد: {purchase_data['supplier']}
        📦 عدد المنتجات: {purchase_data['items']} منتج
        💰 الإجمالي: {purchase_data['total']:,.0f} ج.م
        🚚 تاريخ التسليم: {purchase_data['delivery']}
        ✅ الحالة: {purchase_data['status']}
        """
        
        QMessageBox.information(self, "تفاصيل طلب الشراء", details_text)
    
    def update_status(self, row, purchase_data):
        """تحديث حالة الطلب"""
        statuses = ["مؤكد", "قيد التسليم", "مكتمل", "ملغي"]
        current_status = purchase_data['status']
        
        # نافذة اختيار الحالة الجديدة
        from PySide6.QtWidgets import QInputDialog
        new_status, ok = QInputDialog.getItem(
            self, "تحديث الحالة", "اختر الحالة الجديدة:",
            statuses, statuses.index(current_status), False
        )
        
        if ok and new_status != current_status:
            QMessageBox.information(self, "تحديث الحالة", 
                f"تم تحديث حالة الطلب {purchase_data['order']} إلى: {new_status}")
            self.load_purchases_data()
    
    def print_order(self, row, purchase_data):
        """طباعة طلب الشراء"""
        QMessageBox.information(self, "طباعة", f"تم إرسال طلب الشراء {purchase_data['order']} للطباعة!")


class EnhancedPurchasesWindow(QWidget):
    """نافذة المشتريات المحسنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # المحتوى الرئيسي
        content_splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - التحليلات
        left_panel = self.create_left_panel()
        left_panel.setMaximumWidth(350)
        content_splitter.addWidget(left_panel)
        
        # الجانب الأيمن - إدارة المشتريات
        right_panel = self.create_right_panel()
        content_splitter.addWidget(right_panel)
        
        content_splitter.setSizes([350, 850])
        main_layout.addWidget(content_splitter)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
    
    def create_header(self):
        """إنشاء الرأس"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #17a2b8, stop:0.5 #20c997, stop:1 #17a2b8);
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # معلومات الصفحة
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        title = QLabel("🏭 إدارة المشتريات")
        title.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
        """)
        
        subtitle = QLabel("إدارة طلبات الشراء والموردين مع تحليلات شاملة ومتابعة دقيقة")
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-top: 5px;
        """)
        
        info_layout.addWidget(title)
        info_layout.addWidget(subtitle)
        
        # الإحصائيات السريعة
        stats_widget = self.create_purchases_stats()
        
        layout.addWidget(info_widget, 7)
        layout.addWidget(stats_widget, 3)
        
        return header_frame
    
    def create_purchases_stats(self):
        """إنشاء إحصائيات المشتريات"""
        stats_widget = QWidget()
        stats_layout = QHBoxLayout(stats_widget)
        stats_layout.setSpacing(10)
        
        # طلبات الشهر
        month_orders = self.create_stat_card("📋", "156", "طلبات الشهر")
        stats_layout.addWidget(month_orders)
        
        # القيمة الإجمالية
        total_value = self.create_stat_card("💰", "1.2M", "القيمة الإجمالية")
        stats_layout.addWidget(total_value)
        
        # طلبات معلقة
        pending_orders = self.create_stat_card("⏳", "23", "طلبات معلقة")
        stats_layout.addWidget(pending_orders)
        
        return stats_widget
    
    def create_stat_card(self, icon, value, label):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("color: white; font-size: 20px;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 10px;")
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(label_widget)
        
        return card
    
    def create_left_panel(self):
        """إنشاء اللوحة اليسرى"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(15)
        
        # التحليلات
        self.analytics_widget = PurchasesAnalyticsWidget()
        left_layout.addWidget(self.analytics_widget)
        
        return left_widget
    
    def create_right_panel(self):
        """إنشاء اللوحة اليمنى"""
        right_frame = QFrame()
        right_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        
        layout = QVBoxLayout(right_frame)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # الجدول
        self.purchases_table = PurchasesTableWidget()
        layout.addWidget(self.purchases_table)
        
        return right_frame
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        
        # زر طلب شراء جديد
        new_order_btn = QPushButton("➕ طلب شراء جديد")
        new_order_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        new_order_btn.clicked.connect(self.create_new_order)
        
        # زر استيراد
        import_btn = QPushButton("📥 استيراد")
        import_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        import_btn.clicked.connect(self.import_orders)
        
        # زر تصدير
        export_btn = QPushButton("📤 تصدير")
        export_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        export_btn.clicked.connect(self.export_orders)
        
        # زر التقارير
        reports_btn = QPushButton("📊 التقارير")
        reports_btn.setStyleSheet("""
            QPushButton {
                background: #6f42c1;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #5a32a3;
            }
        """)
        reports_btn.clicked.connect(self.generate_reports)
        
        layout.addWidget(new_order_btn)
        layout.addWidget(import_btn)
        layout.addWidget(export_btn)
        layout.addWidget(reports_btn)
        layout.addStretch()
        
        # فلتر سريع
        filter_combo = QComboBox()
        filter_combo.addItems(["جميع الطلبات", "مؤكد", "قيد التسليم", "مكتمل", "ملغي"])
        filter_combo.setStyleSheet("""
            QComboBox {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
                font-size: 12px;
            }
        """)
        layout.addWidget(QLabel("فلتر:"))
        layout.addWidget(filter_combo)
        
        return toolbar_frame
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFixedHeight(40)
        status_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 8px 15px;
            }
        """)
        
        layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز - نظام المشتريات متاح")
        self.status_label.setStyleSheet("color: #17a2b8; font-weight: bold; font-size: 12px;")
        
        self.orders_count_label = QLabel("5 طلبات")
        self.orders_count_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        
        self.total_value_label = QLabel("إجمالي القيمة: 540,500 ج.م")
        self.total_value_label.setStyleSheet("color: #007bff; font-weight: bold; font-size: 12px;")
        
        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.orders_count_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.total_value_label)
        
        return status_frame
    
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_data)
        self.update_timer.start(60000)  # تحديث كل دقيقة
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.status_label.setText(f"تم التحديث - {datetime.now().strftime('%H:%M')}")
    
    def create_new_order(self):
        """إنشاء طلب شراء جديد"""
        dialog = PurchaseOrderDialog(parent=self)
        if dialog.exec() == QDialog.Accepted:
            self.purchases_table.load_purchases_data()
            self.status_label.setText("تم إنشاء طلب شراء جديد")
    
    def import_orders(self):
        """استيراد طلبات الشراء"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "استيراد طلبات الشراء", "",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;All Files (*)"
        )
        
        if file_path:
            QMessageBox.information(self, "استيراد", f"تم استيراد طلبات الشراء من:\n{file_path}")
    
    def export_orders(self):
        """تصدير طلبات الشراء"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير طلبات الشراء", "purchase_orders.xlsx",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;PDF Files (*.pdf)"
        )
        
        if file_path:
            QMessageBox.information(self, "تصدير", f"تم تصدير طلبات الشراء إلى:\n{file_path}")
    
    def generate_reports(self):
        """إنشاء التقارير"""
        QMessageBox.information(self, "التقارير", "تم إنشاء تقارير المشتريات بنجاح!")"""
صفحة المشتريات المحسنة مع إدارة طلبات الشراء والموردين
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QTableWidget, QTableWidgetItem,
                              QHeaderView, QComboBox, QLineEdit, QSpinBox,
                              QDoubleSpinBox, QTextEdit, QGroupBox, QGridLayout,
                              QScrollArea, QSplitter, QTabWidget, QFormLayout,
                              QMessageBox, QDialog, QDialogButtonBox, QCheckBox,
                              QProgressBar, QFileDialog, QMenu, QApplication,
                              QCalendarWidget, QDateEdit, QListWidget, QListWidgetItem,
                              QTreeWidget, QTreeWidgetItem)
from PySide6.QtCore import Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QDate
from PySide6.QtGui import QFont, QColor, QPainter, QPen, QBrush, QPixmap, QAction, QCursor
import json
import random
from datetime import datetime, timedelta

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class PurchaseOrderDialog(QDialog):
    """نافذة إنشاء طلب شراء جديد"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.order_items = []
        self.total_amount = 0.0
        self.init_ui()
        self.load_suppliers()
        self.load_products()
    
    def init_ui(self):
        """إعداد واجهة النافذة"""
        self.setWindowTitle("إنشاء طلب شراء جديد")
        self.setFixedSize(700, 600)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان النافذة
        title = QLabel("📋 إنشاء طلب شراء جديد")
        title.setStyleSheet("""
            color: #17a2b8;
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            background: #e8f4f8;
            border-radius: 8px;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # معلومات الطلب الأساسية
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        info_layout = QGridLayout(info_frame)
        
        # رقم الطلب
        info_layout.addWidget(QLabel("رقم الطلب:"), 0, 0)
        self.order_number = QLineEdit(f"PO-{datetime.now().strftime('%Y%m%d%H%M')}")
        self.order_number.setReadOnly(True)
        self.order_number.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.order_number, 0, 1)
        
        # تاريخ الطلب
        info_layout.addWidget(QLabel("تاريخ الطلب:"), 0, 2)
        self.order_date = QDateEdit()
        self.order_date.setDate(QDate.currentDate())
        self.order_date.setCalendarPopup(True)
        self.order_date.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.order_date, 0, 3)
        
        # المورد
        info_layout.addWidget(QLabel("المورد:"), 1, 0)
        self.supplier_combo = QComboBox()
        self.supplier_combo.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.supplier_combo, 1, 1)
        
        # تاريخ التسليم المتوقع
        info_layout.addWidget(QLabel("تاريخ التسليم:"), 1, 2)
        self.delivery_date = QDateEdit()
        self.delivery_date.setDate(QDate.currentDate().addDays(7))
        self.delivery_date.setCalendarPopup(True)
        self.delivery_date.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.delivery_date, 1, 3)
        
        layout.addWidget(info_frame)
        
        # إضافة المنتجات
        products_frame = QFrame()
        products_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        products_layout = QVBoxLayout(products_frame)
        
        # إضافة منتج جديد
        add_product_layout = QHBoxLayout()
        
        add_product_layout.addWidget(QLabel("المنتج:"))
        self.product_combo = QComboBox()
        self.product_combo.setStyleSheet(self.get_input_style())
        add_product_layout.addWidget(self.product_combo)
        
        add_product_layout.addWidget(QLabel("الكمية:"))
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(1, 9999)
        self.quantity_spin.setStyleSheet(self.get_input_style())
        add_product_layout.addWidget(self.quantity_spin)
        
        add_product_layout.addWidget(QLabel("السعر:"))
        self.price_spin = QDoubleSpinBox()
        self.price_spin.setRange(0, 999999)
        self.price_spin.setSuffix(" ج.م")
        self.price_spin.setStyleSheet(self.get_input_style())
        add_product_layout.addWidget(self.price_spin)
        
        add_btn = QPushButton("➕ إضافة")
        add_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        add_btn.clicked.connect(self.add_product_to_order)
        add_product_layout.addWidget(add_btn)
        
        products_layout.addLayout(add_product_layout)
        
        # جدول المنتجات المطلوبة
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels(["المنتج", "الكمية", "السعر", "الإجمالي", "حذف"])
        self.items_table.setMaximumHeight(200)
        self.items_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-size: 11px;
            }
            QHeaderView::section {
                background: #17a2b8;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # تحديد عرض الأعمدة
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # الكمية
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # السعر
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # الإجمالي
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # حذف
        
        self.items_table.setColumnWidth(1, 70)   # الكمية
        self.items_table.setColumnWidth(2, 80)   # السعر
        self.items_table.setColumnWidth(3, 80)   # الإجمالي
        self.items_table.setColumnWidth(4, 50)   # حذف
        
        products_layout.addWidget(self.items_table)
        
        layout.addWidget(products_frame)
        
        # الإجمالي
        total_frame = QFrame()
        total_frame.setStyleSheet("""
            QFrame {
                background: #e8f4f8;
                border-radius: 6px;
                padding: 10px;
                border: 2px solid #17a2b8;
            }
        """)
        total_layout = QHBoxLayout(total_frame)
        
        total_label = QLabel("إجمالي الطلب:")
        total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #17a2b8;")
        
        self.total_amount_label = QLabel("0.00 ج.م")
        self.total_amount_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #17a2b8;")
        
        total_layout.addWidget(total_label)
        total_layout.addStretch()
        total_layout.addWidget(self.total_amount_label)
        
        layout.addWidget(total_frame)
        
        # ملاحظات
        notes_frame = QFrame()
        notes_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 10px;
                border: 1px solid #e9ecef;
            }
        """)
        notes_layout = QVBoxLayout(notes_frame)
        
        notes_layout.addWidget(QLabel("ملاحظات:"))
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("ملاحظات إضافية للطلب...")
        self.notes_input.setMaximumHeight(60)
        self.notes_input.setStyleSheet(self.get_input_style())
        notes_layout.addWidget(self.notes_input)
        
        layout.addWidget(notes_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("💾 حفظ الطلب")
        save_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        save_btn.clicked.connect(self.save_order)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #545b62;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        suppliers = ["شركة التقنية المتقدمة", "مؤسسة الأجهزة الذكية", "شركة المنسوجات الحديثة", "دار الكتب العلمية"]
        self.supplier_combo.addItems(suppliers)
    
    def load_products(self):
        """تحميل قائمة المنتجات"""
        products = [
            {"name": "لابتوب ديل", "price": 12000},
            {"name": "هاتف ذكي", "price": 6500},
            {"name": "قميص قطني", "price": 80},
            {"name": "كتاب برمجة", "price": 150},
            {"name": "مكواة كهربائية", "price": 200}
        ]
        
        for product in products:
            self.product_combo.addItem(product["name"], product)
    
    def add_product_to_order(self):
        """إضافة منتج إلى الطلب"""
        if self.product_combo.currentIndex() == -1:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج!")
            return
        
        product_data = self.product_combo.currentData()
        quantity = self.quantity_spin.value()
        price = self.price_spin.value()
        
        if price <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر صحيح!")
            return
        
        # البحث عن المنتج في الطلب
        for item in self.order_items:
            if item["name"] == product_data["name"]:
                QMessageBox.warning(self, "تحذير", "هذا المنتج موجود بالفعل في الطلب!")
                return
        
        # إضافة المنتج للطلب
        order_item = {
            "name": product_data["name"],
            "quantity": quantity,
            "price": price,
            "total": quantity * price
        }
        
        self.order_items.append(order_item)
        self.update_items_table()
        
        # إعادة تعيين القيم
        self.quantity_spin.setValue(1)
        self.price_spin.setValue(product_data["price"])
    
    def update_items_table(self):
        """تحديث جدول المنتجات"""
        self.items_table.setRowCount(len(self.order_items))
        self.total_amount = 0.0
        
        for row, item in enumerate(self.order_items):
            # اسم المنتج
            self.items_table.setItem(row, 0, QTableWidgetItem(item["name"]))
            
            # الكمية
            quantity_item = QTableWidgetItem(f"{item['quantity']} قطعة")
            quantity_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 1, quantity_item)
            
            # السعر
            price_item = QTableWidgetItem(f"{item['price']:.2f}")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 2, price_item)
            
            # الإجمالي
            total_item = QTableWidgetItem(f"{item['total']:.2f}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 3, total_item)
            
            # زر الحذف
            delete_btn = QPushButton("🗑️")
            delete_btn.setFixedSize(30, 25)
            delete_btn.setStyleSheet("""
                QPushButton {
                    background: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 12px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background: #c82333;
                }
            """)
            delete_btn.clicked.connect(lambda checked, r=row: self.remove_item(r))
            self.items_table.setCellWidget(row, 4, delete_btn)
            
            self.total_amount += item["total"]
        
        self.total_amount_label.setText(f"{self.total_amount:.2f} ج.م")
    
    def remove_item(self, row):
        """حذف منتج من الطلب"""
        if row < len(self.order_items):
            del self.order_items[row]
            self.update_items_table()
    
    def save_order(self):
        """حفظ طلب الشراء"""
        if not self.order_items:
            QMessageBox.warning(self, "تحذير", "يرجى إضافة منتجات للطلب!")
            return
        
        if self.supplier_combo.currentIndex() == -1:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد!")
            return
        
        order_data = {
            'order_number': self.order_number.text(),
            'order_date': self.order_date.date().toString(),
            'supplier': self.supplier_combo.currentText(),
            'delivery_date': self.delivery_date.date().toString(),
            'items': self.order_items.copy(),
            'total_amount': self.total_amount,
            'notes': self.notes_input.toPlainText().strip()
        }
        
        QMessageBox.information(self, "تم الحفظ", f"تم حفظ طلب الشراء رقم {order_data['order_number']} بنجاح!")
        self.accept()
    
    def get_input_style(self):
        return """
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit, QTextEdit {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 12px;
                background: white;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus, QTextEdit:focus {
                border-color: #17a2b8;
            }
        """


class PurchasesAnalyticsWidget(QWidget):
    """واجهة تحليلات المشتريات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة التحليلات"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان التحليلات
        title = QLabel("📊 تحليلات المشتريات")
        title.setStyleSheet("""
            color: #17a2b8;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # الإحصائيات الرئيسية
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 15px;
            }
        """)
        stats_layout = QGridLayout(stats_frame)
        
        # إجمالي المشتريات
        total_purchases = self.create_stat_card("💰", "1,234,567", "إجمالي المشتريات", "#17a2b8")
        stats_layout.addWidget(total_purchases, 0, 0)
        
        # عدد الطلبات
        total_orders = self.create_stat_card("📋", "156", "طلبات الشراء", "#28a745")
        stats_layout.addWidget(total_orders, 0, 1)
        
        # متوسط قيمة الطلب
        avg_order = self.create_stat_card("📊", "7,916", "متوسط الطلب", "#ffc107")
        stats_layout.addWidget(avg_order, 1, 0)
        
        # عدد الموردين
        suppliers_count = self.create_stat_card("🏢", "23", "الموردين النشطين", "#6f42c1")
        stats_layout.addWidget(suppliers_count, 1, 1)
        
        layout.addWidget(stats_frame)
        
        # أفضل الموردين
        top_suppliers_frame = QFrame()
        top_suppliers_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 15px;
            }
        """)
        suppliers_layout = QVBoxLayout(top_suppliers_frame)
        
        suppliers_title = QLabel("🏆 أفضل الموردين")
        suppliers_title.setStyleSheet("""
            color: #495057;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        suppliers_layout.addWidget(suppliers_title)
        
        # قائمة أفضل الموردين
        suppliers_data = [
            {"name": "شركة التقنية المتقدمة", "amount": 450000, "orders": 45},
            {"name": "مؤسسة الأجهزة الذكية", "amount": 380000, "orders": 32},
            {"name": "شركة المنسوجات الحديثة", "amount": 280000, "orders": 28},
            {"name": "دار الكتب العلمية", "amount": 124567, "orders": 51}
        ]
        
        for i, supplier in enumerate(suppliers_data):
            supplier_widget = self.create_supplier_item(i+1, supplier)
            suppliers_layout.addWidget(supplier_widget)
        
        layout.addWidget(top_suppliers_frame)
        
        # الرسم البياني (محاكاة)
        chart_frame = QFrame()
        chart_frame.setFixedHeight(150)
        chart_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 2px dashed #dee2e6;
            }
        """)
        
        chart_layout = QVBoxLayout(chart_frame)
        chart_label = QLabel("📈 رسم بياني للمشتريات الشهرية\n(سيتم إضافته قريباً)")
        chart_label.setAlignment(Qt.AlignCenter)
        chart_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        chart_layout.addWidget(chart_label)
        
        layout.addWidget(chart_frame)
        layout.addStretch()
    
    def create_stat_card(self, icon, value, label, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: white;
                border-left: 4px solid {color};
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }}
            QFrame:hover {{
                background: #f8f9fa;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setSpacing(8)
        
        # الأيقونة والقيمة
        top_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"font-size: 24px; color: {color};")
        
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            color: {color};
            font-size: 18px;
            font-weight: bold;
        """)
        value_label.setAlignment(Qt.AlignRight)
        
        top_layout.addWidget(icon_label)
        top_layout.addStretch()
        top_layout.addWidget(value_label)
        
        # التسمية
        label_widget = QLabel(label)
        label_widget.setStyleSheet("""
            color: #6c757d;
            font-size: 11px;
            font-weight: bold;
        """)
        
        layout.addLayout(top_layout)
        layout.addWidget(label_widget)
        
        return card
    
    def create_supplier_item(self, rank, supplier):
        """إنشاء عنصر مورد"""
        item_frame = QFrame()
        item_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 4px;
                padding: 10px;
                margin: 2px 0;
                border: 1px solid #e9ecef;
            }
            QFrame:hover {
                background: #e9ecef;
            }
        """)
        
        layout = QHBoxLayout(item_frame)
        
        # الترتيب
        rank_label = QLabel(f"#{rank}")
        rank_label.setFixedSize(25, 25)
        rank_label.setStyleSheet("""
            background: #17a2b8;
            color: white;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        """)
        rank_label.setAlignment(Qt.AlignCenter)
        
        # معلومات المورد
        info_layout = QVBoxLayout()
        info_layout.setSpacing(2)
        
        name_label = QLabel(supplier["name"])
        name_label.setStyleSheet("font-size: 12px; font-weight: bold; color: #495057;")
        
        details_label = QLabel(f"{supplier['amount']:,.0f} ج.م • {supplier['orders']} طلب")
        details_label.setStyleSheet("font-size: 10px; color: #6c757d;")
        
        info_layout.addWidget(name_label)
        info_layout.addWidget(details_label)
        
        layout.addWidget(rank_label)
        layout.addLayout(info_layout)
        layout.addStretch()
        
        return item_frame


class PurchasesTableWidget(QTableWidget):
    """جدول المشتريات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_table()
        self.load_purchases_data()
    
    def init_table(self):
        """إعداد الجدول"""
        columns = [
            "رقم الطلب", "التاريخ", "المورد", "عدد المنتجات", 
            "الإجمالي", "تاريخ التسليم", "الحالة", "الإجراءات"
        ]
        
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 12px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #495057;
                padding: 12px 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        # إعدادات الجدول
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        self.verticalHeader().setVisible(False)
        
        # تحديد عرض الأعمدة
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)   # رقم الطلب
        header.setSectionResizeMode(1, QHeaderView.Fixed)   # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Stretch) # المورد
        header.setSectionResizeMode(3, QHeaderView.Fixed)   # عدد المنتجات
        header.setSectionResizeMode(4, QHeaderView.Fixed)   # الإجمالي
        header.setSectionResizeMode(5, QHeaderView.Fixed)   # تاريخ التسليم
        header.setSectionResizeMode(6, QHeaderView.Fixed)   # الحالة
        header.setSectionResizeMode(7, QHeaderView.Fixed)   # الإجراءات
        
        self.setColumnWidth(0, 100)  # رقم الطلب
        self.setColumnWidth(1, 100)  # التاريخ
        self.setColumnWidth(3, 100)  # عدد المنتجات
        self.setColumnWidth(4, 100)  # الإجمالي
        self.setColumnWidth(5, 100)  # تاريخ التسليم
        self.setColumnWidth(6, 80)   # الحالة
        self.setColumnWidth(7, 120)  # الإجراءات
    
    def load_purchases_data(self):
        """تحميل بيانات المشتريات"""
        purchases_data = [
            {"order": "PO-001", "date": "2024-01-15", "supplier": "شركة التقنية المتقدمة", 
             "items": 25, "total": 375000, "delivery": "2024-01-22", "status": "مؤكد"},
            {"order": "PO-002", "date": "2024-01-14", "supplier": "مؤسسة الأجهزة الذكية", 
             "items": 15, "total": 120000, "delivery": "2024-01-21", "status": "قيد التسليم"},
            {"order": "PO-003", "date": "2024-01-13", "supplier": "شركة المنسوجات الحديثة", 
             "items": 200, "total": 24000, "delivery": "2024-01-20", "status": "مكتمل"},
            {"order": "PO-004", "date": "2024-01-12", "supplier": "دار الكتب العلمية", 
             "items": 50, "total": 12500, "delivery": "2024-01-19", "status": "مؤكد"},
            {"order": "PO-005", "date": "2024-01-11", "supplier": "شركة الأدوات المنزلية", 
             "items": 30, "total": 9000, "delivery": "2024-01-18", "status": "ملغي"},
        ]
        
        self.setRowCount(len(purchases_data))
        
        for row, purchase in enumerate(purchases_data):
            # رقم الطلب
            order_item = QTableWidgetItem(purchase["order"])
            order_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 0, order_item)
            
            # التاريخ
            date_item = QTableWidgetItem(purchase["date"])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 1, date_item)
            
            # المورد
            self.setItem(row, 2, QTableWidgetItem(purchase["supplier"]))
            
            # عدد المنتجات
            items_item = QTableWidgetItem(f"{purchase['items']} منتج")
            items_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 3, items_item)
            
            # الإجمالي
            total_item = QTableWidgetItem(f"{purchase['total']:,.0f} ج.م")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 4, total_item)
            
            # تاريخ التسليم
            delivery_item = QTableWidgetItem(purchase["delivery"])
            delivery_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 5, delivery_item)
            
            # الحالة
            status_colors = {
                "مؤكد": {"bg": "#fff3cd", "fg": "#856404"},
                "قيد التسليم": {"bg": "#cce5ff", "fg": "#004085"},
                "مكتمل": {"bg": "#d4edda", "fg": "#155724"},
                "ملغي": {"bg": "#f8d7da", "fg": "#721c24"}
            }
            
            status_item = QTableWidgetItem(purchase["status"])
            status_item.setTextAlignment(Qt.AlignCenter)
            color = status_colors.get(purchase["status"], status_colors["مؤكد"])
            status_item.setBackground(QColor(color["bg"]))
            status_item.setForeground(QColor(color["fg"]))
            self.setItem(row, 6, status_item)
            
            # الإجراءات
            actions_widget = self.create_actions_widget(row, purchase)
            self.setCellWidget(row, 7, actions_widget)
    
    def create_actions_widget(self, row, purchase_data):
        """إنشاء أزرار الإجراءات"""
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(5, 2, 5, 2)
        actions_layout.setSpacing(3)
        
        # زر عرض التفاصيل
        view_btn = QPushButton("👁️")
        view_btn.setFixedSize(22, 22)
        view_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        view_btn.setToolTip("عرض التفاصيل")
        view_btn.clicked.connect(lambda: self.view_purchase_details(row, purchase_data))
        
        # زر تحديث الحالة
        update_btn = QPushButton("🔄")
        update_btn.setFixedSize(22, 22)
        update_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        update_btn.setToolTip("تحديث الحالة")
        update_btn.clicked.connect(lambda: self.update_status(row, purchase_data))
        
        # زر طباعة
        print_btn = QPushButton("🖨️")
        print_btn.setFixedSize(22, 22)
        print_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        print_btn.setToolTip("طباعة الطلب")
        print_btn.clicked.connect(lambda: self.print_order(row, purchase_data))
        
        actions_layout.addWidget(view_btn)
        actions_layout.addWidget(update_btn)
        actions_layout.addWidget(print_btn)
        actions_layout.addStretch()
        
        return actions_widget
    
    def view_purchase_details(self, row, purchase_data):
        """عرض تفاصيل طلب الشراء"""
        details_text = f"""
        📋 تفاصيل طلب الشراء:
        
        🔢 رقم الطلب: {purchase_data['order']}
        📅 تاريخ الطلب: {purchase_data['date']}
        🏢 المورد: {purchase_data['supplier']}
        📦 عدد المنتجات: {purchase_data['items']} منتج
        💰 الإجمالي: {purchase_data['total']:,.0f} ج.م
        🚚 تاريخ التسليم: {purchase_data['delivery']}
        ✅ الحالة: {purchase_data['status']}
        """
        
        QMessageBox.information(self, "تفاصيل طلب الشراء", details_text)
    
    def update_status(self, row, purchase_data):
        """تحديث حالة الطلب"""
        statuses = ["مؤكد", "قيد التسليم", "مكتمل", "ملغي"]
        current_status = purchase_data['status']
        
        # نافذة اختيار الحالة الجديدة
        from PySide6.QtWidgets import QInputDialog
        new_status, ok = QInputDialog.getItem(
            self, "تحديث الحالة", "اختر الحالة الجديدة:",
            statuses, statuses.index(current_status), False
        )
        
        if ok and new_status != current_status:
            QMessageBox.information(self, "تحديث الحالة", 
                f"تم تحديث حالة الطلب {purchase_data['order']} إلى: {new_status}")
            self.load_purchases_data()
    
    def print_order(self, row, purchase_data):
        """طباعة طلب الشراء"""
        QMessageBox.information(self, "طباعة", f"تم إرسال طلب الشراء {purchase_data['order']} للطباعة!")


class EnhancedPurchasesWindow(QWidget):
    """نافذة المشتريات المحسنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # المحتوى الرئيسي
        content_splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - التحليلات
        left_panel = self.create_left_panel()
        left_panel.setMaximumWidth(350)
        content_splitter.addWidget(left_panel)
        
        # الجانب الأيمن - إدارة المشتريات
        right_panel = self.create_right_panel()
        content_splitter.addWidget(right_panel)
        
        content_splitter.setSizes([350, 850])
        main_layout.addWidget(content_splitter)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
    
    def create_header(self):
        """إنشاء الرأس"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #17a2b8, stop:0.5 #20c997, stop:1 #17a2b8);
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # معلومات الصفحة
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        title = QLabel("🏭 إدارة المشتريات")
        title.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
        """)
        
        subtitle = QLabel("إدارة طلبات الشراء والموردين مع تحليلات شاملة ومتابعة دقيقة")
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-top: 5px;
        """)
        
        info_layout.addWidget(title)
        info_layout.addWidget(subtitle)
        
        # الإحصائيات السريعة
        stats_widget = self.create_purchases_stats()
        
        layout.addWidget(info_widget, 7)
        layout.addWidget(stats_widget, 3)
        
        return header_frame
    
    def create_purchases_stats(self):
        """إنشاء إحصائيات المشتريات"""
        stats_widget = QWidget()
        stats_layout = QHBoxLayout(stats_widget)
        stats_layout.setSpacing(10)
        
        # طلبات الشهر
        month_orders = self.create_stat_card("📋", "156", "طلبات الشهر")
        stats_layout.addWidget(month_orders)
        
        # القيمة الإجمالية
        total_value = self.create_stat_card("💰", "1.2M", "القيمة الإجمالية")
        stats_layout.addWidget(total_value)
        
        # طلبات معلقة
        pending_orders = self.create_stat_card("⏳", "23", "طلبات معلقة")
        stats_layout.addWidget(pending_orders)
        
        return stats_widget
    
    def create_stat_card(self, icon, value, label):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("color: white; font-size: 20px;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 10px;")
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(label_widget)
        
        return card
    
    def create_left_panel(self):
        """إنشاء اللوحة اليسرى"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(15)
        
        # التحليلات
        self.analytics_widget = PurchasesAnalyticsWidget()
        left_layout.addWidget(self.analytics_widget)
        
        return left_widget
    
    def create_right_panel(self):
        """إنشاء اللوحة اليمنى"""
        right_frame = QFrame()
        right_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        
        layout = QVBoxLayout(right_frame)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # الجدول
        self.purchases_table = PurchasesTableWidget()
        layout.addWidget(self.purchases_table)
        
        return right_frame
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        
        # زر طلب شراء جديد
        new_order_btn = QPushButton("➕ طلب شراء جديد")
        new_order_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        new_order_btn.clicked.connect(self.create_new_order)
        
        # زر استيراد
        import_btn = QPushButton("📥 استيراد")
        import_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        import_btn.clicked.connect(self.import_orders)
        
        # زر تصدير
        export_btn = QPushButton("📤 تصدير")
        export_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        export_btn.clicked.connect(self.export_orders)
        
        # زر التقارير
        reports_btn = QPushButton("📊 التقارير")
        reports_btn.setStyleSheet("""
            QPushButton {
                background: #6f42c1;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #5a32a3;
            }
        """)
        reports_btn.clicked.connect(self.generate_reports)
        
        layout.addWidget(new_order_btn)
        layout.addWidget(import_btn)
        layout.addWidget(export_btn)
        layout.addWidget(reports_btn)
        layout.addStretch()
        
        # فلتر سريع
        filter_combo = QComboBox()
        filter_combo.addItems(["جميع الطلبات", "مؤكد", "قيد التسليم", "مكتمل", "ملغي"])
        filter_combo.setStyleSheet("""
            QComboBox {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
                font-size: 12px;
            }
        """)
        layout.addWidget(QLabel("فلتر:"))
        layout.addWidget(filter_combo)
        
        return toolbar_frame
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFixedHeight(40)
        status_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 8px 15px;
            }
        """)
        
        layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز - نظام المشتريات متاح")
        self.status_label.setStyleSheet("color: #17a2b8; font-weight: bold; font-size: 12px;")
        
        self.orders_count_label = QLabel("5 طلبات")
        self.orders_count_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        
        self.total_value_label = QLabel("إجمالي القيمة: 540,500 ج.م")
        self.total_value_label.setStyleSheet("color: #007bff; font-weight: bold; font-size: 12px;")
        
        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.orders_count_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.total_value_label)
        
        return status_frame
    
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_data)
        self.update_timer.start(60000)  # تحديث كل دقيقة
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.status_label.setText(f"تم التحديث - {datetime.now().strftime('%H:%M')}")
    
    def create_new_order(self):
        """إنشاء طلب شراء جديد"""
        dialog = PurchaseOrderDialog(parent=self)
        if dialog.exec() == QDialog.Accepted:
            self.purchases_table.load_purchases_data()
            self.status_label.setText("تم إنشاء طلب شراء جديد")
    
    def import_orders(self):
        """استيراد طلبات الشراء"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "استيراد طلبات الشراء", "",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;All Files (*)"
        )
        
        if file_path:
            QMessageBox.information(self, "استيراد", f"تم استيراد طلبات الشراء من:\n{file_path}")
    
    def export_orders(self):
        """تصدير طلبات الشراء"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير طلبات الشراء", "purchase_orders.xlsx",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;PDF Files (*.pdf)"
        )
        
        if file_path:
            QMessageBox.information(self, "تصدير", f"تم تصدير طلبات الشراء إلى:\n{file_path}")
    
    def generate_reports(self):
        """إنشاء التقارير"""
        QMessageBox.information(self, "التقارير", "تم إنشاء تقارير المشتريات بنجاح!")"""
صفحة المشتريات المحسنة مع إدارة طلبات الشراء والموردين
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QTableWidget, QTableWidgetItem,
                              QHeaderView, QComboBox, QLineEdit, QSpinBox,
                              QDoubleSpinBox, QTextEdit, QGroupBox, QGridLayout,
                              QScrollArea, QSplitter, QTabWidget, QFormLayout,
                              QMessageBox, QDialog, QDialogButtonBox, QCheckBox,
                              QProgressBar, QFileDialog, QMenu, QApplication,
                              QCalendarWidget, QDateEdit, QListWidget, QListWidgetItem,
                              QTreeWidget, QTreeWidgetItem)
from PySide6.QtCore import Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QDate
from PySide6.QtGui import QFont, QColor, QPainter, QPen, QBrush, QPixmap, QAction, QCursor
import json
import random
from datetime import datetime, timedelta

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class PurchaseOrderDialog(QDialog):
    """نافذة إنشاء طلب شراء جديد"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.order_items = []
        self.total_amount = 0.0
        self.init_ui()
        self.load_suppliers()
        self.load_products()
    
    def init_ui(self):
        """إعداد واجهة النافذة"""
        self.setWindowTitle("إنشاء طلب شراء جديد")
        self.setFixedSize(700, 600)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان النافذة
        title = QLabel("📋 إنشاء طلب شراء جديد")
        title.setStyleSheet("""
            color: #17a2b8;
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            background: #e8f4f8;
            border-radius: 8px;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # معلومات الطلب الأساسية
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        info_layout = QGridLayout(info_frame)
        
        # رقم الطلب
        info_layout.addWidget(QLabel("رقم الطلب:"), 0, 0)
        self.order_number = QLineEdit(f"PO-{datetime.now().strftime('%Y%m%d%H%M')}")
        self.order_number.setReadOnly(True)
        self.order_number.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.order_number, 0, 1)
        
        # تاريخ الطلب
        info_layout.addWidget(QLabel("تاريخ الطلب:"), 0, 2)
        self.order_date = QDateEdit()
        self.order_date.setDate(QDate.currentDate())
        self.order_date.setCalendarPopup(True)
        self.order_date.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.order_date, 0, 3)
        
        # المورد
        info_layout.addWidget(QLabel("المورد:"), 1, 0)
        self.supplier_combo = QComboBox()
        self.supplier_combo.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.supplier_combo, 1, 1)
        
        # تاريخ التسليم المتوقع
        info_layout.addWidget(QLabel("تاريخ التسليم:"), 1, 2)
        self.delivery_date = QDateEdit()
        self.delivery_date.setDate(QDate.currentDate().addDays(7))
        self.delivery_date.setCalendarPopup(True)
        self.delivery_date.setStyleSheet(self.get_input_style())
        info_layout.addWidget(self.delivery_date, 1, 3)
        
        layout.addWidget(info_frame)
        
        # إضافة المنتجات
        products_frame = QFrame()
        products_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        products_layout = QVBoxLayout(products_frame)
        
        # إضافة منتج جديد
        add_product_layout = QHBoxLayout()
        
        add_product_layout.addWidget(QLabel("المنتج:"))
        self.product_combo = QComboBox()
        self.product_combo.setStyleSheet(self.get_input_style())
        add_product_layout.addWidget(self.product_combo)
        
        add_product_layout.addWidget(QLabel("الكمية:"))
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(1, 9999)
        self.quantity_spin.setStyleSheet(self.get_input_style())
        add_product_layout.addWidget(self.quantity_spin)
        
        add_product_layout.addWidget(QLabel("السعر:"))
        self.price_spin = QDoubleSpinBox()
        self.price_spin.setRange(0, 999999)
        self.price_spin.setSuffix(" ج.م")
        self.price_spin.setStyleSheet(self.get_input_style())
        add_product_layout.addWidget(self.price_spin)
        
        add_btn = QPushButton("➕ إضافة")
        add_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        add_btn.clicked.connect(self.add_product_to_order)
        add_product_layout.addWidget(add_btn)
        
        products_layout.addLayout(add_product_layout)
        
        # جدول المنتجات المطلوبة
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels(["المنتج", "الكمية", "السعر", "الإجمالي", "حذف"])
        self.items_table.setMaximumHeight(200)
        self.items_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-size: 11px;
            }
            QHeaderView::section {
                background: #17a2b8;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # تحديد عرض الأعمدة
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # الكمية
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # السعر
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # الإجمالي
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # حذف
        
        self.items_table.setColumnWidth(1, 70)   # الكمية
        self.items_table.setColumnWidth(2, 80)   # السعر
        self.items_table.setColumnWidth(3, 80)   # الإجمالي
        self.items_table.setColumnWidth(4, 50)   # حذف
        
        products_layout.addWidget(self.items_table)
        
        layout.addWidget(products_frame)
        
        # الإجمالي
        total_frame = QFrame()
        total_frame.setStyleSheet("""
            QFrame {
                background: #e8f4f8;
                border-radius: 6px;
                padding: 10px;
                border: 2px solid #17a2b8;
            }
        """)
        total_layout = QHBoxLayout(total_frame)
        
        total_label = QLabel("إجمالي الطلب:")
        total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #17a2b8;")
        
        self.total_amount_label = QLabel("0.00 ج.م")
        self.total_amount_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #17a2b8;")
        
        total_layout.addWidget(total_label)
        total_layout.addStretch()
        total_layout.addWidget(self.total_amount_label)
        
        layout.addWidget(total_frame)
        
        # ملاحظات
        notes_frame = QFrame()
        notes_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 10px;
                border: 1px solid #e9ecef;
            }
        """)
        notes_layout = QVBoxLayout(notes_frame)
        
        notes_layout.addWidget(QLabel("ملاحظات:"))
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("ملاحظات إضافية للطلب...")
        self.notes_input.setMaximumHeight(60)
        self.notes_input.setStyleSheet(self.get_input_style())
        notes_layout.addWidget(self.notes_input)
        
        layout.addWidget(notes_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("💾 حفظ الطلب")
        save_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        save_btn.clicked.connect(self.save_order)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #545b62;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        suppliers = ["شركة التقنية المتقدمة", "مؤسسة الأجهزة الذكية", "شركة المنسوجات الحديثة", "دار الكتب العلمية"]
        self.supplier_combo.addItems(suppliers)
    
    def load_products(self):
        """تحميل قائمة المنتجات"""
        products = [
            {"name": "لابتوب ديل", "price": 12000},
            {"name": "هاتف ذكي", "price": 6500},
            {"name": "قميص قطني", "price": 80},
            {"name": "كتاب برمجة", "price": 150},
            {"name": "مكواة كهربائية", "price": 200}
        ]
        
        for product in products:
            self.product_combo.addItem(product["name"], product)
    
    def add_product_to_order(self):
        """إضافة منتج إلى الطلب"""
        if self.product_combo.currentIndex() == -1:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج!")
            return
        
        product_data = self.product_combo.currentData()
        quantity = self.quantity_spin.value()
        price = self.price_spin.value()
        
        if price <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر صحيح!")
            return
        
        # البحث عن المنتج في الطلب
        for item in self.order_items:
            if item["name"] == product_data["name"]:
                QMessageBox.warning(self, "تحذير", "هذا المنتج موجود بالفعل في الطلب!")
                return
        
        # إضافة المنتج للطلب
        order_item = {
            "name": product_data["name"],
            "quantity": quantity,
            "price": price,
            "total": quantity * price
        }
        
        self.order_items.append(order_item)
        self.update_items_table()
        
        # إعادة تعيين القيم
        self.quantity_spin.setValue(1)
        self.price_spin.setValue(product_data["price"])
    
    def update_items_table(self):
        """تحديث جدول المنتجات"""
        self.items_table.setRowCount(len(self.order_items))
        self.total_amount = 0.0
        
        for row, item in enumerate(self.order_items):
            # اسم المنتج
            self.items_table.setItem(row, 0, QTableWidgetItem(item["name"]))
            
            # الكمية
            quantity_item = QTableWidgetItem(f"{item['quantity']} قطعة")
            quantity_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 1, quantity_item)
            
            # السعر
            price_item = QTableWidgetItem(f"{item['price']:.2f}")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 2, price_item)
            
            # الإجمالي
            total_item = QTableWidgetItem(f"{item['total']:.2f}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 3, total_item)
            
            # زر الحذف
            delete_btn = QPushButton("🗑️")
            delete_btn.setFixedSize(30, 25)
            delete_btn.setStyleSheet("""
                QPushButton {
                    background: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 12px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background: #c82333;
                }
            """)
            delete_btn.clicked.connect(lambda checked, r=row: self.remove_item(r))
            self.items_table.setCellWidget(row, 4, delete_btn)
            
            self.total_amount += item["total"]
        
        self.total_amount_label.setText(f"{self.total_amount:.2f} ج.م")
    
    def remove_item(self, row):
        """حذف منتج من الطلب"""
        if row < len(self.order_items):
            del self.order_items[row]
            self.update_items_table()
    
    def save_order(self):
        """حفظ طلب الشراء"""
        if not self.order_items:
            QMessageBox.warning(self, "تحذير", "يرجى إضافة منتجات للطلب!")
            return
        
        if self.supplier_combo.currentIndex() == -1:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد!")
            return
        
        order_data = {
            'order_number': self.order_number.text(),
            'order_date': self.order_date.date().toString(),
            'supplier': self.supplier_combo.currentText(),
            'delivery_date': self.delivery_date.date().toString(),
            'items': self.order_items.copy(),
            'total_amount': self.total_amount,
            'notes': self.notes_input.toPlainText().strip()
        }
        
        QMessageBox.information(self, "تم الحفظ", f"تم حفظ طلب الشراء رقم {order_data['order_number']} بنجاح!")
        self.accept()
    
    def get_input_style(self):
        return """
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit, QTextEdit {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 12px;
                background: white;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus, QTextEdit:focus {
                border-color: #17a2b8;
            }
        """


class PurchasesAnalyticsWidget(QWidget):
    """واجهة تحليلات المشتريات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة التحليلات"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان التحليلات
        title = QLabel("📊 تحليلات المشتريات")
        title.setStyleSheet("""
            color: #17a2b8;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # الإحصائيات الرئيسية
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 15px;
            }
        """)
        stats_layout = QGridLayout(stats_frame)
        
        # إجمالي المشتريات
        total_purchases = self.create_stat_card("💰", "1,234,567", "إجمالي المشتريات", "#17a2b8")
        stats_layout.addWidget(total_purchases, 0, 0)
        
        # عدد الطلبات
        total_orders = self.create_stat_card("📋", "156", "طلبات الشراء", "#28a745")
        stats_layout.addWidget(total_orders, 0, 1)
        
        # متوسط قيمة الطلب
        avg_order = self.create_stat_card("📊", "7,916", "متوسط الطلب", "#ffc107")
        stats_layout.addWidget(avg_order, 1, 0)
        
        # عدد الموردين
        suppliers_count = self.create_stat_card("🏢", "23", "الموردين النشطين", "#6f42c1")
        stats_layout.addWidget(suppliers_count, 1, 1)
        
        layout.addWidget(stats_frame)
        
        # أفضل الموردين
        top_suppliers_frame = QFrame()
        top_suppliers_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 15px;
            }
        """)
        suppliers_layout = QVBoxLayout(top_suppliers_frame)
        
        suppliers_title = QLabel("🏆 أفضل الموردين")
        suppliers_title.setStyleSheet("""
            color: #495057;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        suppliers_layout.addWidget(suppliers_title)
        
        # قائمة أفضل الموردين
        suppliers_data = [
            {"name": "شركة التقنية المتقدمة", "amount": 450000, "orders": 45},
            {"name": "مؤسسة الأجهزة الذكية", "amount": 380000, "orders": 32},
            {"name": "شركة المنسوجات الحديثة", "amount": 280000, "orders": 28},
            {"name": "دار الكتب العلمية", "amount": 124567, "orders": 51}
        ]
        
        for i, supplier in enumerate(suppliers_data):
            supplier_widget = self.create_supplier_item(i+1, supplier)
            suppliers_layout.addWidget(supplier_widget)
        
        layout.addWidget(top_suppliers_frame)
        
        # الرسم البياني (محاكاة)
        chart_frame = QFrame()
        chart_frame.setFixedHeight(150)
        chart_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 2px dashed #dee2e6;
            }
        """)
        
        chart_layout = QVBoxLayout(chart_frame)
        chart_label = QLabel("📈 رسم بياني للمشتريات الشهرية\n(سيتم إضافته قريباً)")
        chart_label.setAlignment(Qt.AlignCenter)
        chart_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        chart_layout.addWidget(chart_label)
        
        layout.addWidget(chart_frame)
        layout.addStretch()
    
    def create_stat_card(self, icon, value, label, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: white;
                border-left: 4px solid {color};
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }}
            QFrame:hover {{
                background: #f8f9fa;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setSpacing(8)
        
        # الأيقونة والقيمة
        top_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"font-size: 24px; color: {color};")
        
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            color: {color};
            font-size: 18px;
            font-weight: bold;
        """)
        value_label.setAlignment(Qt.AlignRight)
        
        top_layout.addWidget(icon_label)
        top_layout.addStretch()
        top_layout.addWidget(value_label)
        
        # التسمية
        label_widget = QLabel(label)
        label_widget.setStyleSheet("""
            color: #6c757d;
            font-size: 11px;
            font-weight: bold;
        """)
        
        layout.addLayout(top_layout)
        layout.addWidget(label_widget)
        
        return card
    
    def create_supplier_item(self, rank, supplier):
        """إنشاء عنصر مورد"""
        item_frame = QFrame()
        item_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 4px;
                padding: 10px;
                margin: 2px 0;
                border: 1px solid #e9ecef;
            }
            QFrame:hover {
                background: #e9ecef;
            }
        """)
        
        layout = QHBoxLayout(item_frame)
        
        # الترتيب
        rank_label = QLabel(f"#{rank}")
        rank_label.setFixedSize(25, 25)
        rank_label.setStyleSheet("""
            background: #17a2b8;
            color: white;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        """)
        rank_label.setAlignment(Qt.AlignCenter)
        
        # معلومات المورد
        info_layout = QVBoxLayout()
        info_layout.setSpacing(2)
        
        name_label = QLabel(supplier["name"])
        name_label.setStyleSheet("font-size: 12px; font-weight: bold; color: #495057;")
        
        details_label = QLabel(f"{supplier['amount']:,.0f} ج.م • {supplier['orders']} طلب")
        details_label.setStyleSheet("font-size: 10px; color: #6c757d;")
        
        info_layout.addWidget(name_label)
        info_layout.addWidget(details_label)
        
        layout.addWidget(rank_label)
        layout.addLayout(info_layout)
        layout.addStretch()
        
        return item_frame


class PurchasesTableWidget(QTableWidget):
    """جدول المشتريات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_table()
        self.load_purchases_data()
    
    def init_table(self):
        """إعداد الجدول"""
        columns = [
            "رقم الطلب", "التاريخ", "المورد", "عدد المنتجات", 
            "الإجمالي", "تاريخ التسليم", "الحالة", "الإجراءات"
        ]
        
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 12px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #495057;
                padding: 12px 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        # إعدادات الجدول
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        self.verticalHeader().setVisible(False)
        
        # تحديد عرض الأعمدة
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)   # رقم الطلب
        header.setSectionResizeMode(1, QHeaderView.Fixed)   # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Stretch) # المورد
        header.setSectionResizeMode(3, QHeaderView.Fixed)   # عدد المنتجات
        header.setSectionResizeMode(4, QHeaderView.Fixed)   # الإجمالي
        header.setSectionResizeMode(5, QHeaderView.Fixed)   # تاريخ التسليم
        header.setSectionResizeMode(6, QHeaderView.Fixed)   # الحالة
        header.setSectionResizeMode(7, QHeaderView.Fixed)   # الإجراءات
        
        self.setColumnWidth(0, 100)  # رقم الطلب
        self.setColumnWidth(1, 100)  # التاريخ
        self.setColumnWidth(3, 100)  # عدد المنتجات
        self.setColumnWidth(4, 100)  # الإجمالي
        self.setColumnWidth(5, 100)  # تاريخ التسليم
        self.setColumnWidth(6, 80)   # الحالة
        self.setColumnWidth(7, 120)  # الإجراءات
    
    def load_purchases_data(self):
        """تحميل بيانات المشتريات"""
        purchases_data = [
            {"order": "PO-001", "date": "2024-01-15", "supplier": "شركة التقنية المتقدمة", 
             "items": 25, "total": 375000, "delivery": "2024-01-22", "status": "مؤكد"},
            {"order": "PO-002", "date": "2024-01-14", "supplier": "مؤسسة الأجهزة الذكية", 
             "items": 15, "total": 120000, "delivery": "2024-01-21", "status": "قيد التسليم"},
            {"order": "PO-003", "date": "2024-01-13", "supplier": "شركة المنسوجات الحديثة", 
             "items": 200, "total": 24000, "delivery": "2024-01-20", "status": "مكتمل"},
            {"order": "PO-004", "date": "2024-01-12", "supplier": "دار الكتب العلمية", 
             "items": 50, "total": 12500, "delivery": "2024-01-19", "status": "مؤكد"},
            {"order": "PO-005", "date": "2024-01-11", "supplier": "شركة الأدوات المنزلية", 
             "items": 30, "total": 9000, "delivery": "2024-01-18", "status": "ملغي"},
        ]
        
        self.setRowCount(len(purchases_data))
        
        for row, purchase in enumerate(purchases_data):
            # رقم الطلب
            order_item = QTableWidgetItem(purchase["order"])
            order_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 0, order_item)
            
            # التاريخ
            date_item = QTableWidgetItem(purchase["date"])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 1, date_item)
            
            # المورد
            self.setItem(row, 2, QTableWidgetItem(purchase["supplier"]))
            
            # عدد المنتجات
            items_item = QTableWidgetItem(f"{purchase['items']} منتج")
            items_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 3, items_item)
            
            # الإجمالي
            total_item = QTableWidgetItem(f"{purchase['total']:,.0f} ج.م")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 4, total_item)
            
            # تاريخ التسليم
            delivery_item = QTableWidgetItem(purchase["delivery"])
            delivery_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 5, delivery_item)
            
            # الحالة
            status_colors = {
                "مؤكد": {"bg": "#fff3cd", "fg": "#856404"},
                "قيد التسليم": {"bg": "#cce5ff", "fg": "#004085"},
                "مكتمل": {"bg": "#d4edda", "fg": "#155724"},
                "ملغي": {"bg": "#f8d7da", "fg": "#721c24"}
            }
            
            status_item = QTableWidgetItem(purchase["status"])
            status_item.setTextAlignment(Qt.AlignCenter)
            color = status_colors.get(purchase["status"], status_colors["مؤكد"])
            status_item.setBackground(QColor(color["bg"]))
            status_item.setForeground(QColor(color["fg"]))
            self.setItem(row, 6, status_item)
            
            # الإجراءات
            actions_widget = self.create_actions_widget(row, purchase)
            self.setCellWidget(row, 7, actions_widget)
    
    def create_actions_widget(self, row, purchase_data):
        """إنشاء أزرار الإجراءات"""
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(5, 2, 5, 2)
        actions_layout.setSpacing(3)
        
        # زر عرض التفاصيل
        view_btn = QPushButton("👁️")
        view_btn.setFixedSize(22, 22)
        view_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        view_btn.setToolTip("عرض التفاصيل")
        view_btn.clicked.connect(lambda: self.view_purchase_details(row, purchase_data))
        
        # زر تحديث الحالة
        update_btn = QPushButton("🔄")
        update_btn.setFixedSize(22, 22)
        update_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        update_btn.setToolTip("تحديث الحالة")
        update_btn.clicked.connect(lambda: self.update_status(row, purchase_data))
        
        # زر طباعة
        print_btn = QPushButton("🖨️")
        print_btn.setFixedSize(22, 22)
        print_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        print_btn.setToolTip("طباعة الطلب")
        print_btn.clicked.connect(lambda: self.print_order(row, purchase_data))
        
        actions_layout.addWidget(view_btn)
        actions_layout.addWidget(update_btn)
        actions_layout.addWidget(print_btn)
        actions_layout.addStretch()
        
        return actions_widget
    
    def view_purchase_details(self, row, purchase_data):
        """عرض تفاصيل طلب الشراء"""
        details_text = f"""
        📋 تفاصيل طلب الشراء:
        
        🔢 رقم الطلب: {purchase_data['order']}
        📅 تاريخ الطلب: {purchase_data['date']}
        🏢 المورد: {purchase_data['supplier']}
        📦 عدد المنتجات: {purchase_data['items']} منتج
        💰 الإجمالي: {purchase_data['total']:,.0f} ج.م
        🚚 تاريخ التسليم: {purchase_data['delivery']}
        ✅ الحالة: {purchase_data['status']}
        """
        
        QMessageBox.information(self, "تفاصيل طلب الشراء", details_text)
    
    def update_status(self, row, purchase_data):
        """تحديث حالة الطلب"""
        statuses = ["مؤكد", "قيد التسليم", "مكتمل", "ملغي"]
        current_status = purchase_data['status']
        
        # نافذة اختيار الحالة الجديدة
        from PySide6.QtWidgets import QInputDialog
        new_status, ok = QInputDialog.getItem(
            self, "تحديث الحالة", "اختر الحالة الجديدة:",
            statuses, statuses.index(current_status), False
        )
        
        if ok and new_status != current_status:
            QMessageBox.information(self, "تحديث الحالة", 
                f"تم تحديث حالة الطلب {purchase_data['order']} إلى: {new_status}")
            self.load_purchases_data()
    
    def print_order(self, row, purchase_data):
        """طباعة طلب الشراء"""
        QMessageBox.information(self, "طباعة", f"تم إرسال طلب الشراء {purchase_data['order']} للطباعة!")


class EnhancedPurchasesWindow(QWidget):
    """نافذة المشتريات المحسنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # المحتوى الرئيسي
        content_splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - التحليلات
        left_panel = self.create_left_panel()
        left_panel.setMaximumWidth(350)
        content_splitter.addWidget(left_panel)
        
        # الجانب الأيمن - إدارة المشتريات
        right_panel = self.create_right_panel()
        content_splitter.addWidget(right_panel)
        
        content_splitter.setSizes([350, 850])
        main_layout.addWidget(content_splitter)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
    
    def create_header(self):
        """إنشاء الرأس"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #17a2b8, stop:0.5 #20c997, stop:1 #17a2b8);
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # معلومات الصفحة
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        title = QLabel("🏭 إدارة المشتريات")
        title.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
        """)
        
        subtitle = QLabel("إدارة طلبات الشراء والموردين مع تحليلات شاملة ومتابعة دقيقة")
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-top: 5px;
        """)
        
        info_layout.addWidget(title)
        info_layout.addWidget(subtitle)
        
        # الإحصائيات السريعة
        stats_widget = self.create_purchases_stats()
        
        layout.addWidget(info_widget, 7)
        layout.addWidget(stats_widget, 3)
        
        return header_frame
    
    def create_purchases_stats(self):
        """إنشاء إحصائيات المشتريات"""
        stats_widget = QWidget()
        stats_layout = QHBoxLayout(stats_widget)
        stats_layout.setSpacing(10)
        
        # طلبات الشهر
        month_orders = self.create_stat_card("📋", "156", "طلبات الشهر")
        stats_layout.addWidget(month_orders)
        
        # القيمة الإجمالية
        total_value = self.create_stat_card("💰", "1.2M", "القيمة الإجمالية")
        stats_layout.addWidget(total_value)
        
        # طلبات معلقة
        pending_orders = self.create_stat_card("⏳", "23", "طلبات معلقة")
        stats_layout.addWidget(pending_orders)
        
        return stats_widget
    
    def create_stat_card(self, icon, value, label):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("color: white; font-size: 20px;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 10px;")
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(label_widget)
        
        return card
    
    def create_left_panel(self):
        """إنشاء اللوحة اليسرى"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(15)
        
        # التحليلات
        self.analytics_widget = PurchasesAnalyticsWidget()
        left_layout.addWidget(self.analytics_widget)
        
        return left_widget
    
    def create_right_panel(self):
        """إنشاء اللوحة اليمنى"""
        right_frame = QFrame()
        right_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        
        layout = QVBoxLayout(right_frame)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # الجدول
        self.purchases_table = PurchasesTableWidget()
        layout.addWidget(self.purchases_table)
        
        return right_frame
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        
        # زر طلب شراء جديد
        new_order_btn = QPushButton("➕ طلب شراء جديد")
        new_order_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        new_order_btn.clicked.connect(self.create_new_order)
        
        # زر استيراد
        import_btn = QPushButton("📥 استيراد")
        import_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        import_btn.clicked.connect(self.import_orders)
        
        # زر تصدير
        export_btn = QPushButton("📤 تصدير")
        export_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        export_btn.clicked.connect(self.export_orders)
        
        # زر التقارير
        reports_btn = QPushButton("📊 التقارير")
        reports_btn.setStyleSheet("""
            QPushButton {
                background: #6f42c1;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #5a32a3;
            }
        """)
        reports_btn.clicked.connect(self.generate_reports)
        
        layout.addWidget(new_order_btn)
        layout.addWidget(import_btn)
        layout.addWidget(export_btn)
        layout.addWidget(reports_btn)
        layout.addStretch()
        
        # فلتر سريع
        filter_combo = QComboBox()
        filter_combo.addItems(["جميع الطلبات", "مؤكد", "قيد التسليم", "مكتمل", "ملغي"])
        filter_combo.setStyleSheet("""
            QComboBox {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
                font-size: 12px;
            }
        """)
        layout.addWidget(QLabel("فلتر:"))
        layout.addWidget(filter_combo)
        
        return toolbar_frame
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFixedHeight(40)
        status_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 8px 15px;
            }
        """)
        
        layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز - نظام المشتريات متاح")
        self.status_label.setStyleSheet("color: #17a2b8; font-weight: bold; font-size: 12px;")
        
        self.orders_count_label = QLabel("5 طلبات")
        self.orders_count_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        
        self.total_value_label = QLabel("إجمالي القيمة: 540,500 ج.م")
        self.total_value_label.setStyleSheet("color: #007bff; font-weight: bold; font-size: 12px;")
        
        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.orders_count_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.total_value_label)
        
        return status_frame
    
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_data)
        self.update_timer.start(60000)  # تحديث كل دقيقة
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.status_label.setText(f"تم التحديث - {datetime.now().strftime('%H:%M')}")
    
    def create_new_order(self):
        """إنشاء طلب شراء جديد"""
        dialog = PurchaseOrderDialog(parent=self)
        if dialog.exec() == QDialog.Accepted:
            self.purchases_table.load_purchases_data()
            self.status_label.setText("تم إنشاء طلب شراء جديد")
    
    def import_orders(self):
        """استيراد طلبات الشراء"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "استيراد طلبات الشراء", "",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;All Files (*)"
        )
        
        if file_path:
            QMessageBox.information(self, "استيراد", f"تم استيراد طلبات الشراء من:\n{file_path}")
    
    def export_orders(self):
        """تصدير طلبات الشراء"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير طلبات الشراء", "purchase_orders.xlsx",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;PDF Files (*.pdf)"
        )
        
        if file_path:
            QMessageBox.information(self, "تصدير", f"تم تصدير طلبات الشراء إلى:\n{file_path}")
    
    def generate_reports(self):
        """إنشاء التقارير"""
        QMessageBox.information(self, "التقارير", "تم إنشاء تقارير المشتريات بنجاح!")