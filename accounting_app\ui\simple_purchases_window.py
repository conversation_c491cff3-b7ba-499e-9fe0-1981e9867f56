"""
صفحة المشتريات البسيطة
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QTableWidget, QTableWidgetItem,
                              QHeaderView, QComboBox, QLineEdit,
                              QMessageBox)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QColor
from datetime import datetime


class SimplePurchasesWindow(QWidget):
    """نافذة المشتريات البسيطة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.purchases_data = []
        self.init_ui()
        self.load_sample_data()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # شريط البحث
        search_frame = self.create_search_frame()
        main_layout.addWidget(search_frame)
        
        # الجدول
        table_frame = self.create_table_frame()
        main_layout.addWidget(table_frame)
    
    def create_header(self):
        """إنشاء رأس الصفحة"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #17a2b8, stop:1 #20c997);
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        title = QLabel("🛒 إدارة المشتريات")
        title.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
        """)
        
        # أزرار سريعة
        buttons_layout = QHBoxLayout()
        
        new_btn = QPushButton("➕ طلب جديد")
        new_btn.setStyleSheet(self.get_button_style("#28a745"))
        new_btn.clicked.connect(self.create_new_order)
        
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setStyleSheet(self.get_button_style("#17a2b8"))
        refresh_btn.clicked.connect(self.refresh_data)
        
        buttons_layout.addWidget(new_btn)
        buttons_layout.addWidget(refresh_btn)
        
        layout.addWidget(title)
        layout.addStretch()
        layout.addLayout(buttons_layout)
        
        return header_frame
    
    def create_search_frame(self):
        """إنشاء شريط البحث"""
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 15px;
            }
        """)
        
        layout = QHBoxLayout(search_frame)
        
        layout.addWidget(QLabel("🔍 البحث:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث في طلبات الشراء...")
        self.search_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.search_input)
        
        layout.addWidget(QLabel("📋 الحالة:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(["الكل", "مؤكد", "قيد التسليم", "مكتمل", "ملغي"])
        self.status_filter.setStyleSheet(self.get_input_style())
        layout.addWidget(self.status_filter)
        
        return search_frame
    
    def create_table_frame(self):
        """إنشاء إطار الجدول"""
        table_frame = QFrame()
        table_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(table_frame)
        
        table_title = QLabel("📋 طلبات الشراء")
        table_title.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(table_title)
        
        # الجدول
        self.purchases_table = QTableWidget()
        self.purchases_table.setColumnCount(6)
        self.purchases_table.setHorizontalHeaderLabels([
            "رقم الطلب", "التاريخ", "المورد", "المبلغ", "الحالة", "الإجراءات"
        ])
        
        self.purchases_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: none;
                font-size: 12px;
            }
            QHeaderView::section {
                background: #17a2b8;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # تحديد عرض الأعمدة
        header = self.purchases_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.setSectionResizeMode(1, QHeaderView.Fixed)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.Fixed)
        header.setSectionResizeMode(4, QHeaderView.Fixed)
        header.setSectionResizeMode(5, QHeaderView.Fixed)
        
        self.purchases_table.setColumnWidth(0, 100)
        self.purchases_table.setColumnWidth(1, 100)
        self.purchases_table.setColumnWidth(3, 100)
        self.purchases_table.setColumnWidth(4, 80)
        self.purchases_table.setColumnWidth(5, 120)
        
        self.purchases_table.setAlternatingRowColors(True)
        self.purchases_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(self.purchases_table)
        return table_frame
    
    def load_sample_data(self):
        """تحميل بيانات تجريبية"""
        sample_data = [
            {"order": "PO-001", "date": "2024-01-15", "supplier": "شركة التقنية", "amount": 15000, "status": "مؤكد"},
            {"order": "PO-002", "date": "2024-01-14", "supplier": "مؤسسة الأجهزة", "amount": 8500, "status": "قيد التسليم"},
            {"order": "PO-003", "date": "2024-01-13", "supplier": "شركة المنسوجات", "amount": 12000, "status": "مكتمل"},
            {"order": "PO-004", "date": "2024-01-12", "supplier": "دار الكتب", "amount": 3500, "status": "مؤكد"},
            {"order": "PO-005", "date": "2024-01-11", "supplier": "شركة التقنية", "amount": 22000, "status": "مكتمل"},
        ]
        
        self.purchases_data = sample_data
        self.update_table()
    
    def update_table(self):
        """تحديث الجدول"""
        self.purchases_table.setRowCount(len(self.purchases_data))
        
        for row, purchase in enumerate(self.purchases_data):
            # رقم الطلب
            self.purchases_table.setItem(row, 0, QTableWidgetItem(purchase["order"]))
            
            # التاريخ
            self.purchases_table.setItem(row, 1, QTableWidgetItem(purchase["date"]))
            
            # المورد
            self.purchases_table.setItem(row, 2, QTableWidgetItem(purchase["supplier"]))
            
            # المبلغ
            amount_item = QTableWidgetItem(f"{purchase['amount']:,} ج.م")
            amount_item.setTextAlignment(Qt.AlignCenter)
            self.purchases_table.setItem(row, 3, amount_item)
            
            # الحالة
            status_item = QTableWidgetItem(purchase["status"])
            status_item.setTextAlignment(Qt.AlignCenter)
            
            # تلوين الحالة
            if purchase["status"] == "مكتمل":
                status_item.setBackground(QColor("#d4edda"))
            elif purchase["status"] == "قيد التسليم":
                status_item.setBackground(QColor("#fff3cd"))
            elif purchase["status"] == "مؤكد":
                status_item.setBackground(QColor("#cce5ff"))
            
            self.purchases_table.setItem(row, 4, status_item)
            
            # أزرار الإجراءات
            actions_widget = self.create_actions_widget(row, purchase)
            self.purchases_table.setCellWidget(row, 5, actions_widget)
    
    def create_actions_widget(self, row, purchase_data):
        """إنشاء أزرار الإجراءات"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 2, 5, 2)
        layout.setSpacing(5)
        
        # زر العرض
        view_btn = QPushButton("👁️")
        view_btn.setFixedSize(25, 25)
        view_btn.setStyleSheet(self.get_small_button_style("#17a2b8"))
        view_btn.setToolTip("عرض التفاصيل")
        view_btn.clicked.connect(lambda: self.view_details(purchase_data))
        
        # زر التعديل
        edit_btn = QPushButton("✏️")
        edit_btn.setFixedSize(25, 25)
        edit_btn.setStyleSheet(self.get_small_button_style("#ffc107"))
        edit_btn.setToolTip("تعديل")
        edit_btn.clicked.connect(lambda: self.edit_purchase(purchase_data))
        
        layout.addWidget(view_btn)
        layout.addWidget(edit_btn)
        layout.addStretch()
        
        return widget
    
    def get_button_style(self, color):
        """نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 15px;
                font-size: 12px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
        """
    
    def get_small_button_style(self, color):
        """نمط الأزرار الصغيرة"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 12px;
                font-size: 10px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
        """
    
    def get_input_style(self):
        """نمط حقول الإدخال"""
        return """
            QLineEdit, QComboBox {
                padding: 8px 12px;
                border: 1px solid #ced4da;
                border-radius: 6px;
                font-size: 12px;
                background: white;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #17a2b8;
            }
        """
    
    def darken_color(self, color, factor=0.2):
        """تظليل اللون"""
        color_map = {
            "#28a745": "#218838",
            "#17a2b8": "#138496",
            "#ffc107": "#e0a800",
            "#dc3545": "#c82333"
        }
        return color_map.get(color, "#333333")
    
    def create_new_order(self):
        """إنشاء طلب جديد"""
        QMessageBox.information(self, "طلب جديد", "سيتم فتح نافذة إنشاء طلب شراء جديد قريباً!")
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_sample_data()
        QMessageBox.information(self, "تحديث", "تم تحديث البيانات بنجاح!")
    
    def view_details(self, purchase_data):
        """عرض تفاصيل الطلب"""
        details = f"""
        📋 تفاصيل طلب الشراء:
        
        رقم الطلب: {purchase_data['order']}
        التاريخ: {purchase_data['date']}
        المورد: {purchase_data['supplier']}
        المبلغ: {purchase_data['amount']:,} ج.م
        الحالة: {purchase_data['status']}
        """
        QMessageBox.information(self, "تفاصيل الطلب", details)
    
    def edit_purchase(self, purchase_data):
        """تعديل الطلب"""
        QMessageBox.information(self, "تعديل", f"سيتم فتح نافذة تعديل الطلب {purchase_data['order']} قريباً!")
