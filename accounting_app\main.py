"""
نقطة الدخول الرئيسية للتطبيق
"""
import sys
import os
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTranslator, QLocale, QLibraryInfo, Qt
from accounting_app.ui.login_window import LoginWindow
from accounting_app.bll.settings_manager import SettingsManager
from accounting_app.utils.app_manager import AppManager


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    try:
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # منع إغلاق التطبيق تلقائياً عند إغلاق أي نافذة
        app.setQuitOnLastWindowClosed(False)
        
        # إنشاء مدير التطبيق
        app_manager = AppManager()
        app.app_manager = app_manager  # الاحتفاظ بالمرجع
        
        # تعيين اسم التطبيق ومنظمته
        app.setApplicationName("نظام المحاسبة المتكامل")
        app.setOrganizationName("MyCompany")
        app.setApplicationVersion("2.0")
        
        # تهيئة مدير الإعدادات
        settings_manager = SettingsManager()
        
        # تحميل ملف الترجمة بناءً على إعدادات اللغة
        language = settings_manager.get_language()
        translator = QTranslator()
        
        if language == 'ar':
            # تحميل ملف الترجمة العربية لـ Qt
            qt_translator = QTranslator()
            try:
                translations_path = QLibraryInfo.path(QLibraryInfo.TranslationsPath)
                qt_translator.load("qt_ar", translations_path)
                app.installTranslator(qt_translator)
            except AttributeError:
                # للتوافق مع الإصدارات القديمة من PySide6
                qt_translator.load("qt_ar", QLibraryInfo.location(QLibraryInfo.TranslationsPath))
                app.installTranslator(qt_translator)
            
            # تعيين اتجاه التطبيق من اليمين إلى اليسار
            app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء وعرض نافذة تسجيل الدخول
        login_window = LoginWindow()
        
        # الاحتفاظ بمرجع للتطبيق لمنع حذفه من الذاكرة
        app.login_window = login_window
        app.main_window = None
        
        # عرض نافذة تسجيل الدخول
        login_window.show()
        
        # إعداد معالجة الإغلاق الآمن
        def cleanup():
            app_manager.cleanup_on_exit()
        
        app.aboutToQuit.connect(cleanup)
        
        # تنفيذ حلقة الأحداث الرئيسية
        exit_code = app.exec()
        
        # تنظيف نهائي
        cleanup()
        return exit_code
        
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    main()