"""
صفحة إدارة الموردين
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                              QTableWidgetItem, QPushButton, QLabel, QFrame,
                              QLineEdit, QComboBox, QDialog, QFormLayout,
                              QTextEdit, QMessageBox, QHeaderView, QSplitter,
                              QGroupBox, QGridLayout, QScrollArea, QDateEdit)
from PySide6.QtCore import Qt, QDate, QTimer
from PySide6.QtGui import QFont, QColor
from accounting_app.ui.styles import AppStyles
from datetime import datetime
import uuid


class SuppliersWindow(QWidget):
    """نافذة إدارة الموردين"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🏢 إدارة الموردين")
        self.setMinimumSize(1200, 800)
        self.suppliers_data = []
        self.init_ui()
        self.load_sample_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        main_layout.addWidget(toolbar)
        
        # المحتوى الرئيسي
        content = self.create_main_content()
        main_layout.addWidget(content)
    
    def create_header(self):
        """إنشاء رأس الصفحة"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2196f3, stop:0.5 #1976d2, stop:1 #0d47a1);
                border-radius: 15px;
                padding: 25px;
                color: white;
                min-height: 120px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # المعلومات النصية
        text_section = QWidget()
        text_layout = QVBoxLayout(text_section)
        
        title = QLabel("🏢 إدارة الموردين الشاملة")
        title.setStyleSheet("font-size: 28px; font-weight: bold; color: white; margin-bottom: 10px;")
        
        subtitle = QLabel("إدارة كاملة لجميع الموردين والشركاء التجاريين")
        subtitle.setStyleSheet("font-size: 16px; color: white; opacity: 0.9;")
        
        # إحصائيات سريعة
        stats_layout = QHBoxLayout()
        stats_widgets = [
            self.create_stat_card("إجمالي الموردين", "127", "🏢"),
            self.create_stat_card("الموردين النشطين", "98", "✅"),
            self.create_stat_card("المعاملات الشهرية", "45", "📊")
        ]
        
        for widget in stats_widgets:
            stats_layout.addWidget(widget)
        stats_layout.addStretch()
        
        text_layout.addWidget(title)
        text_layout.addWidget(subtitle)
        text_layout.addSpacing(10)
        text_layout.addLayout(stats_layout)
        
        # الأيقونة
        icon_label = QLabel("🏢")
        icon_label.setStyleSheet("""
            font-size: 80px;
            color: white;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 40px;
            padding: 20px;
            min-width: 120px;
            max-width: 120px;
            min-height: 120px;
            max-height: 120px;
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(text_section, 8)
        layout.addWidget(icon_label, 2)
        
        return header_frame
    
    def create_stat_card(self, title, value, icon):
        """إنشاء بطاقة إحصائية"""
        card = QWidget()
        card.setStyleSheet("""
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 10px 15px;
            margin: 5px;
        """)
        
        layout = QHBoxLayout(card)
        layout.setContentsMargins(10, 8, 10, 8)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 20px; color: white;")
        
        text_widget = QWidget()
        text_layout = QVBoxLayout(text_widget)
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(2)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 12px; color: white; opacity: 0.8;")
        
        text_layout.addWidget(value_label)
        text_layout.addWidget(title_label)
        
        layout.addWidget(icon_label)
        layout.addWidget(text_widget)
        
        return card
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        layout.setSpacing(15)
        
        # زر إضافة مورد جديد
        add_btn = QPushButton("➕ إضافة مورد جديد")
        add_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                min-width: 150px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #3d8b40);
            }}
        """)
        add_btn.clicked.connect(self.add_supplier)
        
        # زر تعديل
        edit_btn = QPushButton("✏️ تعديل")
        edit_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976D2, stop:1 #1565C0);
            }}
        """)
        edit_btn.clicked.connect(self.edit_supplier)
        
        # زر حذف
        delete_btn = QPushButton("🗑️ حذف")
        delete_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f44336, stop:1 #d32f2f);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d32f2f, stop:1 #c62828);
            }}
        """)
        delete_btn.clicked.connect(self.delete_supplier)
        
        # مربع البحث
        search_input = QLineEdit()
        search_input.setPlaceholderText("🔍 البحث في الموردين...")
        search_input.setStyleSheet(f"""
            QLineEdit {{
                padding: 12px 15px;
                border: 2px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                background: white;
                min-width: 200px;
            }}
            QLineEdit:focus {{
                border-color: {AppStyles.PRIMARY_COLOR};
            }}
        """)
        search_input.textChanged.connect(self.search_suppliers)
        
        # فلتر الحالة
        status_filter = QComboBox()
        status_filter.addItems(["جميع الحالات", "نشط", "غير نشط", "محظور"])
        status_filter.setStyleSheet(f"""
            QComboBox {{
                padding: 12px 15px;
                border: 2px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                background: white;
                min-width: 150px;
            }}
            QComboBox:focus {{
                border-color: {AppStyles.PRIMARY_COLOR};
            }}
        """)
        status_filter.currentTextChanged.connect(self.filter_by_status)
        
        layout.addWidget(add_btn)
        layout.addWidget(edit_btn)
        layout.addWidget(delete_btn)
        layout.addStretch()
        layout.addWidget(QLabel("🔍 البحث:"))
        layout.addWidget(search_input)
        layout.addWidget(QLabel("📊 الحالة:"))
        layout.addWidget(status_filter)
        
        return toolbar_frame
    
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        splitter = QSplitter(Qt.Horizontal)
        
        # الجدول الرئيسي
        table_frame = self.create_suppliers_table()
        
        # لوحة التفاصيل
        details_frame = self.create_details_panel()
        
        splitter.addWidget(table_frame)
        splitter.addWidget(details_frame)
        splitter.setSizes([700, 400])
        
        return splitter
    
    def create_suppliers_table(self):
        """إنشاء جدول الموردين"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # عنوان الجدول
        title_label = QLabel("📋 قائمة الموردين")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        # الجدول
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(8)
        self.suppliers_table.setHorizontalHeaderLabels([
            "كود المورد", "اسم المورد", "رقم الهاتف", "البريد الإلكتروني", 
            "المدينة", "إجمالي المشتريات", "تاريخ التسجيل", "الحالة"
        ])
        
        # تنسيق الجدول
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #e3f2fd;
                font-size: 13px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f5f5f5, stop:1 #e0e0e0);
                color: #333;
                padding: 12px 8px;
                border: 1px solid #ddd;
                font-weight: bold;
                font-size: 14px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
        
        # ضبط عرض الأعمدة
        header = self.suppliers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # كود المورد
        header.setSectionResizeMode(1, QHeaderView.Stretch)           # اسم المورد
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # رقم الهاتف
        header.setSectionResizeMode(3, QHeaderView.Stretch)           # البريد الإلكتروني
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # المدينة
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # إجمالي المشتريات
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # تاريخ التسجيل
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # الحالة
        
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        layout.addWidget(title_label)
        layout.addWidget(self.suppliers_table)
        
        return frame
    
    def create_details_panel(self):
        """إنشاء لوحة التفاصيل"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # عنوان اللوحة
        title_label = QLabel("📊 تفاصيل المورد")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        # منطقة التفاصيل القابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; }")
        
        self.details_widget = QWidget()
        self.details_layout = QVBoxLayout(self.details_widget)
        
        # رسالة فارغة
        empty_label = QLabel("اختر مورداً لعرض تفاصيله")
        empty_label.setStyleSheet("""
            font-size: 16px;
            color: #666;
            text-align: center;
            padding: 50px;
        """)
        empty_label.setAlignment(Qt.AlignCenter)
        self.details_layout.addWidget(empty_label)
        
        scroll_area.setWidget(self.details_widget)
        
        layout.addWidget(title_label)
        layout.addWidget(scroll_area)
        
        return frame
    
    def load_sample_data(self):
        """تحميل البيانات الوهمية"""
        self.suppliers_data = [
            {
                "supplier_id": "SUP001",
                "name": "شركة التقنية المتقدمة",
                "phone": "0*********0",
                "email": "<EMAIL>",
                "address": "123 شارع النهضة، القاهرة",
                "city": "القاهرة",
                "country": "مصر",
                "total_purchases": 850000,
                "registration_date": "2023-01-15",
                "status": "نشط",
                "contact_person": "أحمد محمد",
                "tax_number": "*********",
                "payment_terms": "30 يوم",
                "notes": "مورد موثوق للأجهزة الإلكترونية"
            },
            {
                "supplier_id": "SUP002",
                "name": "مؤسسة الأجهزة الحديثة",
                "phone": "0*********1",
                "email": "<EMAIL>",
                "address": "456 شارع الجمهورية، الإسكندرية",
                "city": "الإسكندرية",
                "country": "مصر",
                "total_purchases": 620000,
                "registration_date": "2023-03-22",
                "status": "نشط",
                "contact_person": "فاطمة أحمد",
                "tax_number": "*********",
                "payment_terms": "45 يوم",
                "notes": "متخصص في الطابعات والماسحات الضوئية"
            },
            {
                "supplier_id": "SUP003",
                "name": "شركة البرمجيات العربية",
                "phone": "0*********2",
                "email": "<EMAIL>",
                "address": "789 شارع الحرية، الجيزة",
                "city": "الجيزة",
                "country": "مصر",
                "total_purchases": 450000,
                "registration_date": "2023-05-10",
                "status": "نشط",
                "contact_person": "محمد علي",
                "tax_number": "*********",
                "payment_terms": "60 يوم",
                "notes": "مورد تراخيص البرمجيات الأصلية"
            },
            {
                "supplier_id": "SUP004",
                "name": "مكتبة الكتب الإلكترونية",
                "phone": "0*********3",
                "email": "<EMAIL>",
                "address": "321 شارع الأزهر، القاهرة",
                "city": "القاهرة",
                "country": "مصر",
                "total_purchases": 125000,
                "registration_date": "2023-08-18",
                "status": "غير نشط",
                "contact_person": "سارة محمود",
                "tax_number": "*********",
                "payment_terms": "15 يوم",
                "notes": "متوقف مؤقتاً بسبب إعادة التنظيم"
            }
        ]
        
        self.refresh_table()
    
    def refresh_table(self):
        """تحديث الجدول"""
        self.suppliers_table.setRowCount(len(self.suppliers_data))
        
        for row, supplier in enumerate(self.suppliers_data):
            # كود المورد
            self.suppliers_table.setItem(row, 0, QTableWidgetItem(supplier["supplier_id"]))
            
            # اسم المورد
            self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier["name"]))
            
            # رقم الهاتف
            self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier["phone"]))
            
            # البريد الإلكتروني
            self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier["email"]))
            
            # المدينة
            self.suppliers_table.setItem(row, 4, QTableWidgetItem(supplier["city"]))
            
            # إجمالي المشتريات
            purchases_item = QTableWidgetItem(f"{supplier['total_purchases']:,.0f} ج.م")
            purchases_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.suppliers_table.setItem(row, 5, purchases_item)
            
            # تاريخ التسجيل
            self.suppliers_table.setItem(row, 6, QTableWidgetItem(supplier["registration_date"]))
            
            # الحالة
            status_item = QTableWidgetItem(supplier["status"])
            if supplier["status"] == "نشط":
                status_item.setBackground(QColor("#e8f5e8"))
                status_item.setForeground(QColor("#2e7d32"))
            elif supplier["status"] == "غير نشط":
                status_item.setBackground(QColor("#fff3e0"))
                status_item.setForeground(QColor("#ef6c00"))
            else:
                status_item.setBackground(QColor("#ffebee"))
                status_item.setForeground(QColor("#c62828"))
            
            self.suppliers_table.setItem(row, 7, status_item)
    
    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0 and current_row < len(self.suppliers_data):
            self.show_supplier_details(self.suppliers_data[current_row])
    
    def show_supplier_details(self, supplier):
        """عرض تفاصيل المورد"""
        # مسح المحتوى السابق
        for i in reversed(range(self.details_layout.count())):
            self.details_layout.itemAt(i).widget().setParent(None)
        
        # المعلومات الأساسية
        basic_info = QGroupBox("📋 المعلومات الأساسية")
        basic_info.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px;
            }
        """)
        
        basic_layout = QGridLayout(basic_info)
        basic_layout.setSpacing(10)
        
        basic_data = [
            ("كود المورد:", supplier["supplier_id"]),
            ("اسم المورد:", supplier["name"]),
            ("الشخص المسؤول:", supplier["contact_person"]),
            ("رقم الهاتف:", supplier["phone"]),
            ("البريد الإلكتروني:", supplier["email"]),
            ("المدينة:", supplier["city"]),
            ("الدولة:", supplier["country"]),
            ("الرقم الضريبي:", supplier["tax_number"])
        ]
        
        for i, (label, value) in enumerate(basic_data):
            label_widget = QLabel(label)
            label_widget.setStyleSheet("font-weight: 600; color: #555; font-size: 14px;")
            
            value_widget = QLabel(str(value))
            value_widget.setStyleSheet("color: #333; font-size: 14px;")
            
            basic_layout.addWidget(label_widget, i, 0)
            basic_layout.addWidget(value_widget, i, 1)
        
        # المعلومات المالية
        financial_info = QGroupBox("💰 المعلومات المالية")
        financial_info.setStyleSheet(basic_info.styleSheet())
        
        financial_layout = QGridLayout(financial_info)
        financial_layout.setSpacing(10)
        
        financial_data = [
            ("إجمالي المشتريات:", f"{supplier['total_purchases']:,.0f} ج.م"),
            ("شروط الدفع:", supplier["payment_terms"]),
            ("تاريخ التسجيل:", supplier["registration_date"]),
            ("الحالة:", supplier["status"])
        ]
        
        for i, (label, value) in enumerate(financial_data):
            label_widget = QLabel(label)
            label_widget.setStyleSheet("font-weight: 600; color: #555; font-size: 14px;")
            
            value_widget = QLabel(str(value))
            value_widget.setStyleSheet("color: #333; font-size: 14px;")
            
            financial_layout.addWidget(label_widget, i, 0)
            financial_layout.addWidget(value_widget, i, 1)
        
        # العنوان والملاحظات
        address_info = QGroupBox("📍 العنوان والملاحظات")
        address_info.setStyleSheet(basic_info.styleSheet())
        
        address_layout = QVBoxLayout(address_info)
        
        # العنوان
        address_label = QLabel("العنوان الكامل:")
        address_label.setStyleSheet("font-weight: 600; color: #555; font-size: 14px; margin-bottom: 5px;")
        
        address_value = QLabel(supplier["address"])
        address_value.setStyleSheet("color: #333; font-size: 14px; margin-bottom: 15px;")
        address_value.setWordWrap(True)
        
        # الملاحظات
        notes_label = QLabel("الملاحظات:")
        notes_label.setStyleSheet("font-weight: 600; color: #555; font-size: 14px; margin-bottom: 5px;")
        
        notes_value = QLabel(supplier["notes"])
        notes_value.setStyleSheet("color: #333; font-size: 14px;")
        notes_value.setWordWrap(True)
        
        address_layout.addWidget(address_label)
        address_layout.addWidget(address_value)
        address_layout.addWidget(notes_label)
        address_layout.addWidget(notes_value)
        
        # إضافة المجموعات
        self.details_layout.addWidget(basic_info)
        self.details_layout.addWidget(financial_info)
        self.details_layout.addWidget(address_info)
        self.details_layout.addStretch()
    
    def add_supplier(self):
        """إضافة مورد جديد"""
        dialog = SupplierDialog(parent=self)
        if dialog.exec() == QDialog.Accepted:
            supplier_data = dialog.get_supplier_data()
            supplier_data["supplier_id"] = f"SUP{len(self.suppliers_data)+1:03d}"
            supplier_data["registration_date"] = datetime.now().strftime("%Y-%m-%d")
            supplier_data["total_purchases"] = 0
            self.suppliers_data.append(supplier_data)
            self.refresh_table()
            QMessageBox.information(self, "نجح", "تم إضافة المورد بنجاح!")
    
    def edit_supplier(self):
        """تعديل مورد"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0:
            supplier = self.suppliers_data[current_row]
            dialog = SupplierDialog(supplier, parent=self)
            if dialog.exec() == QDialog.Accepted:
                updated_data = dialog.get_supplier_data()
                # الاحتفاظ ببعض البيانات الأصلية
                updated_data["supplier_id"] = supplier["supplier_id"]
                updated_data["registration_date"] = supplier["registration_date"]
                updated_data["total_purchases"] = supplier["total_purchases"]
                self.suppliers_data[current_row] = updated_data
                self.refresh_table()
                QMessageBox.information(self, "نجح", "تم تعديل بيانات المورد بنجاح!")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للتعديل")
    
    def delete_supplier(self):
        """حذف مورد"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0:
            supplier_name = self.suppliers_data[current_row]["name"]
            reply = QMessageBox.question(
                self, 
                "تأكيد الحذف", 
                f"هل تريد حذف المورد '{supplier_name}'؟\n\nسيتم حذف جميع البيانات المرتبطة به.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                del self.suppliers_data[current_row]
                self.refresh_table()
                # مسح لوحة التفاصيل
                for i in reversed(range(self.details_layout.count())):
                    self.details_layout.itemAt(i).widget().setParent(None)
                empty_label = QLabel("اختر مورداً لعرض تفاصيله")
                empty_label.setStyleSheet("font-size: 16px; color: #666; text-align: center; padding: 50px;")
                empty_label.setAlignment(Qt.AlignCenter)
                self.details_layout.addWidget(empty_label)
                QMessageBox.information(self, "نجح", "تم حذف المورد بنجاح!")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للحذف")
    
    def search_suppliers(self, text):
        """البحث في الموردين"""
        # تنفيذ البحث (يمكن تطويره أكثر)
        print(f"البحث عن: {text}")
    
    def filter_by_status(self, status):
        """فلترة حسب الحالة"""
        # تنفيذ الفلترة (يمكن تطويره أكثر)
        print(f"فلترة حسب الحالة: {status}")


class SupplierDialog(QDialog):
    """نافذة إضافة/تعديل مورد"""
    
    def __init__(self, supplier_data=None, parent=None):
        super().__init__(parent)
        self.supplier_data = supplier_data
        
        self.setWindowTitle("إضافة مورد جديد" if supplier_data is None else "تعديل بيانات المورد")
        self.setMinimumSize(600, 700)
        self.init_ui()
        
        if supplier_data:
            self.load_supplier_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # المعلومات الأساسية
        basic_group = self.create_basic_info_group()
        layout.addWidget(basic_group)
        
        # معلومات الاتصال
        contact_group = self.create_contact_info_group()
        layout.addWidget(contact_group)
        
        # المعلومات المالية
        financial_group = self.create_financial_info_group()
        layout.addWidget(financial_group)
        
        # الأزرار
        buttons_layout = self.create_buttons()
        layout.addLayout(buttons_layout)
    
    def create_basic_info_group(self):
        """إنشاء مجموعة المعلومات الأساسية"""
        group = QGroupBox("📋 المعلومات الأساسية")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # اسم المورد
        layout.addWidget(QLabel("اسم المورد:"), 0, 0)
        self.name_input = QLineEdit()
        self.name_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.name_input, 0, 1)
        
        # الشخص المسؤول
        layout.addWidget(QLabel("الشخص المسؤول:"), 0, 2)
        self.contact_person_input = QLineEdit()
        self.contact_person_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.contact_person_input, 0, 3)
        
        # الرقم الضريبي
        layout.addWidget(QLabel("الرقم الضريبي:"), 1, 0)
        self.tax_number_input = QLineEdit()
        self.tax_number_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.tax_number_input, 1, 1)
        
        # الحالة
        layout.addWidget(QLabel("الحالة:"), 1, 2)
        self.status_combo = QComboBox()
        self.status_combo.addItems(["نشط", "غير نشط", "محظور"])
        self.status_combo.setStyleSheet(self.get_input_style())
        layout.addWidget(self.status_combo, 1, 3)
        
        return group
    
    def create_contact_info_group(self):
        """إنشاء مجموعة معلومات الاتصال"""
        group = QGroupBox("📞 معلومات الاتصال")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # رقم الهاتف
        layout.addWidget(QLabel("رقم الهاتف:"), 0, 0)
        self.phone_input = QLineEdit()
        self.phone_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.phone_input, 0, 1)
        
        # البريد الإلكتروني
        layout.addWidget(QLabel("البريد الإلكتروني:"), 0, 2)
        self.email_input = QLineEdit()
        self.email_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.email_input, 0, 3)
        
        # المدينة
        layout.addWidget(QLabel("المدينة:"), 1, 0)
        self.city_input = QLineEdit()
        self.city_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.city_input, 1, 1)
        
        # الدولة
        layout.addWidget(QLabel("الدولة:"), 1, 2)
        self.country_input = QLineEdit()
        self.country_input.setText("مصر")
        self.country_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.country_input, 1, 3)
        
        # العنوان الكامل
        layout.addWidget(QLabel("العنوان الكامل:"), 2, 0)
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(80)
        self.address_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.address_input, 2, 1, 1, 3)
        
        return group
    
    def create_financial_info_group(self):
        """إنشاء مجموعة المعلومات المالية"""
        group = QGroupBox("💰 المعلومات المالية")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # شروط الدفع
        layout.addWidget(QLabel("شروط الدفع:"), 0, 0)
        self.payment_terms_combo = QComboBox()
        self.payment_terms_combo.addItems(["15 يوم", "30 يوم", "45 يوم", "60 يوم", "90 يوم", "نقداً"])
        self.payment_terms_combo.setStyleSheet(self.get_input_style())
        layout.addWidget(self.payment_terms_combo, 0, 1)
        
        # الملاحظات
        layout.addWidget(QLabel("الملاحظات:"), 1, 0)
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.notes_input, 1, 1, 1, 3)
        
        return group
    
    def create_buttons(self):
        """إنشاء أزرار الحوار"""
        layout = QHBoxLayout()
        layout.addStretch()
        
        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: #f44336;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }
            QPushButton:hover {
                background: #d32f2f;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        # زر الحفظ
        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet(f"""
            QPushButton {{
                background: {AppStyles.PRIMARY_COLOR};
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background: {AppStyles.HIGHLIGHT_COLOR};
            }}
        """)
        save_btn.clicked.connect(self.accept)
        
        layout.addWidget(cancel_btn)
        layout.addWidget(save_btn)
        
        return layout
    
    def get_input_style(self):
        """الحصول على نمط عناصر الإدخال"""
        return f"""
            QLineEdit, QComboBox, QTextEdit {{
                padding: 10px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 14px;
                background: white;
            }}
            QLineEdit:focus, QComboBox:focus, QTextEdit:focus {{
                border-color: {AppStyles.PRIMARY_COLOR};
            }}
        """
    
    def load_supplier_data(self):
        """تحميل بيانات المورد للتعديل"""
        self.name_input.setText(self.supplier_data["name"])
        self.contact_person_input.setText(self.supplier_data["contact_person"])
        self.tax_number_input.setText(self.supplier_data["tax_number"])
        self.status_combo.setCurrentText(self.supplier_data["status"])
        self.phone_input.setText(self.supplier_data["phone"])
        self.email_input.setText(self.supplier_data["email"])
        self.city_input.setText(self.supplier_data["city"])
        self.country_input.setText(self.supplier_data["country"])
        self.address_input.setPlainText(self.supplier_data["address"])
        self.payment_terms_combo.setCurrentText(self.supplier_data["payment_terms"])
        self.notes_input.setPlainText(self.supplier_data["notes"])
    
    def get_supplier_data(self):
        """الحصول على بيانات المورد"""
        return {
            "name": self.name_input.text().strip(),
            "contact_person": self.contact_person_input.text().strip(),
            "tax_number": self.tax_number_input.text().strip(),
            "status": self.status_combo.currentText(),
            "phone": self.phone_input.text().strip(),
            "email": self.email_input.text().strip(),
            "city": self.city_input.text().strip(),
            "country": self.country_input.text().strip(),
            "address": self.address_input.toPlainText().strip(),
            "payment_terms": self.payment_terms_combo.currentText(),
            "notes": self.notes_input.toPlainText().strip()
        }


class SupplierDialog(QDialog):
    """نافذة إضافة/تعديل مورد"""
    
    def __init__(self, supplier_data=None, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🏢 إدارة الموردين")
        self.setMinimumSize(1200, 800)
        self.suppliers_data = []
        self.init_ui()
        self.load_sample_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        main_layout.addWidget(toolbar)
        
        # المحتوى الرئيسي
        content = self.create_main_content()
        main_layout.addWidget(content)
    
    def create_header(self):
        """إنشاء رأس الصفحة"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2196f3, stop:0.5 #1976d2, stop:1 #0d47a1);
                border-radius: 15px;
                padding: 25px;
                color: white;
                min-height: 120px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # المعلومات النصية
        text_section = QWidget()
        text_layout = QVBoxLayout(text_section)
        
        title = QLabel("🏢 إدارة الموردين الشاملة")
        title.setStyleSheet("font-size: 28px; font-weight: bold; color: white; margin-bottom: 10px;")
        
        subtitle = QLabel("إدارة كاملة لجميع الموردين والشركاء التجاريين")
        subtitle.setStyleSheet("font-size: 16px; color: white; opacity: 0.9;")
        
        # إحصائيات سريعة
        stats_layout = QHBoxLayout()
        stats_widgets = [
            self.create_stat_card("إجمالي الموردين", "127", "🏢"),
            self.create_stat_card("الموردين النشطين", "98", "✅"),
            self.create_stat_card("المعاملات الشهرية", "45", "📊")
        ]
        
        for widget in stats_widgets:
            stats_layout.addWidget(widget)
        stats_layout.addStretch()
        
        text_layout.addWidget(title)
        text_layout.addWidget(subtitle)
        text_layout.addSpacing(10)
        text_layout.addLayout(stats_layout)
        
        # الأيقونة
        icon_label = QLabel("🏢")
        icon_label.setStyleSheet("""
            font-size: 80px;
            color: white;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 40px;
            padding: 20px;
            min-width: 120px;
            max-width: 120px;
            min-height: 120px;
            max-height: 120px;
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(text_section, 8)
        layout.addWidget(icon_label, 2)
        
        return header_frame
    
    def create_stat_card(self, title, value, icon):
        """إنشاء بطاقة إحصائية"""
        card = QWidget()
        card.setStyleSheet("""
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 10px 15px;
            margin: 5px;
        """)
        
        layout = QHBoxLayout(card)
        layout.setContentsMargins(10, 8, 10, 8)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 20px; color: white;")
        
        text_widget = QWidget()
        text_layout = QVBoxLayout(text_widget)
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(2)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 12px; color: white; opacity: 0.8;")
        
        text_layout.addWidget(value_label)
        text_layout.addWidget(title_label)
        
        layout.addWidget(icon_label)
        layout.addWidget(text_widget)
        
        return card
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        layout.setSpacing(15)
        
        # زر إضافة مورد جديد
        add_btn = QPushButton("➕ إضافة مورد جديد")
        add_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                min-width: 150px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #3d8b40);
            }}
        """)
        add_btn.clicked.connect(self.add_supplier)
        
        # زر تعديل
        edit_btn = QPushButton("✏️ تعديل")
        edit_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976D2, stop:1 #1565C0);
            }}
        """)
        edit_btn.clicked.connect(self.edit_supplier)
        
        # زر حذف
        delete_btn = QPushButton("🗑️ حذف")
        delete_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f44336, stop:1 #d32f2f);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d32f2f, stop:1 #c62828);
            }}
        """)
        delete_btn.clicked.connect(self.delete_supplier)
        
        # مربع البحث
        search_input = QLineEdit()
        search_input.setPlaceholderText("🔍 البحث في الموردين...")
        search_input.setStyleSheet(f"""
            QLineEdit {{
                padding: 12px 15px;
                border: 2px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                background: white;
                min-width: 200px;
            }}
            QLineEdit:focus {{
                border-color: {AppStyles.PRIMARY_COLOR};
            }}
        """)
        search_input.textChanged.connect(self.search_suppliers)
        
        # فلتر الحالة
        status_filter = QComboBox()
        status_filter.addItems(["جميع الحالات", "نشط", "غير نشط", "محظور"])
        status_filter.setStyleSheet(f"""
            QComboBox {{
                padding: 12px 15px;
                border: 2px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                background: white;
                min-width: 150px;
            }}
            QComboBox:focus {{
                border-color: {AppStyles.PRIMARY_COLOR};
            }}
        """)
        status_filter.currentTextChanged.connect(self.filter_by_status)
        
        layout.addWidget(add_btn)
        layout.addWidget(edit_btn)
        layout.addWidget(delete_btn)
        layout.addStretch()
        layout.addWidget(QLabel("🔍 البحث:"))
        layout.addWidget(search_input)
        layout.addWidget(QLabel("📊 الحالة:"))
        layout.addWidget(status_filter)
        
        return toolbar_frame
    
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        splitter = QSplitter(Qt.Horizontal)
        
        # الجدول الرئيسي
        table_frame = self.create_suppliers_table()
        
        # لوحة التفاصيل
        details_frame = self.create_details_panel()
        
        splitter.addWidget(table_frame)
        splitter.addWidget(details_frame)
        splitter.setSizes([700, 400])
        
        return splitter
    
    def create_suppliers_table(self):
        """إنشاء جدول الموردين"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # عنوان الجدول
        title_label = QLabel("📋 قائمة الموردين")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        # الجدول
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(8)
        self.suppliers_table.setHorizontalHeaderLabels([
            "كود المورد", "اسم المورد", "رقم الهاتف", "البريد الإلكتروني", 
            "المدينة", "إجمالي المشتريات", "تاريخ التسجيل", "الحالة"
        ])
        
        # تنسيق الجدول
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #e3f2fd;
                font-size: 13px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f5f5f5, stop:1 #e0e0e0);
                color: #333;
                padding: 12px 8px;
                border: 1px solid #ddd;
                font-weight: bold;
                font-size: 14px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
        
        # ضبط عرض الأعمدة
        header = self.suppliers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # كود المورد
        header.setSectionResizeMode(1, QHeaderView.Stretch)           # اسم المورد
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # رقم الهاتف
        header.setSectionResizeMode(3, QHeaderView.Stretch)           # البريد الإلكتروني
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # المدينة
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # إجمالي المشتريات
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # تاريخ التسجيل
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # الحالة
        
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        layout.addWidget(title_label)
        layout.addWidget(self.suppliers_table)
        
        return frame
    
    def create_details_panel(self):
        """إنشاء لوحة التفاصيل"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # عنوان اللوحة
        title_label = QLabel("📊 تفاصيل المورد")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        # منطقة التفاصيل القابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; }")
        
        self.details_widget = QWidget()
        self.details_layout = QVBoxLayout(self.details_widget)
        
        # رسالة فارغة
        empty_label = QLabel("اختر مورداً لعرض تفاصيله")
        empty_label.setStyleSheet("""
            font-size: 16px;
            color: #666;
            text-align: center;
            padding: 50px;
        """)
        empty_label.setAlignment(Qt.AlignCenter)
        self.details_layout.addWidget(empty_label)
        
        scroll_area.setWidget(self.details_widget)
        
        layout.addWidget(title_label)
        layout.addWidget(scroll_area)
        
        return frame
    
    def load_sample_data(self):
        """تحميل البيانات الوهمية"""
        self.suppliers_data = [
            {
                "supplier_id": "SUP001",
                "name": "شركة التقنية المتقدمة",
                "phone": "0*********0",
                "email": "<EMAIL>",
                "address": "123 شارع النهضة، القاهرة",
                "city": "القاهرة",
                "country": "مصر",
                "total_purchases": 850000,
                "registration_date": "2023-01-15",
                "status": "نشط",
                "contact_person": "أحمد محمد",
                "tax_number": "*********",
                "payment_terms": "30 يوم",
                "notes": "مورد موثوق للأجهزة الإلكترونية"
            },
            {
                "supplier_id": "SUP002",
                "name": "مؤسسة الأجهزة الحديثة",
                "phone": "0*********1",
                "email": "<EMAIL>",
                "address": "456 شارع الجمهورية، الإسكندرية",
                "city": "الإسكندرية",
                "country": "مصر",
                "total_purchases": 620000,
                "registration_date": "2023-03-22",
                "status": "نشط",
                "contact_person": "فاطمة أحمد",
                "tax_number": "*********",
                "payment_terms": "45 يوم",
                "notes": "متخصص في الطابعات والماسحات الضوئية"
            },
            {
                "supplier_id": "SUP003",
                "name": "شركة البرمجيات العربية",
                "phone": "0*********2",
                "email": "<EMAIL>",
                "address": "789 شارع الحرية، الجيزة",
                "city": "الجيزة",
                "country": "مصر",
                "total_purchases": 450000,
                "registration_date": "2023-05-10",
                "status": "نشط",
                "contact_person": "محمد علي",
                "tax_number": "*********",
                "payment_terms": "60 يوم",
                "notes": "مورد تراخيص البرمجيات الأصلية"
            },
            {
                "supplier_id": "SUP004",
                "name": "مكتبة الكتب الإلكترونية",
                "phone": "0*********3",
                "email": "<EMAIL>",
                "address": "321 شارع الأزهر، القاهرة",
                "city": "القاهرة",
                "country": "مصر",
                "total_purchases": 125000,
                "registration_date": "2023-08-18",
                "status": "غير نشط",
                "contact_person": "سارة محمود",
                "tax_number": "*********",
                "payment_terms": "15 يوم",
                "notes": "متوقف مؤقتاً بسبب إعادة التنظيم"
            }
        ]
        
        self.refresh_table()
    
    def refresh_table(self):
        """تحديث الجدول"""
        self.suppliers_table.setRowCount(len(self.suppliers_data))
        
        for row, supplier in enumerate(self.suppliers_data):
            # كود المورد
            self.suppliers_table.setItem(row, 0, QTableWidgetItem(supplier["supplier_id"]))
            
            # اسم المورد
            self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier["name"]))
            
            # رقم الهاتف
            self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier["phone"]))
            
            # البريد الإلكتروني
            self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier["email"]))
            
            # المدينة
            self.suppliers_table.setItem(row, 4, QTableWidgetItem(supplier["city"]))
            
            # إجمالي المشتريات
            purchases_item = QTableWidgetItem(f"{supplier['total_purchases']:,.0f} ج.م")
            purchases_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.suppliers_table.setItem(row, 5, purchases_item)
            
            # تاريخ التسجيل
            self.suppliers_table.setItem(row, 6, QTableWidgetItem(supplier["registration_date"]))
            
            # الحالة
            status_item = QTableWidgetItem(supplier["status"])
            if supplier["status"] == "نشط":
                status_item.setBackground(QColor("#e8f5e8"))
                status_item.setForeground(QColor("#2e7d32"))
            elif supplier["status"] == "غير نشط":
                status_item.setBackground(QColor("#fff3e0"))
                status_item.setForeground(QColor("#ef6c00"))
            else:
                status_item.setBackground(QColor("#ffebee"))
                status_item.setForeground(QColor("#c62828"))
            
            self.suppliers_table.setItem(row, 7, status_item)
    
    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0 and current_row < len(self.suppliers_data):
            self.show_supplier_details(self.suppliers_data[current_row])
    
    def show_supplier_details(self, supplier):
        """عرض تفاصيل المورد"""
        # مسح المحتوى السابق
        for i in reversed(range(self.details_layout.count())):
            self.details_layout.itemAt(i).widget().setParent(None)
        
        # المعلومات الأساسية
        basic_info = QGroupBox("📋 المعلومات الأساسية")
        basic_info.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px;
            }
        """)
        
        basic_layout = QGridLayout(basic_info)
        basic_layout.setSpacing(10)
        
        basic_data = [
            ("كود المورد:", supplier["supplier_id"]),
            ("اسم المورد:", supplier["name"]),
            ("الشخص المسؤول:", supplier["contact_person"]),
            ("رقم الهاتف:", supplier["phone"]),
            ("البريد الإلكتروني:", supplier["email"]),
            ("المدينة:", supplier["city"]),
            ("الدولة:", supplier["country"]),
            ("الرقم الضريبي:", supplier["tax_number"])
        ]
        
        for i, (label, value) in enumerate(basic_data):
            label_widget = QLabel(label)
            label_widget.setStyleSheet("font-weight: 600; color: #555; font-size: 14px;")
            
            value_widget = QLabel(str(value))
            value_widget.setStyleSheet("color: #333; font-size: 14px;")
            
            basic_layout.addWidget(label_widget, i, 0)
            basic_layout.addWidget(value_widget, i, 1)
        
        # المعلومات المالية
        financial_info = QGroupBox("💰 المعلومات المالية")
        financial_info.setStyleSheet(basic_info.styleSheet())
        
        financial_layout = QGridLayout(financial_info)
        financial_layout.setSpacing(10)
        
        financial_data = [
            ("إجمالي المشتريات:", f"{supplier['total_purchases']:,.0f} ج.م"),
            ("شروط الدفع:", supplier["payment_terms"]),
            ("تاريخ التسجيل:", supplier["registration_date"]),
            ("الحالة:", supplier["status"])
        ]
        
        for i, (label, value) in enumerate(financial_data):
            label_widget = QLabel(label)
            label_widget.setStyleSheet("font-weight: 600; color: #555; font-size: 14px;")
            
            value_widget = QLabel(str(value))
            value_widget.setStyleSheet("color: #333; font-size: 14px;")
            
            financial_layout.addWidget(label_widget, i, 0)
            financial_layout.addWidget(value_widget, i, 1)
        
        # العنوان والملاحظات
        address_info = QGroupBox("📍 العنوان والملاحظات")
        address_info.setStyleSheet(basic_info.styleSheet())
        
        address_layout = QVBoxLayout(address_info)
        
        # العنوان
        address_label = QLabel("العنوان الكامل:")
        address_label.setStyleSheet("font-weight: 600; color: #555; font-size: 14px; margin-bottom: 5px;")
        
        address_value = QLabel(supplier["address"])
        address_value.setStyleSheet("color: #333; font-size: 14px; margin-bottom: 15px;")
        address_value.setWordWrap(True)
        
        # الملاحظات
        notes_label = QLabel("الملاحظات:")
        notes_label.setStyleSheet("font-weight: 600; color: #555; font-size: 14px; margin-bottom: 5px;")
        
        notes_value = QLabel(supplier["notes"])
        notes_value.setStyleSheet("color: #333; font-size: 14px;")
        notes_value.setWordWrap(True)
        
        address_layout.addWidget(address_label)
        address_layout.addWidget(address_value)
        address_layout.addWidget(notes_label)
        address_layout.addWidget(notes_value)
        
        # إضافة المجموعات
        self.details_layout.addWidget(basic_info)
        self.details_layout.addWidget(financial_info)
        self.details_layout.addWidget(address_info)
        self.details_layout.addStretch()
    
    def add_supplier(self):
        """إضافة مورد جديد"""
        dialog = SupplierDialog(parent=self)
        if dialog.exec() == QDialog.Accepted:
            supplier_data = dialog.get_supplier_data()
            supplier_data["supplier_id"] = f"SUP{len(self.suppliers_data)+1:03d}"
            supplier_data["registration_date"] = datetime.now().strftime("%Y-%m-%d")
            supplier_data["total_purchases"] = 0
            self.suppliers_data.append(supplier_data)
            self.refresh_table()
            QMessageBox.information(self, "نجح", "تم إضافة المورد بنجاح!")
    
    def edit_supplier(self):
        """تعديل مورد"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0:
            supplier = self.suppliers_data[current_row]
            dialog = SupplierDialog(supplier, parent=self)
            if dialog.exec() == QDialog.Accepted:
                updated_data = dialog.get_supplier_data()
                # الاحتفاظ ببعض البيانات الأصلية
                updated_data["supplier_id"] = supplier["supplier_id"]
                updated_data["registration_date"] = supplier["registration_date"]
                updated_data["total_purchases"] = supplier["total_purchases"]
                self.suppliers_data[current_row] = updated_data
                self.refresh_table()
                QMessageBox.information(self, "نجح", "تم تعديل بيانات المورد بنجاح!")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للتعديل")
    
    def delete_supplier(self):
        """حذف مورد"""
        current_row = self.suppliers_table.currentRow()
        if current_row >= 0:
            supplier_name = self.suppliers_data[current_row]["name"]
            reply = QMessageBox.question(
                self, 
                "تأكيد الحذف", 
                f"هل تريد حذف المورد '{supplier_name}'؟\n\nسيتم حذف جميع البيانات المرتبطة به.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                del self.suppliers_data[current_row]
                self.refresh_table()
                # مسح لوحة التفاصيل
                for i in reversed(range(self.details_layout.count())):
                    self.details_layout.itemAt(i).widget().setParent(None)
                empty_label = QLabel("اختر مورداً لعرض تفاصيله")
                empty_label.setStyleSheet("font-size: 16px; color: #666; text-align: center; padding: 50px;")
                empty_label.setAlignment(Qt.AlignCenter)
                self.details_layout.addWidget(empty_label)
                QMessageBox.information(self, "نجح", "تم حذف المورد بنجاح!")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للحذف")
    
    def search_suppliers(self, text):
        """البحث في الموردين"""
        # تنفيذ البحث (يمكن تطويره أكثر)
        print(f"البحث عن: {text}")
    
    def filter_by_status(self, status):
        """فلترة حسب الحالة"""
        # تنفيذ الفلترة (يمكن تطويره أكثر)
        print(f"فلترة حسب الحالة: {status}")


class SupplierDialog(QDialog):
    """نافذة إضافة/تعديل مورد"""
    
    def __init__(self, supplier_data=None, parent=None):
        super().__init__(parent)
        self.supplier_data = supplier_data
        
        self.setWindowTitle("إضافة مورد جديد" if supplier_data is None else "تعديل بيانات المورد")
        self.setMinimumSize(600, 700)
        self.init_ui()
        
        if supplier_data:
            self.load_supplier_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # المعلومات الأساسية
        basic_group = self.create_basic_info_group()
        layout.addWidget(basic_group)
        
        # معلومات الاتصال
        contact_group = self.create_contact_info_group()
        layout.addWidget(contact_group)
        
        # المعلومات المالية
        financial_group = self.create_financial_info_group()
        layout.addWidget(financial_group)
        
        # الأزرار
        buttons_layout = self.create_buttons()
        layout.addLayout(buttons_layout)
    
    def create_basic_info_group(self):
        """إنشاء مجموعة المعلومات الأساسية"""
        group = QGroupBox("📋 المعلومات الأساسية")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # اسم المورد
        layout.addWidget(QLabel("اسم المورد:"), 0, 0)
        self.name_input = QLineEdit()
        self.name_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.name_input, 0, 1)
        
        # الشخص المسؤول
        layout.addWidget(QLabel("الشخص المسؤول:"), 0, 2)
        self.contact_person_input = QLineEdit()
        self.contact_person_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.contact_person_input, 0, 3)
        
        # الرقم الضريبي
        layout.addWidget(QLabel("الرقم الضريبي:"), 1, 0)
        self.tax_number_input = QLineEdit()
        self.tax_number_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.tax_number_input, 1, 1)
        
        # الحالة
        layout.addWidget(QLabel("الحالة:"), 1, 2)
        self.status_combo = QComboBox()
        self.status_combo.addItems(["نشط", "غير نشط", "محظور"])
        self.status_combo.setStyleSheet(self.get_input_style())
        layout.addWidget(self.status_combo, 1, 3)
        
        return group
    
    def create_contact_info_group(self):
        """إنشاء مجموعة معلومات الاتصال"""
        group = QGroupBox("📞 معلومات الاتصال")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # رقم الهاتف
        layout.addWidget(QLabel("رقم الهاتف:"), 0, 0)
        self.phone_input = QLineEdit()
        self.phone_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.phone_input, 0, 1)
        
        # البريد الإلكتروني
        layout.addWidget(QLabel("البريد الإلكتروني:"), 0, 2)
        self.email_input = QLineEdit()
        self.email_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.email_input, 0, 3)
        
        # المدينة
        layout.addWidget(QLabel("المدينة:"), 1, 0)
        self.city_input = QLineEdit()
        self.city_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.city_input, 1, 1)
        
        # الدولة
        layout.addWidget(QLabel("الدولة:"), 1, 2)
        self.country_input = QLineEdit()
        self.country_input.setText("مصر")
        self.country_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.country_input, 1, 3)
        
        # العنوان الكامل
        layout.addWidget(QLabel("العنوان الكامل:"), 2, 0)
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(80)
        self.address_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.address_input, 2, 1, 1, 3)
        
        return group
    
    def create_financial_info_group(self):
        """إنشاء مجموعة المعلومات المالية"""
        group = QGroupBox("💰 المعلومات المالية")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # شروط الدفع
        layout.addWidget(QLabel("شروط الدفع:"), 0, 0)
        self.payment_terms_combo = QComboBox()
        self.payment_terms_combo.addItems(["15 يوم", "30 يوم", "45 يوم", "60 يوم", "90 يوم", "نقداً"])
        self.payment_terms_combo.setStyleSheet(self.get_input_style())
        layout.addWidget(self.payment_terms_combo, 0, 1)
        
        # الملاحظات
        layout.addWidget(QLabel("الملاحظات:"), 1, 0)
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setStyleSheet(self.get_input_style())
        layout.addWidget(self.notes_input, 1, 1, 1, 3)
        
        return group
    
    def create_buttons(self):
        """إنشاء أزرار الحوار"""
        layout = QHBoxLayout()
        layout.addStretch()
        
        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: #f44336;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }
            QPushButton:hover {
                background: #d32f2f;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        # زر الحفظ
        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet(f"""
            QPushButton {{
                background: {AppStyles.PRIMARY_COLOR};
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background: {AppStyles.HIGHLIGHT_COLOR};
            }}
        """)
        save_btn.clicked.connect(self.accept)
        
        layout.addWidget(cancel_btn)
        layout.addWidget(save_btn)
        
        return layout
    
    def get_input_style(self):
        """الحصول على نمط عناصر الإدخال"""
        return f"""
            QLineEdit, QComboBox, QTextEdit {{
                padding: 10px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 14px;
                background: white;
            }}
            QLineEdit:focus, QComboBox:focus, QTextEdit:focus {{
                border-color: {AppStyles.PRIMARY_COLOR};
            }}
        """
    
    def load_supplier_data(self):
        """تحميل بيانات المورد للتعديل"""
        self.name_input.setText(self.supplier_data["name"])
        self.contact_person_input.setText(self.supplier_data["contact_person"])
        self.tax_number_input.setText(self.supplier_data["tax_number"])
        self.status_combo.setCurrentText(self.supplier_data["status"])
        self.phone_input.setText(self.supplier_data["phone"])
        self.email_input.setText(self.supplier_data["email"])
        self.city_input.setText(self.supplier_data["city"])
        self.country_input.setText(self.supplier_data["country"])
        self.address_input.setPlainText(self.supplier_data["address"])
        self.payment_terms_combo.setCurrentText(self.supplier_data["payment_terms"])
        self.notes_input.setPlainText(self.supplier_data["notes"])
    
    def get_supplier_data(self):
        """الحصول على بيانات المورد"""
        return {
            "name": self.name_input.text().strip(),
            "contact_person": self.contact_person_input.text().strip(),
            "tax_number": self.tax_number_input.text().strip(),
            "status": self.status_combo.currentText(),
            "phone": self.phone_input.text().strip(),
            "email": self.email_input.text().strip(),
            "city": self.city_input.text().strip(),
            "country": self.country_input.text().strip(),
            "address": self.address_input.toPlainText().strip(),
            "payment_terms": self.payment_terms_combo.currentText(),
            "notes": self.notes_input.toPlainText().strip()
        }