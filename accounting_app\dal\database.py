"""
وحدة قاعدة البيانات - تتعامل مع إنشاء وإدارة قاعدة البيانات
"""
import os
import sqlite3
from sqlite3 import Error


class DatabaseManager:
    """مدير قاعدة البيانات - يتعامل مع جميع عمليات قاعدة البيانات"""
    
    def __init__(self, db_file="accounting_db.sqlite"):
        """
        تهيئة مدير قاعدة البيانات
        :param db_file: مسار ملف قاعدة البيانات
        """
        # الحصول على المسار الكامل لملف قاعدة البيانات
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.db_path = os.path.join(base_dir, db_file)
        self.conn = None
    
    def connect(self):
        """إنشاء اتصال بقاعدة البيانات SQLite"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            return self.conn
        except Error as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return None
    
    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.conn:
            self.conn.close()
    
    def execute_query(self, query, params=()):
        """
        تنفيذ استعلام SQL
        :param query: استعلام SQL
        :param params: معلمات الاستعلام
        :return: نتيجة الاستعلام
        """
        try:
            cursor = self.conn.cursor()
            cursor.execute(query, params)
            self.conn.commit()
            return cursor
        except Error as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return None
    
    def fetch_all(self, query, params=()):
        """
        جلب جميع النتائج من استعلام
        :param query: استعلام SQL
        :param params: معلمات الاستعلام
        :return: قائمة بجميع النتائج
        """
        cursor = self.execute_query(query, params)
        if cursor:
            return cursor.fetchall()
        return []
    
    def fetch_one(self, query, params=()):
        """
        جلب نتيجة واحدة من استعلام
        :param query: استعلام SQL
        :param params: معلمات الاستعلام
        :return: نتيجة واحدة
        """
        cursor = self.execute_query(query, params)
        if cursor:
            return cursor.fetchone()
        return None
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات إذا لم تكن موجودة"""
        # جدول المستخدمين
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL UNIQUE,
            password TEXT NOT NULL,
            full_name TEXT,
            email TEXT,
            phone TEXT,
            role TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # جدول المنتجات
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT NOT NULL UNIQUE,
            name TEXT NOT NULL,
            description TEXT,
            category TEXT,
            purchase_price REAL NOT NULL,
            selling_price REAL NOT NULL,
            quantity INTEGER DEFAULT 0,
            min_quantity INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # جدول العملاء
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            address TEXT,
            balance REAL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # جدول الموردين
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            address TEXT,
            balance REAL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # جدول فواتير البيع
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS sales_invoices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_number TEXT NOT NULL UNIQUE,
            customer_id INTEGER,
            total_amount REAL NOT NULL,
            discount REAL DEFAULT 0,
            tax REAL DEFAULT 0,
            net_amount REAL NOT NULL,
            paid_amount REAL NOT NULL,
            remaining_amount REAL NOT NULL,
            status TEXT NOT NULL,
            notes TEXT,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id),
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
        ''')
        
        # جدول تفاصيل فواتير البيع
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS sales_invoice_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL,
            price REAL NOT NULL,
            total REAL NOT NULL,
            FOREIGN KEY (invoice_id) REFERENCES sales_invoices (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
        ''')
        
        # جدول فواتير الشراء
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS purchase_invoices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_number TEXT NOT NULL UNIQUE,
            supplier_id INTEGER,
            total_amount REAL NOT NULL,
            discount REAL DEFAULT 0,
            tax REAL DEFAULT 0,
            net_amount REAL NOT NULL,
            paid_amount REAL NOT NULL,
            remaining_amount REAL NOT NULL,
            status TEXT NOT NULL,
            notes TEXT,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
        ''')
        
        # جدول تفاصيل فواتير الشراء
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS purchase_invoice_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL,
            price REAL NOT NULL,
            total REAL NOT NULL,
            FOREIGN KEY (invoice_id) REFERENCES purchase_invoices (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
        ''')
        
        # جدول الإعدادات
        self.execute_query('''
        CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT NOT NULL UNIQUE,
            value TEXT,
            description TEXT
        )
        ''')
        
        # إدخال مستخدم افتراضي (admin/admin)
        self.execute_query('''
        INSERT OR IGNORE INTO users (username, password, full_name, role)
        VALUES (?, ?, ?, ?)
        ''', ('admin', 'admin', 'مدير النظام', 'admin'))
        
        # إدخال إعدادات افتراضية
        default_settings = [
            ('company_name', 'نظام المحاسبة', 'اسم الشركة'),
            ('company_phone', '0123456789', 'رقم هاتف الشركة'),
            ('company_address', 'العنوان', 'عنوان الشركة'),
            ('language', 'ar', 'لغة النظام (ar/en)'),
            ('tax_rate', '14', 'نسبة الضريبة')
        ]
        
        for setting in default_settings:
            self.execute_query('''
            INSERT OR IGNORE INTO settings (key, value, description)
            VALUES (?, ?, ?)
            ''', setting)