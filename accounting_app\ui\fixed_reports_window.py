"""
صفحة التقارير المحسنة والواضحة
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                              QLabel, QFrame, QTableWidget, QTableWidgetItem, 
                              QPushButton, QDateEdit, QComboBox, QGroupBox,
                              QScrollArea, QGridLayout, QHeaderView, QSplitter)
from PySide6.QtCore import Qt, QDate, QTimer
from PySide6.QtGui import QFont, QColor
from accounting_app.ui.styles import AppStyles
from accounting_app.ui.enhanced_charts import <PERSON>hancedPieChart, EnhancedBarChart, EnhancedLineChart
import random
from datetime import datetime, timedelta


class ClearReportsWindow(QWidget):
    """نافذة التقارير المحسنة والواضحة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("📊 التقارير والإحصائيات")
        self.setMinimumSize(1200, 800)
        self.init_ui()
        self.load_sample_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # إنشاء الرأس
        header = self.create_clear_header()
        main_layout.addWidget(header)
        
        # إنشاء التبويبات
        tabs = self.create_clear_tabs()
        main_layout.addWidget(tabs)
    
    def create_clear_header(self):
        """إنشاء رأس واضح للصفحة"""
        header_frame = QFrame()
        header_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                border-radius: 15px;
                padding: 25px;
                color: white;
                min-height: 120px;
            }}
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # المعلومات النصية
        text_section = QWidget()
        text_layout = QVBoxLayout(text_section)
        
        title = QLabel("📊 تقارير شاملة ومفصلة")
        title.setStyleSheet("font-size: 28px; font-weight: bold; color: white; margin-bottom: 10px;")
        
        subtitle = QLabel("تقارير واضحة ومفهومة لجميع عمليات النشاط التجاري")
        subtitle.setStyleSheet("font-size: 16px; color: white; opacity: 0.9;")
        
        # معلومات سريعة
        info_layout = QHBoxLayout()
        info_widgets = [
            self.create_info_card("إجمالي التقارير", "4", "📋"),
            self.create_info_card("البيانات محدثة", "الآن", "🔄"),
            self.create_info_card("الفترة", "شهر كامل", "📅")
        ]
        
        for widget in info_widgets:
            info_layout.addWidget(widget)
        info_layout.addStretch()
        
        text_layout.addWidget(title)
        text_layout.addWidget(subtitle)
        text_layout.addSpacing(10)
        text_layout.addLayout(info_layout)
        
        # الأيقونة الجانبية
        icon_label = QLabel("📊")
        icon_label.setStyleSheet("""
            font-size: 80px;
            color: white;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 40px;
            padding: 20px;
            min-width: 120px;
            max-width: 120px;
            min-height: 120px;
            max-height: 120px;
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(text_section, 8)
        layout.addWidget(icon_label, 2)
        
        return header_frame
    
    def create_info_card(self, title, value, icon):
        """إنشاء بطاقة معلومات صغيرة"""
        card = QWidget()
        card.setStyleSheet("""
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 10px 15px;
            margin: 5px;
        """)
        
        layout = QHBoxLayout(card)
        layout.setContentsMargins(10, 8, 10, 8)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 20px; color: white;")
        
        text_widget = QWidget()
        text_layout = QVBoxLayout(text_widget)
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(2)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 12px; color: white; opacity: 0.8;")
        
        text_layout.addWidget(value_label)
        text_layout.addWidget(title_label)
        
        layout.addWidget(icon_label)
        layout.addWidget(text_widget)
        
        return card
    
    def create_clear_tabs(self):
        """إنشاء تبويبات واضحة"""
        tabs = QTabWidget()
        tabs.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                background: white;
                padding: 15px;
            }}
            QTabBar::tab {{
                background: #f5f5f5;
                color: #333;
                padding: 15px 25px;
                margin: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                min-width: 150px;
                border: 2px solid #e0e0e0;
            }}
            QTabBar::tab:selected {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border-bottom: none;
                font-weight: bold;
            }}
            QTabBar::tab:hover:!selected {{
                background: #e8f5e8;
                color: #2e7d32;
            }}
        """)
        
        # تبويبات التقارير
        tabs.addTab(self.create_sales_report_tab(), "💰 تقرير المبيعات")
        tabs.addTab(self.create_customers_report_tab(), "👥 تقرير العملاء") 
        tabs.addTab(self.create_inventory_report_tab(), "📦 تقرير المخزون")
        tabs.addTab(self.create_financial_report_tab(), "💼 التقرير المالي")
        
        return tabs
    
    def create_sales_report_tab(self):
        """تبويب تقرير المبيعات الواضح"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        # شريط التحكم
        control_bar = self.create_control_bar("المبيعات")
        layout.addWidget(control_bar)
        
        # قسم الإحصائيات
        stats_section = self.create_stats_section()
        layout.addWidget(stats_section)
        
        # قسم الرسم البياني والجدول
        content_splitter = QSplitter(Qt.Horizontal)
        
        # الرسم البياني
        chart_frame = self.create_chart_section("مبيعات الأشهر الستة الماضية", "bar")
        content_splitter.addWidget(chart_frame)
        
        # الجدول
        table_frame = self.create_sales_table()
        content_splitter.addWidget(table_frame)
        
        content_splitter.setSizes([500, 500])
        layout.addWidget(content_splitter)
        
        return tab
    
    def create_control_bar(self, report_type):
        """إنشاء شريط التحكم الواضح"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # تسمية القسم
        title = QLabel(f"🎯 تحكم في تقرير {report_type}")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #333;")
        
        # تحديد الفترة
        period_label = QLabel("📅 الفترة:")
        period_label.setStyleSheet("font-size: 14px; font-weight: 600; color: #555;")
        
        start_date = QDateEdit()
        start_date.setDate(QDate.currentDate().addMonths(-1))
        start_date.setStyleSheet("""
            QDateEdit {
                padding: 8px 12px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 14px;
                min-width: 120px;
                background: white;
            }
            QDateEdit:focus {
                border-color: #4CAF50;
            }
        """)
        
        to_label = QLabel("إلى")
        to_label.setStyleSheet("font-size: 14px; color: #666; margin: 0 10px;")
        
        end_date = QDateEdit()
        end_date.setDate(QDate.currentDate())
        end_date.setStyleSheet(start_date.styleSheet())
        
        # زر التحديث
        refresh_btn = QPushButton("🔄 تحديث التقرير")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 600;
                min-width: 140px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #3d8b40);
            }
            QPushButton:pressed {
                background: #3d8b40;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_report)
        
        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة")
        print_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976D2, stop:1 #1565C0);
            }
        """)
        print_btn.clicked.connect(self.print_report)
        
        layout.addWidget(title)
        layout.addStretch()
        layout.addWidget(period_label)
        layout.addWidget(start_date)
        layout.addWidget(to_label)
        layout.addWidget(end_date)
        layout.addSpacing(15)
        layout.addWidget(refresh_btn)
        layout.addWidget(print_btn)
        
        return frame
    
    def create_stats_section(self):
        """إنشاء قسم الإحصائيات الواضح"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e3f2fd;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        layout = QGridLayout(frame)
        layout.setSpacing(15)
        
        # بطاقات الإحصائيات
        stats = [
            ("💰 إجمالي المبيعات", "485,750 ج.م", "#4CAF50", "⬆️ +12.5%"),
            ("📄 عدد الفواتير", "156 فاتورة", "#2196F3", "⬆️ +5.8%"),
            ("💵 متوسط الفاتورة", "3,114 ج.م", "#FF9800", "⬆️ +3.2%"),
            ("👤 أفضل عميل", "أحمد محمد علي", "#9C27B0", "95,750 ج.م")
        ]
        
        for i, (title, value, color, change) in enumerate(stats):
            card = self.create_large_stat_card(title, value, color, change)
            layout.addWidget(card, 0, i)
        
        return frame
    
    def create_large_stat_card(self, title, value, color, change):
        """إنشاء بطاقة إحصائية كبيرة وواضحة"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}, stop:1 {color}dd);
                border-radius: 12px;
                padding: 20px;
                color: white;
                min-height: 120px;
                max-height: 120px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setSpacing(8)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 14px;
            font-weight: 600;
            color: white;
            opacity: 0.9;
        """)
        
        # القيمة الرئيسية
        value_label = QLabel(value)
        value_label.setStyleSheet("""
            font-size: 22px;
            font-weight: bold;
            color: white;
            margin: 5px 0;
        """)
        
        # التغيير
        change_label = QLabel(change)
        change_label.setStyleSheet("""
            font-size: 12px;
            color: white;
            opacity: 0.8;
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 12px;
        """)
        change_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        layout.addStretch()
        layout.addWidget(change_label)
        
        return card
    
    def create_chart_section(self, title, chart_type):
        """إنشاء قسم الرسم البياني الواضح"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # عنوان القسم
        title_label = QLabel(f"📊 {title}")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        # الرسم البياني
        if chart_type == "bar":
            chart = EnhancedBarChart("المبيعات الشهرية", self.get_monthly_sales_data())
        elif chart_type == "pie":
            chart = EnhancedPieChart("توزيع المبيعات", self.get_sales_distribution_data())
        else:
            chart = EnhancedLineChart("نمو المبيعات", self.get_sales_growth_data())
        
        chart.setMinimumHeight(300)
        
        layout.addWidget(title_label)
        layout.addWidget(chart)
        
        return frame
    
    def create_sales_table(self):
        """إنشاء جدول المبيعات الواضح"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # عنوان الجدول
        title_label = QLabel("📋 تفاصيل المبيعات")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        # الجدول
        table = QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(["التاريخ", "رقم الفاتورة", "العميل", "المبلغ", "الحالة"])
        
        # تنسيق الجدول
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #e3f2fd;
                font-size: 13px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f5f5f5, stop:1 #e0e0e0);
                color: #333;
                padding: 12px 8px;
                border: 1px solid #ddd;
                font-weight: bold;
                font-size: 14px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
        
        # إضافة بيانات وهمية واضحة
        sales_data = [
            ["2024-01-20", "INV-001", "أحمد محمد علي", "15,750 ج.م", "مدفوعة"],
            ["2024-01-19", "INV-002", "فاطمة أحمد", "12,300 ج.م", "معلقة"],
            ["2024-01-18", "INV-003", "محمد حسن", "8,900 ج.م", "مدفوعة"],
            ["2024-01-17", "INV-004", "سارة محمود", "22,150 ج.م", "مدفوعة"],
            ["2024-01-16", "INV-005", "علي عبدالله", "5,680 ج.م", "ملغاة"],
            ["2024-01-15", "INV-006", "نورا السيد", "18,920 ج.م", "مدفوعة"],
            ["2024-01-14", "INV-007", "خالد أحمد", "9,750 ج.م", "معلقة"],
            ["2024-01-13", "INV-008", "مريم يوسف", "14,200 ج.م", "مدفوعة"]
        ]
        
        table.setRowCount(len(sales_data))
        
        for row, row_data in enumerate(sales_data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                
                # تلوين خلايا الحالة
                if col == 4:  # عمود الحالة
                    if cell_data == "مدفوعة":
                        item.setBackground(QColor("#e8f5e8"))
                        item.setForeground(QColor("#2e7d32"))
                    elif cell_data == "معلقة":
                        item.setBackground(QColor("#fff3e0"))
                        item.setForeground(QColor("#ef6c00"))
                    elif cell_data == "ملغاة":
                        item.setBackground(QColor("#ffebee"))
                        item.setForeground(QColor("#c62828"))
                
                table.setItem(row, col, item)
        
        # ضبط عرض الأعمدة
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # رقم الفاتورة
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # العميل
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # الحالة
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setMinimumHeight(300)
        
        layout.addWidget(title_label)
        layout.addWidget(table)
        
        return frame
    
    def create_customers_report_tab(self):
        """تبويب تقرير العملاء"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        control_bar = self.create_control_bar("العملاء")
        layout.addWidget(control_bar)
        
        # إحصائيات العملاء
        customers_stats = self.create_customers_stats()
        layout.addWidget(customers_stats)
        
        # الرسم البياني والجدول
        content_splitter = QSplitter(Qt.Horizontal)
        
        chart_frame = self.create_chart_section("أفضل 5 عملاء", "pie")
        table_frame = self.create_customers_table()
        
        content_splitter.addWidget(chart_frame)
        content_splitter.addWidget(table_frame)
        content_splitter.setSizes([400, 600])
        
        layout.addWidget(content_splitter)
        
        return tab
    
    def create_customers_stats(self):
        """إحصائيات العملاء"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e3f2fd;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        layout = QGridLayout(frame)
        layout.setSpacing(15)
        
        stats = [
            ("👥 العملاء الكل", "248 عميل", "#4CAF50", "⬆️ +18"),
            ("🔥 العملاء النشطين", "186 عميل", "#2196F3", "⬆️ +12"),
            ("⭐ عملاء VIP", "42 عميل", "#FF9800", "⬆️ +5"),
            ("💎 أفضل عميل", "أحمد محمد", "#9C27B0", "95,750 ج.م")
        ]
        
        for i, (title, value, color, change) in enumerate(stats):
            card = self.create_large_stat_card(title, value, color, change)
            layout.addWidget(card, 0, i)
        
        return frame
    
    def create_customers_table(self):
        """جدول العملاء"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        title_label = QLabel("👥 قائمة أفضل العملاء")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["العميل", "إجمالي المشتريات", "عدد الفواتير", "التصنيف"])
        
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #e3f2fd;
                font-size: 13px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f5f5f5, stop:1 #e0e0e0);
                color: #333;
                padding: 12px 8px;
                border: 1px solid #ddd;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        
        customers_data = [
            ["أحمد محمد علي", "95,750 ج.م", "23", "⭐ VIP"],
            ["فاطمة أحمد", "76,200 ج.م", "18", "🔥 نشط"],
            ["محمد حسن", "62,800 ج.م", "15", "🔥 نشط"],
            ["سارة محمود", "48,500 ج.م", "12", "👤 عادي"],
            ["علي عبدالله", "42,300 ج.م", "10", "👤 عادي"],
            ["نورا السيد", "38,900 ج.م", "9", "👤 عادي"]
        ]
        
        table.setRowCount(len(customers_data))
        
        for row, row_data in enumerate(customers_data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                if col == 3:  # عمود التصنيف
                    if "VIP" in cell_data:
                        item.setBackground(QColor("#fff3e0"))
                        item.setForeground(QColor("#ef6c00"))
                    elif "نشط" in cell_data:
                        item.setBackground(QColor("#e8f5e8"))
                        item.setForeground(QColor("#2e7d32"))
                table.setItem(row, col, item)
        
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setMinimumHeight(300)
        
        layout.addWidget(title_label)
        layout.addWidget(table)
        
        return frame
    
    def create_inventory_report_tab(self):
        """تبويب تقرير المخزون"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        control_bar = self.create_control_bar("المخزون")
        layout.addWidget(control_bar)
        
        # إحصائيات المخزون
        inventory_stats = self.create_inventory_stats()
        layout.addWidget(inventory_stats)
        
        # الرسم البياني والجدول
        content_splitter = QSplitter(Qt.Horizontal)
        
        chart_frame = self.create_chart_section("حالة المخزون", "pie")
        table_frame = self.create_inventory_table()
        
        content_splitter.addWidget(chart_frame)
        content_splitter.addWidget(table_frame)
        content_splitter.setSizes([400, 600])
        
        layout.addWidget(content_splitter)
        
        return tab
    
    def create_inventory_stats(self):
        """إحصائيات المخزون"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e3f2fd;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        layout = QGridLayout(frame)
        layout.setSpacing(15)
        
        stats = [
            ("📦 إجمالي المنتجات", "1,248 منتج", "#4CAF50", "⬆️ +32"),
            ("⚠️ منخفض المخزون", "18 منتج", "#f44336", "🔔 تنبيه"),
            ("💰 قيمة المخزون", "2,450,000 ج.م", "#2196F3", "⬆️ +8.5%"),
            ("🔄 معدل الدوران", "4.2 مرة/شهر", "#FF9800", "⬆️ +0.3")
        ]
        
        for i, (title, value, color, change) in enumerate(stats):
            card = self.create_large_stat_card(title, value, color, change)
            layout.addWidget(card, 0, i)
        
        return frame
    
    def create_inventory_table(self):
        """جدول المخزون"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        title_label = QLabel("📦 حالة المخزون التفصيلية")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        table = QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(["المنتج", "الكمية المتاحة", "الحد الأدنى", "السعر", "الحالة"])
        
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #e3f2fd;
                font-size: 13px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f5f5f5, stop:1 #e0e0e0);
                color: #333;
                padding: 12px 8px;
                border: 1px solid #ddd;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        
        inventory_data = [
            ["لابتوب ديل XPS", "45", "10", "25,000 ج.م", "🟢 طبيعي"],
            ["ماوس لوجيتك", "8", "15", "450 ج.م", "🔴 منخفض"],
            ["كيبورد ميكانيكي", "23", "5", "1,200 ج.م", "🟢 طبيعي"],
            ["شاشة سامسونج 24\"", "12", "3", "3,500 ج.م", "🟢 طبيعي"],
            ["سماعات بلوتوث", "156", "20", "350 ج.م", "🟢 طبيعي"],
            ["كابل USB-C", "7", "25", "85 ج.م", "🔴 منخفض"],
            ["حقيبة لابتوب", "34", "10", "250 ج.م", "🟢 طبيعي"],
            ["بطارية خارجية", "89", "15", "180 ج.م", "🟢 طبيعي"]
        ]
        
        table.setRowCount(len(inventory_data))
        
        for row, row_data in enumerate(inventory_data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                if col == 4:  # عمود الحالة
                    if "منخفض" in cell_data:
                        item.setBackground(QColor("#ffebee"))
                        item.setForeground(QColor("#c62828"))
                    elif "طبيعي" in cell_data:
                        item.setBackground(QColor("#e8f5e8"))
                        item.setForeground(QColor("#2e7d32"))
                table.setItem(row, col, item)
        
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setMinimumHeight(300)
        
        layout.addWidget(title_label)
        layout.addWidget(table)
        
        return frame
    
    def create_financial_report_tab(self):
        """تبويب التقرير المالي"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        control_bar = self.create_control_bar("المالي")
        layout.addWidget(control_bar)
        
        # إحصائيات مالية
        financial_stats = self.create_financial_stats()
        layout.addWidget(financial_stats)
        
        # الرسم البياني والجدول
        content_splitter = QSplitter(Qt.Horizontal)
        
        chart_frame = self.create_chart_section("التدفق النقدي الشهري", "line")
        table_frame = self.create_financial_table()
        
        content_splitter.addWidget(chart_frame)
        content_splitter.addWidget(table_frame)
        content_splitter.setSizes([500, 500])
        
        layout.addWidget(content_splitter)
        
        return tab
    
    def create_financial_stats(self):
        """إحصائيات مالية"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e3f2fd;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        layout = QGridLayout(frame)
        layout.setSpacing(15)
        
        stats = [
            ("💰 إجمالي الإيرادات", "1,850,000 ج.م", "#4CAF50", "⬆️ +15.2%"),
            ("💸 إجمالي المصروفات", "1,230,000 ج.م", "#f44336", "⬆️ +8.7%"),
            ("💵 صافي الربح", "620,000 ج.م", "#2196F3", "⬆️ +28.5%"),
            ("📊 هامش الربح", "33.5%", "#FF9800", "⬆️ +2.1%")
        ]
        
        for i, (title, value, color, change) in enumerate(stats):
            card = self.create_large_stat_card(title, value, color, change)
            layout.addWidget(card, 0, i)
        
        return frame
    
    def create_financial_table(self):
        """جدول مالي"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        title_label = QLabel("💼 ملخص الحسابات الشهرية")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["الشهر", "الإيرادات", "المصروفات", "صافي الربح"])
        
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #e3f2fd;
                font-size: 13px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f5f5f5, stop:1 #e0e0e0);
                color: #333;
                padding: 12px 8px;
                border: 1px solid #ddd;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        
        financial_data = [
            ["يناير 2024", "320,000 ج.م", "210,000 ج.م", "110,000 ج.م"],
            ["فبراير 2024", "280,000 ج.م", "195,000 ج.م", "85,000 ج.م"],
            ["مارس 2024", "350,000 ج.م", "225,000 ج.م", "125,000 ج.م"],
            ["أبريل 2024", "290,000 ج.م", "200,000 ج.م", "90,000 ج.م"],
            ["مايو 2024", "380,000 ج.م", "245,000 ج.م", "135,000 ج.م"],
            ["يونيو 2024", "230,000 ج.م", "155,000 ج.م", "75,000 ج.م"]
        ]
        
        table.setRowCount(len(financial_data))
        
        for row, row_data in enumerate(financial_data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                if col == 3:  # عمود صافي الربح
                    profit = int(cell_data.replace(",", "").replace(" ج.م", ""))
                    if profit > 100000:
                        item.setBackground(QColor("#e8f5e8"))
                        item.setForeground(QColor("#2e7d32"))
                    elif profit < 80000:
                        item.setBackground(QColor("#fff3e0"))
                        item.setForeground(QColor("#ef6c00"))
                table.setItem(row, col, item)
        
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setMinimumHeight(300)
        
        layout.addWidget(title_label)
        layout.addWidget(table)
        
        return frame
    
    def get_monthly_sales_data(self):
        """بيانات المبيعات الشهرية"""
        return [
            ("يناير", 320000), ("فبراير", 280000), ("مارس", 350000),
            ("أبريل", 290000), ("مايو", 380000), ("يونيو", 230000)
        ]
    
    def get_sales_distribution_data(self):
        """بيانات توزيع المبيعات"""
        return [
            ("إلكترونيات", 35, "#FF6B6B"),
            ("ملابس", 25, "#4ECDC4"), 
            ("أدوات منزلية", 20, "#45B7D1"),
            ("كتب", 15, "#96CEB4"),
            ("أخرى", 5, "#FFEAA7")
        ]
    
    def get_sales_growth_data(self):
        """بيانات نمو المبيعات"""
        return [
            (1, 250000), (2, 280000), (3, 320000), (4, 350000),
            (5, 290000), (6, 380000), (7, 410000), (8, 450000)
        ]
    
    def refresh_report(self):
        """تحديث التقرير"""
        print("تم تحديث التقرير بنجاح!")
    
    def print_report(self):
        """طباعة التقرير"""
        print("جاري إعداد الطباعة...")
    
    def load_sample_data(self):
        """تحميل البيانات الوهمية"""
        print("تم تحميل بيانات التقارير بنجاح")


class ClearReportsWindow(QWidget):
    """نافذة التقارير المحسنة والواضحة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("📊 التقارير والإحصائيات")
        self.setMinimumSize(1200, 800)
        self.init_ui()
        self.load_sample_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # إنشاء الرأس
        header = self.create_clear_header()
        main_layout.addWidget(header)
        
        # إنشاء التبويبات
        tabs = self.create_clear_tabs()
        main_layout.addWidget(tabs)
    
    def create_clear_header(self):
        """إنشاء رأس واضح للصفحة"""
        header_frame = QFrame()
        header_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                border-radius: 15px;
                padding: 25px;
                color: white;
                min-height: 120px;
            }}
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # المعلومات النصية
        text_section = QWidget()
        text_layout = QVBoxLayout(text_section)
        
        title = QLabel("📊 تقارير شاملة ومفصلة")
        title.setStyleSheet("font-size: 28px; font-weight: bold; color: white; margin-bottom: 10px;")
        
        subtitle = QLabel("تقارير واضحة ومفهومة لجميع عمليات النشاط التجاري")
        subtitle.setStyleSheet("font-size: 16px; color: white; opacity: 0.9;")
        
        # معلومات سريعة
        info_layout = QHBoxLayout()
        info_widgets = [
            self.create_info_card("إجمالي التقارير", "4", "📋"),
            self.create_info_card("البيانات محدثة", "الآن", "🔄"),
            self.create_info_card("الفترة", "شهر كامل", "📅")
        ]
        
        for widget in info_widgets:
            info_layout.addWidget(widget)
        info_layout.addStretch()
        
        text_layout.addWidget(title)
        text_layout.addWidget(subtitle)
        text_layout.addSpacing(10)
        text_layout.addLayout(info_layout)
        
        # الأيقونة الجانبية
        icon_label = QLabel("📊")
        icon_label.setStyleSheet("""
            font-size: 80px;
            color: white;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 40px;
            padding: 20px;
            min-width: 120px;
            max-width: 120px;
            min-height: 120px;
            max-height: 120px;
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(text_section, 8)
        layout.addWidget(icon_label, 2)
        
        return header_frame
    
    def create_info_card(self, title, value, icon):
        """إنشاء بطاقة معلومات صغيرة"""
        card = QWidget()
        card.setStyleSheet("""
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 10px 15px;
            margin: 5px;
        """)
        
        layout = QHBoxLayout(card)
        layout.setContentsMargins(10, 8, 10, 8)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 20px; color: white;")
        
        text_widget = QWidget()
        text_layout = QVBoxLayout(text_widget)
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(2)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 12px; color: white; opacity: 0.8;")
        
        text_layout.addWidget(value_label)
        text_layout.addWidget(title_label)
        
        layout.addWidget(icon_label)
        layout.addWidget(text_widget)
        
        return card
    
    def create_clear_tabs(self):
        """إنشاء تبويبات واضحة"""
        tabs = QTabWidget()
        tabs.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                background: white;
                padding: 15px;
            }}
            QTabBar::tab {{
                background: #f5f5f5;
                color: #333;
                padding: 15px 25px;
                margin: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                min-width: 150px;
                border: 2px solid #e0e0e0;
            }}
            QTabBar::tab:selected {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border-bottom: none;
                font-weight: bold;
            }}
            QTabBar::tab:hover:!selected {{
                background: #e8f5e8;
                color: #2e7d32;
            }}
        """)
        
        # تبويبات التقارير
        tabs.addTab(self.create_sales_report_tab(), "💰 تقرير المبيعات")
        tabs.addTab(self.create_customers_report_tab(), "👥 تقرير العملاء") 
        tabs.addTab(self.create_inventory_report_tab(), "📦 تقرير المخزون")
        tabs.addTab(self.create_financial_report_tab(), "💼 التقرير المالي")
        
        return tabs
    
    def create_sales_report_tab(self):
        """تبويب تقرير المبيعات الواضح"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        # شريط التحكم
        control_bar = self.create_control_bar("المبيعات")
        layout.addWidget(control_bar)
        
        # قسم الإحصائيات
        stats_section = self.create_stats_section()
        layout.addWidget(stats_section)
        
        # قسم الرسم البياني والجدول
        content_splitter = QSplitter(Qt.Horizontal)
        
        # الرسم البياني
        chart_frame = self.create_chart_section("مبيعات الأشهر الستة الماضية", "bar")
        content_splitter.addWidget(chart_frame)
        
        # الجدول
        table_frame = self.create_sales_table()
        content_splitter.addWidget(table_frame)
        
        content_splitter.setSizes([500, 500])
        layout.addWidget(content_splitter)
        
        return tab
    
    def create_control_bar(self, report_type):
        """إنشاء شريط التحكم الواضح"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # تسمية القسم
        title = QLabel(f"🎯 تحكم في تقرير {report_type}")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #333;")
        
        # تحديد الفترة
        period_label = QLabel("📅 الفترة:")
        period_label.setStyleSheet("font-size: 14px; font-weight: 600; color: #555;")
        
        start_date = QDateEdit()
        start_date.setDate(QDate.currentDate().addMonths(-1))
        start_date.setStyleSheet("""
            QDateEdit {
                padding: 8px 12px;
                border: 2px solid #ddd;
                border-radius: 6px;
                font-size: 14px;
                min-width: 120px;
                background: white;
            }
            QDateEdit:focus {
                border-color: #4CAF50;
            }
        """)
        
        to_label = QLabel("إلى")
        to_label.setStyleSheet("font-size: 14px; color: #666; margin: 0 10px;")
        
        end_date = QDateEdit()
        end_date.setDate(QDate.currentDate())
        end_date.setStyleSheet(start_date.styleSheet())
        
        # زر التحديث
        refresh_btn = QPushButton("🔄 تحديث التقرير")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 600;
                min-width: 140px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #3d8b40);
            }
            QPushButton:pressed {
                background: #3d8b40;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_report)
        
        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة")
        print_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 600;
                min-width: 100px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976D2, stop:1 #1565C0);
            }
        """)
        print_btn.clicked.connect(self.print_report)
        
        layout.addWidget(title)
        layout.addStretch()
        layout.addWidget(period_label)
        layout.addWidget(start_date)
        layout.addWidget(to_label)
        layout.addWidget(end_date)
        layout.addSpacing(15)
        layout.addWidget(refresh_btn)
        layout.addWidget(print_btn)
        
        return frame
    
    def create_stats_section(self):
        """إنشاء قسم الإحصائيات الواضح"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e3f2fd;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        layout = QGridLayout(frame)
        layout.setSpacing(15)
        
        # بطاقات الإحصائيات
        stats = [
            ("💰 إجمالي المبيعات", "485,750 ج.م", "#4CAF50", "⬆️ +12.5%"),
            ("📄 عدد الفواتير", "156 فاتورة", "#2196F3", "⬆️ +5.8%"),
            ("💵 متوسط الفاتورة", "3,114 ج.م", "#FF9800", "⬆️ +3.2%"),
            ("👤 أفضل عميل", "أحمد محمد علي", "#9C27B0", "95,750 ج.م")
        ]
        
        for i, (title, value, color, change) in enumerate(stats):
            card = self.create_large_stat_card(title, value, color, change)
            layout.addWidget(card, 0, i)
        
        return frame
    
    def create_large_stat_card(self, title, value, color, change):
        """إنشاء بطاقة إحصائية كبيرة وواضحة"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}, stop:1 {color}dd);
                border-radius: 12px;
                padding: 20px;
                color: white;
                min-height: 120px;
                max-height: 120px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setSpacing(8)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 14px;
            font-weight: 600;
            color: white;
            opacity: 0.9;
        """)
        
        # القيمة الرئيسية
        value_label = QLabel(value)
        value_label.setStyleSheet("""
            font-size: 22px;
            font-weight: bold;
            color: white;
            margin: 5px 0;
        """)
        
        # التغيير
        change_label = QLabel(change)
        change_label.setStyleSheet("""
            font-size: 12px;
            color: white;
            opacity: 0.8;
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 12px;
        """)
        change_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        layout.addStretch()
        layout.addWidget(change_label)
        
        return card
    
    def create_chart_section(self, title, chart_type):
        """إنشاء قسم الرسم البياني الواضح"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # عنوان القسم
        title_label = QLabel(f"📊 {title}")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        # الرسم البياني
        if chart_type == "bar":
            chart = EnhancedBarChart("المبيعات الشهرية", self.get_monthly_sales_data())
        elif chart_type == "pie":
            chart = EnhancedPieChart("توزيع المبيعات", self.get_sales_distribution_data())
        else:
            chart = EnhancedLineChart("نمو المبيعات", self.get_sales_growth_data())
        
        chart.setMinimumHeight(300)
        
        layout.addWidget(title_label)
        layout.addWidget(chart)
        
        return frame
    
    def create_sales_table(self):
        """إنشاء جدول المبيعات الواضح"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # عنوان الجدول
        title_label = QLabel("📋 تفاصيل المبيعات")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        # الجدول
        table = QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(["التاريخ", "رقم الفاتورة", "العميل", "المبلغ", "الحالة"])
        
        # تنسيق الجدول
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #e3f2fd;
                font-size: 13px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f5f5f5, stop:1 #e0e0e0);
                color: #333;
                padding: 12px 8px;
                border: 1px solid #ddd;
                font-weight: bold;
                font-size: 14px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
        
        # إضافة بيانات وهمية واضحة
        sales_data = [
            ["2024-01-20", "INV-001", "أحمد محمد علي", "15,750 ج.م", "مدفوعة"],
            ["2024-01-19", "INV-002", "فاطمة أحمد", "12,300 ج.م", "معلقة"],
            ["2024-01-18", "INV-003", "محمد حسن", "8,900 ج.م", "مدفوعة"],
            ["2024-01-17", "INV-004", "سارة محمود", "22,150 ج.م", "مدفوعة"],
            ["2024-01-16", "INV-005", "علي عبدالله", "5,680 ج.م", "ملغاة"],
            ["2024-01-15", "INV-006", "نورا السيد", "18,920 ج.م", "مدفوعة"],
            ["2024-01-14", "INV-007", "خالد أحمد", "9,750 ج.م", "معلقة"],
            ["2024-01-13", "INV-008", "مريم يوسف", "14,200 ج.م", "مدفوعة"]
        ]
        
        table.setRowCount(len(sales_data))
        
        for row, row_data in enumerate(sales_data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                
                # تلوين خلايا الحالة
                if col == 4:  # عمود الحالة
                    if cell_data == "مدفوعة":
                        item.setBackground(QColor("#e8f5e8"))
                        item.setForeground(QColor("#2e7d32"))
                    elif cell_data == "معلقة":
                        item.setBackground(QColor("#fff3e0"))
                        item.setForeground(QColor("#ef6c00"))
                    elif cell_data == "ملغاة":
                        item.setBackground(QColor("#ffebee"))
                        item.setForeground(QColor("#c62828"))
                
                table.setItem(row, col, item)
        
        # ضبط عرض الأعمدة
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # رقم الفاتورة
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # العميل
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # الحالة
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setMinimumHeight(300)
        
        layout.addWidget(title_label)
        layout.addWidget(table)
        
        return frame
    
    def create_customers_report_tab(self):
        """تبويب تقرير العملاء"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        control_bar = self.create_control_bar("العملاء")
        layout.addWidget(control_bar)
        
        # إحصائيات العملاء
        customers_stats = self.create_customers_stats()
        layout.addWidget(customers_stats)
        
        # الرسم البياني والجدول
        content_splitter = QSplitter(Qt.Horizontal)
        
        chart_frame = self.create_chart_section("أفضل 5 عملاء", "pie")
        table_frame = self.create_customers_table()
        
        content_splitter.addWidget(chart_frame)
        content_splitter.addWidget(table_frame)
        content_splitter.setSizes([400, 600])
        
        layout.addWidget(content_splitter)
        
        return tab
    
    def create_customers_stats(self):
        """إحصائيات العملاء"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e3f2fd;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        layout = QGridLayout(frame)
        layout.setSpacing(15)
        
        stats = [
            ("👥 العملاء الكل", "248 عميل", "#4CAF50", "⬆️ +18"),
            ("🔥 العملاء النشطين", "186 عميل", "#2196F3", "⬆️ +12"),
            ("⭐ عملاء VIP", "42 عميل", "#FF9800", "⬆️ +5"),
            ("💎 أفضل عميل", "أحمد محمد", "#9C27B0", "95,750 ج.م")
        ]
        
        for i, (title, value, color, change) in enumerate(stats):
            card = self.create_large_stat_card(title, value, color, change)
            layout.addWidget(card, 0, i)
        
        return frame
    
    def create_customers_table(self):
        """جدول العملاء"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        title_label = QLabel("👥 قائمة أفضل العملاء")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["العميل", "إجمالي المشتريات", "عدد الفواتير", "التصنيف"])
        
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #e3f2fd;
                font-size: 13px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f5f5f5, stop:1 #e0e0e0);
                color: #333;
                padding: 12px 8px;
                border: 1px solid #ddd;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        
        customers_data = [
            ["أحمد محمد علي", "95,750 ج.م", "23", "⭐ VIP"],
            ["فاطمة أحمد", "76,200 ج.م", "18", "🔥 نشط"],
            ["محمد حسن", "62,800 ج.م", "15", "🔥 نشط"],
            ["سارة محمود", "48,500 ج.م", "12", "👤 عادي"],
            ["علي عبدالله", "42,300 ج.م", "10", "👤 عادي"],
            ["نورا السيد", "38,900 ج.م", "9", "👤 عادي"]
        ]
        
        table.setRowCount(len(customers_data))
        
        for row, row_data in enumerate(customers_data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                if col == 3:  # عمود التصنيف
                    if "VIP" in cell_data:
                        item.setBackground(QColor("#fff3e0"))
                        item.setForeground(QColor("#ef6c00"))
                    elif "نشط" in cell_data:
                        item.setBackground(QColor("#e8f5e8"))
                        item.setForeground(QColor("#2e7d32"))
                table.setItem(row, col, item)
        
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setMinimumHeight(300)
        
        layout.addWidget(title_label)
        layout.addWidget(table)
        
        return frame
    
    def create_inventory_report_tab(self):
        """تبويب تقرير المخزون"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        control_bar = self.create_control_bar("المخزون")
        layout.addWidget(control_bar)
        
        # إحصائيات المخزون
        inventory_stats = self.create_inventory_stats()
        layout.addWidget(inventory_stats)
        
        # الرسم البياني والجدول
        content_splitter = QSplitter(Qt.Horizontal)
        
        chart_frame = self.create_chart_section("حالة المخزون", "pie")
        table_frame = self.create_inventory_table()
        
        content_splitter.addWidget(chart_frame)
        content_splitter.addWidget(table_frame)
        content_splitter.setSizes([400, 600])
        
        layout.addWidget(content_splitter)
        
        return tab
    
    def create_inventory_stats(self):
        """إحصائيات المخزون"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e3f2fd;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        layout = QGridLayout(frame)
        layout.setSpacing(15)
        
        stats = [
            ("📦 إجمالي المنتجات", "1,248 منتج", "#4CAF50", "⬆️ +32"),
            ("⚠️ منخفض المخزون", "18 منتج", "#f44336", "🔔 تنبيه"),
            ("💰 قيمة المخزون", "2,450,000 ج.م", "#2196F3", "⬆️ +8.5%"),
            ("🔄 معدل الدوران", "4.2 مرة/شهر", "#FF9800", "⬆️ +0.3")
        ]
        
        for i, (title, value, color, change) in enumerate(stats):
            card = self.create_large_stat_card(title, value, color, change)
            layout.addWidget(card, 0, i)
        
        return frame
    
    def create_inventory_table(self):
        """جدول المخزون"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        title_label = QLabel("📦 حالة المخزون التفصيلية")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        table = QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(["المنتج", "الكمية المتاحة", "الحد الأدنى", "السعر", "الحالة"])
        
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #e3f2fd;
                font-size: 13px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f5f5f5, stop:1 #e0e0e0);
                color: #333;
                padding: 12px 8px;
                border: 1px solid #ddd;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        
        inventory_data = [
            ["لابتوب ديل XPS", "45", "10", "25,000 ج.م", "🟢 طبيعي"],
            ["ماوس لوجيتك", "8", "15", "450 ج.م", "🔴 منخفض"],
            ["كيبورد ميكانيكي", "23", "5", "1,200 ج.م", "🟢 طبيعي"],
            ["شاشة سامسونج 24\"", "12", "3", "3,500 ج.م", "🟢 طبيعي"],
            ["سماعات بلوتوث", "156", "20", "350 ج.م", "🟢 طبيعي"],
            ["كابل USB-C", "7", "25", "85 ج.م", "🔴 منخفض"],
            ["حقيبة لابتوب", "34", "10", "250 ج.م", "🟢 طبيعي"],
            ["بطارية خارجية", "89", "15", "180 ج.م", "🟢 طبيعي"]
        ]
        
        table.setRowCount(len(inventory_data))
        
        for row, row_data in enumerate(inventory_data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                if col == 4:  # عمود الحالة
                    if "منخفض" in cell_data:
                        item.setBackground(QColor("#ffebee"))
                        item.setForeground(QColor("#c62828"))
                    elif "طبيعي" in cell_data:
                        item.setBackground(QColor("#e8f5e8"))
                        item.setForeground(QColor("#2e7d32"))
                table.setItem(row, col, item)
        
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setMinimumHeight(300)
        
        layout.addWidget(title_label)
        layout.addWidget(table)
        
        return frame
    
    def create_financial_report_tab(self):
        """تبويب التقرير المالي"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        control_bar = self.create_control_bar("المالي")
        layout.addWidget(control_bar)
        
        # إحصائيات مالية
        financial_stats = self.create_financial_stats()
        layout.addWidget(financial_stats)
        
        # الرسم البياني والجدول
        content_splitter = QSplitter(Qt.Horizontal)
        
        chart_frame = self.create_chart_section("التدفق النقدي الشهري", "line")
        table_frame = self.create_financial_table()
        
        content_splitter.addWidget(chart_frame)
        content_splitter.addWidget(table_frame)
        content_splitter.setSizes([500, 500])
        
        layout.addWidget(content_splitter)
        
        return tab
    
    def create_financial_stats(self):
        """إحصائيات مالية"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e3f2fd;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        layout = QGridLayout(frame)
        layout.setSpacing(15)
        
        stats = [
            ("💰 إجمالي الإيرادات", "1,850,000 ج.م", "#4CAF50", "⬆️ +15.2%"),
            ("💸 إجمالي المصروفات", "1,230,000 ج.م", "#f44336", "⬆️ +8.7%"),
            ("💵 صافي الربح", "620,000 ج.م", "#2196F3", "⬆️ +28.5%"),
            ("📊 هامش الربح", "33.5%", "#FF9800", "⬆️ +2.1%")
        ]
        
        for i, (title, value, color, change) in enumerate(stats):
            card = self.create_large_stat_card(title, value, color, change)
            layout.addWidget(card, 0, i)
        
        return frame
    
    def create_financial_table(self):
        """جدول مالي"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        title_label = QLabel("💼 ملخص الحسابات الشهرية")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #333;
            padding: 10px 0;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 15px;
        """)
        
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["الشهر", "الإيرادات", "المصروفات", "صافي الربح"])
        
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #e3f2fd;
                font-size: 13px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f5f5f5, stop:1 #e0e0e0);
                color: #333;
                padding: 12px 8px;
                border: 1px solid #ddd;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        
        financial_data = [
            ["يناير 2024", "320,000 ج.م", "210,000 ج.م", "110,000 ج.م"],
            ["فبراير 2024", "280,000 ج.م", "195,000 ج.م", "85,000 ج.م"],
            ["مارس 2024", "350,000 ج.م", "225,000 ج.م", "125,000 ج.م"],
            ["أبريل 2024", "290,000 ج.م", "200,000 ج.م", "90,000 ج.م"],
            ["مايو 2024", "380,000 ج.م", "245,000 ج.م", "135,000 ج.م"],
            ["يونيو 2024", "230,000 ج.م", "155,000 ج.م", "75,000 ج.م"]
        ]
        
        table.setRowCount(len(financial_data))
        
        for row, row_data in enumerate(financial_data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                if col == 3:  # عمود صافي الربح
                    profit = int(cell_data.replace(",", "").replace(" ج.م", ""))
                    if profit > 100000:
                        item.setBackground(QColor("#e8f5e8"))
                        item.setForeground(QColor("#2e7d32"))
                    elif profit < 80000:
                        item.setBackground(QColor("#fff3e0"))
                        item.setForeground(QColor("#ef6c00"))
                table.setItem(row, col, item)
        
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setMinimumHeight(300)
        
        layout.addWidget(title_label)
        layout.addWidget(table)
        
        return frame
    
    def get_monthly_sales_data(self):
        """بيانات المبيعات الشهرية"""
        return [
            ("يناير", 320000), ("فبراير", 280000), ("مارس", 350000),
            ("أبريل", 290000), ("مايو", 380000), ("يونيو", 230000)
        ]
    
    def get_sales_distribution_data(self):
        """بيانات توزيع المبيعات"""
        return [
            ("إلكترونيات", 35, "#FF6B6B"),
            ("ملابس", 25, "#4ECDC4"), 
            ("أدوات منزلية", 20, "#45B7D1"),
            ("كتب", 15, "#96CEB4"),
            ("أخرى", 5, "#FFEAA7")
        ]
    
    def get_sales_growth_data(self):
        """بيانات نمو المبيعات"""
        return [
            (1, 250000), (2, 280000), (3, 320000), (4, 350000),
            (5, 290000), (6, 380000), (7, 410000), (8, 450000)
        ]
    
    def refresh_report(self):
        """تحديث التقرير"""
        print("تم تحديث التقرير بنجاح!")
    
    def print_report(self):
        """طباعة التقرير"""
        print("جاري إعداد الطباعة...")
    
    def load_sample_data(self):
        """تحميل البيانات الوهمية"""
        print("تم تحميل بيانات التقارير بنجاح")