"""
النافذة الرئيسية المحسنة التي تجمع جميع التحسينات
"""
from PySide6.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, 
                              QStackedWidget, QSplitter, QStatusBar, QMessageBox,
                              QToolBar, QMenuBar, QApplication)
from PySide6.QtCore import Qt, QTimer, pyqtSignal
from PySide6.QtGui import QAction, QIcon, QPixmap

# استيراد النوافذ المحسنة
from accounting_app.ui.enhanced_settings_window import EnhancedSettingsWindow
from accounting_app.ui.enhanced_dashboard import EnhancedDashboard
from accounting_app.ui.enhanced_reports_window import EnhancedReportsWindow
from accounting_app.ui.enhanced_sidebar import EnhancedSidebar

# استيراد النوافذ الأصلية
from accounting_app.ui.products_window import ProductsWindow
from accounting_app.ui.inventory_window import InventoryWindow
from accounting_app.ui.sales_window import SalesWindow
from accounting_app.ui.purchases_window import PurchasesWindow
from accounting_app.ui.customers_window import CustomersWindow
from accounting_app.ui.suppliers_window import SuppliersWindow

from accounting_app.ui.styles import AppStyles
from datetime import datetime


class EnhancedMainWindow(QMainWindow):
    """النافذة الرئيسية المحسنة"""
    
    def __init__(self, user_data, parent=None):
        super().__init__(parent)
        self.user_data = user_data
        self.current_page = "dashboard"
        self.is_dark_mode = False
        self.pages = {}
        
        self.init_ui()
        self.setup_status_bar()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.connect_signals()
        
        # تحميل الصفحة الافتراضية
        self.show_page("dashboard")
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("💼 نظام المحاسبة المتطور - الإصدار 2.0")
        self.setMinimumSize(1400, 900)
        self.resize(1600, 1000)
        
        # تطبيق الأنماط
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
        """)
        
        # الوجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # الشريط الجانبي المحسن
        self.sidebar = EnhancedSidebar(self.user_data)
        main_layout.addWidget(self.sidebar)
        
        # منطقة المحتوى
        self.content_area = QStackedWidget()
        self.content_area.setStyleSheet("""
            QStackedWidget {
                background-color: #ffffff;
                border: none;
            }
        """)
        main_layout.addWidget(self.content_area)
        
        # إنشاء الصفحات
        self.create_pages()
    
    def create_pages(self):
        """إنشاء جميع صفحات التطبيق"""
        # لوحة التحكم المحسنة
        self.pages["dashboard"] = EnhancedDashboard(self.user_data)
        self.content_area.addWidget(self.pages["dashboard"])
        
        # صفحة التقارير المحسنة
        self.pages["reports"] = EnhancedReportsWindow()
        self.content_area.addWidget(self.pages["reports"])
        
        # صفحة الإعدادات المحسنة
        self.pages["settings"] = EnhancedSettingsWindow()
        self.content_area.addWidget(self.pages["settings"])
        
        # الصفحات الأصلية
        self.pages["products"] = ProductsWindow()
        self.content_area.addWidget(self.pages["products"])
        
        self.pages["inventory"] = InventoryWindow()
        self.content_area.addWidget(self.pages["inventory"])
        
        self.pages["sales"] = SalesWindow()
        self.content_area.addWidget(self.pages["sales"])
        
        self.pages["purchases"] = PurchasesWindow()
        self.content_area.addWidget(self.pages["purchases"])
        
        self.pages["customers"] = CustomersWindow()
        self.content_area.addWidget(self.pages["customers"])
        
        self.pages["suppliers"] = SuppliersWindow()
        self.content_area.addWidget(self.pages["suppliers"])
        
        # صفحة الشؤون المالية (مؤقتة)
        self.pages["finances"] = self.create_placeholder_page(
            "💰 الشؤون المالية",
            "صفحة إدارة الحسابات والأموال - قريباً..."
        )
        self.content_area.addWidget(self.pages["finances"])
    
    def create_placeholder_page(self, title, description):
        """إنشاء صفحة مؤقتة"""
        from PySide6.QtWidgets import QFrame, QLabel
        
        page = QFrame()
        page.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                margin: 20px;
            }
        """)
        
        layout = QVBoxLayout(page)
        layout.setAlignment(Qt.AlignCenter)
        
        icon = QLabel("🚧")
        icon.setStyleSheet("font-size: 80px;")
        icon.setAlignment(Qt.AlignCenter)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            color: #495057;
            font-size: 24px;
            font-weight: bold;
            margin: 20px 0;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            color: #6c757d;
            font-size: 16px;
            margin-bottom: 20px;
        """)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)
        
        layout.addWidget(icon)
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        
        return page
    
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)
        
        # المعلومات الأساسية
        self.status_label = status_bar.addWidget(
            QLabel(f"مرحباً {self.user_data[3] if len(self.user_data) > 3 else 'المستخدم'}")
        )
        
        # فاصل
        status_bar.addPermanentWidget(QLabel("|"))
        
        # التاريخ والوقت
        self.datetime_label = QLabel()
        self.update_datetime()
        status_bar.addPermanentWidget(self.datetime_label)
        
        # فاصل
        status_bar.addPermanentWidget(QLabel("|"))
        
        # حالة الاتصال
        connection_label = QLabel("🟢 متصل")
        connection_label.setStyleSheet("color: #28a745; font-weight: bold;")
        status_bar.addPermanentWidget(connection_label)
        
        # فاصل
        status_bar.addPermanentWidget(QLabel("|"))
        
        # إصدار النظام
        version_label = QLabel("الإصدار 2.0")
        version_label.setStyleSheet("color: #6c757d; font-size: 11px;")
        status_bar.addPermanentWidget(version_label)
        
        # مؤقت لتحديث الوقت
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_datetime)
        self.time_timer.start(1000)  # تحديث كل ثانية
        
        # تنسيق شريط الحالة
        status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f8f9fa;
                border-top: 1px solid #dee2e6;
                padding: 5px;
                color: #495057;
                font-size: 12px;
            }
            QStatusBar::item {
                border: none;
            }
        """)
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        menubar.setStyleSheet("""
            QMenuBar {
                background-color: white;
                border-bottom: 1px solid #dee2e6;
                padding: 5px;
                color: #495057;
                font-size: 13px;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background-color: #e9ecef;
                color: #007bff;
            }
            QMenu {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 8px 0;
            }
            QMenu::item {
                padding: 8px 20px;
                color: #495057;
            }
            QMenu::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        # قائمة الملف
        file_menu = menubar.addMenu("📁 ملف")
        
        new_action = QAction("📄 جديد", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.new_file)
        file_menu.addAction(new_action)
        
        open_action = QAction("📂 فتح", self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self.open_file)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("🚪 خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة العرض
        view_menu = menubar.addMenu("👁️ عرض")
        
        toggle_sidebar_action = QAction("☰ إظهار/إخفاء الشريط الجانبي", self)
        toggle_sidebar_action.setShortcut("F9")
        toggle_sidebar_action.triggered.connect(self.sidebar.toggle_sidebar)
        view_menu.addAction(toggle_sidebar_action)
        
        toggle_theme_action = QAction("🌙 تبديل المظهر", self)
        toggle_theme_action.setShortcut("F10")
        toggle_theme_action.triggered.connect(self.sidebar.toggle_theme)
        view_menu.addAction(toggle_theme_action)
        
        # قائمة الأدوات
        tools_menu = menubar.addMenu("🔧 أدوات")
        
        calculator_action = QAction("🧮 حاسبة", self)
        calculator_action.triggered.connect(self.open_calculator)
        tools_menu.addAction(calculator_action)
        
        backup_action = QAction("💾 نسخ احتياطي", self)
        backup_action.triggered.connect(self.create_backup)
        tools_menu.addAction(backup_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("❓ مساعدة")
        
        help_action = QAction("📖 دليل المستخدم", self)
        help_action.setShortcut("F1")
        help_action.triggered.connect(self.show_help)
        help_menu.addAction(help_action)
        
        about_action = QAction("ℹ️ حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = QToolBar("شريط الأدوات الرئيسي")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        toolbar.setStyleSheet("""
            QToolBar {
                background-color: white;
                border-bottom: 1px solid #dee2e6;
                padding: 8px;
                spacing: 10px;
            }
            QToolButton {
                background-color: transparent;
                border: 1px solid transparent;
                border-radius: 6px;
                padding: 8px;
                color: #495057;
                font-size: 11px;
            }
            QToolButton:hover {
                background-color: #e9ecef;
                border-color: #ced4da;
            }
            QToolButton:pressed {
                background-color: #007bff;
                color: white;
            }
        """)
        
        # أدوات سريعة
        dashboard_action = QAction("🏠", self)
        dashboard_action.setText("لوحة التحكم")
        dashboard_action.triggered.connect(lambda: self.show_page("dashboard"))
        toolbar.addAction(dashboard_action)
        
        sales_action = QAction("🛒", self)
        sales_action.setText("مبيعات سريعة")
        sales_action.triggered.connect(lambda: self.show_page("sales"))
        toolbar.addAction(sales_action)
        
        reports_action = QAction("📊", self)
        reports_action.setText("التقارير")
        reports_action.triggered.connect(lambda: self.show_page("reports"))
        toolbar.addAction(reports_action)
        
        toolbar.addSeparator()
        
        settings_action = QAction("⚙️", self)
        settings_action.setText("الإعدادات")
        settings_action.triggered.connect(lambda: self.show_page("settings"))
        toolbar.addAction(settings_action)
        
        self.addToolBar(toolbar)
    
    def connect_signals(self):
        """ربط الإشارات"""
        # ربط إشارات الشريط الجانبي
        self.sidebar.menuItemClicked.connect(self.on_sidebar_menu_clicked)
        self.sidebar.themeChanged.connect(self.on_theme_changed)
    
    def show_page(self, page_name):
        """عرض صفحة معينة"""
        if page_name in self.pages:
            self.content_area.setCurrentWidget(self.pages[page_name])
            self.current_page = page_name
            
            # تحديث الشريط الجانبي
            self.sidebar.set_active_page(page_name)
            
            # تحديث شريط الحالة
            page_titles = {
                "dashboard": "لوحة التحكم",
                "sales": "المبيعات",
                "purchases": "المشتريات",
                "inventory": "المخزون",
                "customers": "العملاء",
                "suppliers": "الموردين",
                "reports": "التقارير",
                "finances": "الشؤون المالية",
                "settings": "الإعدادات",
                "products": "المنتجات"
            }
            
            current_title = page_titles.get(page_name, page_name)
            self.status_label.setText(f"الصفحة الحالية: {current_title}")
    
    def on_sidebar_menu_clicked(self, page_name):
        """معالج النقر على عنصر في الشريط الجانبي"""
        if page_name == "logout":
            self.logout()
        else:
            self.show_page(page_name)
    
    def on_theme_changed(self, is_dark_mode):
        """معالج تغيير المظهر"""
        self.is_dark_mode = is_dark_mode
        
        # تحديث الأنماط العامة
        if is_dark_mode:
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #2c3e50;
                    color: #ecf0f1;
                }
            """)
            
            # تحديث شريط الحالة
            self.statusBar().setStyleSheet("""
                QStatusBar {
                    background-color: #34495e;
                    border-top: 1px solid #2c3e50;
                    color: #ecf0f1;
                }
            """)
        else:
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #f8f9fa;
                    color: #495057;
                }
            """)
            
            # إعادة الأنماط الأصلية
            self.statusBar().setStyleSheet("""
                QStatusBar {
                    background-color: #f8f9fa;
                    border-top: 1px solid #dee2e6;
                    color: #495057;
                }
            """)
    
    def update_datetime(self):
        """تحديث التاريخ والوقت"""
        current_time = datetime.now()
        formatted_time = current_time.strftime("%Y/%m/%d %H:%M:%S")
        self.datetime_label.setText(formatted_time)
    
    def new_file(self):
        """إنشاء ملف جديد"""
        QMessageBox.information(self, "ملف جديد", "ميزة إنشاء ملف جديد ستتوفر قريباً!")
    
    def open_file(self):
        """فتح ملف"""
        QMessageBox.information(self, "فتح ملف", "ميزة فتح الملفات ستتوفر قريباً!")
    
    def open_calculator(self):
        """فتح الحاسبة"""
        try:
            import subprocess
            subprocess.Popen("calc.exe")
        except:
            QMessageBox.warning(self, "خطأ", "لا يمكن فتح الحاسبة!")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        reply = QMessageBox.question(
            self, "نسخ احتياطي",
            "هل تريد إنشاء نسخة احتياطية من البيانات؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "نسخ احتياطي", "تم إنشاء النسخة الاحتياطية بنجاح!")
    
    def show_help(self):
        """عرض المساعدة"""
        help_text = """
        🔍 دليل الاستخدام السريع:
        
        📋 التنقل:
        • استخدم الشريط الجانبي للتنقل بين الأقسام
        • أو استخدم شريط الأدوات للوصول السريع
        
        ⌨️ اختصارات لوحة المفاتيح:
        • Ctrl+N: جديد
        • Ctrl+O: فتح
        • Ctrl+Q: خروج
        • F9: إظهار/إخفاء الشريط الجانبي
        • F10: تبديل المظهر
        • F1: المساعدة
        
        🎨 المظهر:
        • يمكنك تبديل المظهر الفاتح/الداكن من الشريط الجانبي
        • أو من قائمة العرض أو بالضغط على F10
        
        📊 التقارير:
        • استخدم الفلاتر المتقدمة للحصول على تقارير مخصصة
        • يمكن تصدير التقارير بصيغ متعددة
        
        ⚙️ الإعدادات:
        • جميع الإعدادات محفوظة تلقائياً
        • يمكن إعادة تعيينها في أي وقت
        """
        
        QMessageBox.information(self, "المساعدة", help_text)
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """
        💼 نظام المحاسبة المتطور
        الإصدار 2.0
        
        🚀 الميزات الجديدة:
        ✅ واجهة محسنة مع تأثيرات بصرية
        ✅ شريط جانبي تفاعلي مع أيقونات
        ✅ تقارير متقدمة مع فلاتر ذكية
        ✅ إعدادات منظمة وسهلة الاستخدام
        ✅ دعم المظهر الفاتح والداكن
        ✅ إشعارات وتنبيهات ذكية
        ✅ حفظ تلقائي للإعدادات
        ✅ واجهة مستجيبة وسريعة
        
        👨‍💻 تطوير: فريق التطوير
        📧 الدعم: <EMAIL>
        🌐 الموقع: www.company.com
        
        © 2024 جميع الحقوق محفوظة
        """
        
        QMessageBox.about(self, "حول البرنامج", about_text)
    
    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(
            self, "تسجيل الخروج",
            "هل تريد تسجيل الخروج من النظام؟\n\nسيتم حفظ جميع التغييرات تلقائياً.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # حفظ الإعدادات قبل الخروج
            QMessageBox.information(self, "تسجيل الخروج", "تم تسجيل الخروج بنجاح!")
            self.close()
    
    def closeEvent(self, event):
        """معالج إغلاق النافذة"""
        reply = QMessageBox.question(
            self, "إغلاق البرنامج",
            "هل تريد إغلاق البرنامج؟\n\nسيتم حفظ جميع البيانات تلقائياً.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # إيقاف المؤقتات
            if hasattr(self, 'time_timer'):
                self.time_timer.stop()
            
            event.accept()
        else:
            event.ignore()


# دالة اختبار النافذة المحسنة
def test_enhanced_window():
    """اختبار النافذة المحسنة"""
    import sys
    
    app = QApplication(sys.argv)
    
    # بيانات مستخدم تجريبية
    user_data = ["1", "admin", "admin123", "المدير العام", "مدير", "2024-01-15"]
    
    # إنشاء النافذة
    window = EnhancedMainWindow(user_data)
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    test_enhanced_window()