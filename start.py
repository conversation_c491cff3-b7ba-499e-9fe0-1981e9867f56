#!/usr/bin/env python3
"""
🚀 مشغل نظام المحاسبة المتقدم
Advanced Accounting System Launcher

ملف تشغيل شامل مع خيارات متعددة
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def print_menu():
    """طباعة قائمة الخيارات"""
    print("\n" + "="*60)
    print("💼 نظام المحاسبة المتقدم - Advanced Accounting System")
    print("="*60)
    print("اختر طريقة التشغيل - Choose launch method:")
    print()
    print("1️⃣  التطبيق الكامل (موصى به) - Full Application (Recommended)")
    print("    python final_app.py")
    print()
    print("2️⃣  التطبيق المحسن - Enhanced Application")
    print("    python run_app.py")
    print()
    print("3️⃣  اختبار سريع - Quick Test")
    print("    python quick_start.py")
    print()
    print("4️⃣  التطبيق التقليدي - Traditional Application")
    print("    python -m accounting_app.main")
    print()
    print("5️⃣  فحص النظام - System Check")
    print()
    print("0️⃣  خروج - Exit")
    print("="*60)

def check_system():
    """فحص النظام"""
    print("\n🔍 فحص النظام - System Check")
    print("-" * 40)
    
    # فحص Python
    print(f"🐍 Python: {sys.version}")
    if sys.version_info >= (3, 8):
        print("✅ إصدار Python مناسب")
    else:
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    
    # فحص PySide6
    try:
        import PySide6
        print(f"🎨 PySide6: متوفر")
        print("✅ PySide6 مثبت بنجاح")
    except ImportError:
        print("❌ PySide6 غير مثبت")
        print("💡 قم بتثبيته: pip install PySide6")
        return False
    
    # فحص الملفات
    required_files = [
        "final_app.py",
        "accounting_app/main.py",
        "accounting_app/ui/enhanced_dashboard.py"
    ]
    
    print("\n📁 فحص الملفات:")
    all_files_exist = True
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} مفقود")
            all_files_exist = False
    
    if all_files_exist:
        print("\n🎉 النظام جاهز للتشغيل!")
        print("🎉 System ready to run!")
        return True
    else:
        print("\n❌ بعض الملفات مفقودة")
        return False

def run_application(choice):
    """تشغيل التطبيق حسب الاختيار"""
    commands = {
        "1": "python final_app.py",
        "2": "python run_app.py", 
        "3": "python quick_start.py",
        "4": "python -m accounting_app.main"
    }
    
    if choice in commands:
        print(f"\n🚀 تشغيل: {commands[choice]}")
        print("🚀 Running:", commands[choice])
        print("-" * 40)
        
        try:
            os.system(commands[choice])
        except KeyboardInterrupt:
            print("\n👋 تم إيقاف التطبيق")
        except Exception as e:
            print(f"\n❌ خطأ: {e}")
    else:
        print("❌ اختيار غير صحيح")

def main():
    """الدالة الرئيسية"""
    while True:
        print_menu()
        
        try:
            choice = input("\nاختر رقم (1-5) أو 0 للخروج: ").strip()
            
            if choice == "0":
                print("\n👋 وداعاً - Goodbye!")
                break
            elif choice == "5":
                check_system()
                input("\nاضغط Enter للمتابعة...")
            elif choice in ["1", "2", "3", "4"]:
                run_application(choice)
                input("\nاضغط Enter للعودة للقائمة...")
            else:
                print("❌ اختيار غير صحيح. اختر رقم من 0-5")
                
        except KeyboardInterrupt:
            print("\n\n👋 تم الخروج - Exited")
            break
        except Exception as e:
            print(f"\n❌ خطأ: {e}")
            input("اضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()
