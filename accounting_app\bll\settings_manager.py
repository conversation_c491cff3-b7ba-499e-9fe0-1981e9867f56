"""
مدير الإعدادات - يتعامل مع إعدادات التطبيق
"""
from accounting_app.dal.database import DatabaseManager


class SettingsManager:
    """مدير الإعدادات - يتعامل مع إعدادات التطبيق"""
    
    def __init__(self):
        """تهيئة مدير الإعدادات"""
        self.db = DatabaseManager()
        self._settings_cache = {}
        self._load_settings()
    
    def _load_settings(self):
        """تحميل جميع الإعدادات من قاعدة البيانات إلى الذاكرة المؤقتة"""
        self.db.connect()
        query = "SELECT key, value FROM settings"
        settings = self.db.fetch_all(query)
        self.db.close()
        
        for key, value in settings:
            self._settings_cache[key] = value
    
    def get_setting(self, key, default=None):
        """
        الحصول على قيمة إعداد
        :param key: مفتاح الإعداد
        :param default: القيمة الافتراضية إذا لم يتم العثور على الإعداد
        :return: قيمة الإعداد
        """
        return self._settings_cache.get(key, default)
    
    def set_setting(self, key, value, description=None):
        """
        تعيين قيمة إعداد
        :param key: مفتاح الإعداد
        :param value: قيمة الإعداد
        :param description: وصف الإعداد (اختياري)
        :return: True إذا تم التعيين بنجاح، وإلا False
        """
        self.db.connect()
        
        # التحقق مما إذا كان الإعداد موجودًا بالفعل
        query = "SELECT id FROM settings WHERE key = ?"
        existing = self.db.fetch_one(query, (key,))
        
        if existing:
            # تحديث الإعداد الموجود
            query = "UPDATE settings SET value = ? WHERE key = ?"
            cursor = self.db.execute_query(query, (value, key))
        else:
            # إنشاء إعداد جديد
            query = "INSERT INTO settings (key, value, description) VALUES (?, ?, ?)"
            cursor = self.db.execute_query(query, (key, value, description))
        
        success = cursor is not None
        self.db.close()
        
        if success:
            # تحديث الذاكرة المؤقتة
            self._settings_cache[key] = value
        
        return success
    
    def get_all_settings(self):
        """
        الحصول على جميع الإعدادات
        :return: قاموس بجميع الإعدادات
        """
        return self._settings_cache.copy()
    
    def get_language(self):
        """
        الحصول على لغة التطبيق الحالية
        :return: رمز اللغة (ar/en)
        """
        return self.get_setting('language', 'ar')
    
    def set_language(self, language_code):
        """
        تعيين لغة التطبيق
        :param language_code: رمز اللغة (ar/en)
        :return: True إذا تم التعيين بنجاح، وإلا False
        """
        return self.set_setting('language', language_code)