"""
صفحة إدارة المنتجات
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QTableWidget, QTableWidgetItem, 
                              QHeaderView, QFrame, QLineEdit, QComboBox,
                              QSpinBox, QDoubleSpinBox, QDialog, QFormLayout,
                              QDialogButtonBox, QMessageBox, QGridLayout,
                              QGroupBox, QScrollArea)
from PySide6.QtCore import Qt
from PySide6.QtGui import QColor

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class ProductsWindow(QWidget):
    """صفحة إدارة المنتجات"""
    
    def __init__(self, parent=None):
        """
        تهيئة صفحة إدارة المنتجات
        :param parent: النافذة الأم
        """
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        try:
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)
            
            # شريط الأدوات العلوي
            toolbar_frame = self.create_toolbar()
            
            # جدول المنتجات
            products_table = self.create_products_table()
            
            # إضافة العناصر إلى التخطيط
            main_layout.addWidget(toolbar_frame)
            main_layout.addWidget(products_table)
        except Exception as e:
            print("Error in ProductsWindow.init_ui:", e)
            import traceback
            traceback.print_exc()
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        try:
            toolbar_frame = QFrame()
            toolbar_frame.setStyleSheet(f"""
                background-color: white;
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 15px;
                border: 1px solid {AppStyles.BORDER_COLOR};
            """)
            
            toolbar_layout = QHBoxLayout(toolbar_frame)
            
            # زر إضافة منتج جديد
            add_btn = QPushButton("إضافة منتج جديد")
            add_btn.setStyleSheet(AppStyles.BUTTON_STYLE)
            add_btn.clicked.connect(self.add_product)
            
            # زر تعديل منتج
            edit_btn = QPushButton("تعديل")
            edit_btn.setStyleSheet(AppStyles.BUTTON_SECONDARY_STYLE)
            edit_btn.clicked.connect(self.edit_product)
            
            # زر حذف منتج
            delete_btn = QPushButton("حذف")
            delete_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {AppStyles.ERROR_COLOR};
                    color: white;
                    border: none;
                    border-radius: {AppStyles.BORDER_RADIUS}px;
                    padding: 10px 20px;
                    font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: #C62828;
                }}
            """)
            delete_btn.clicked.connect(self.delete_product)
            
            # شريط البحث
            search_line = QLineEdit()
            search_line.setPlaceholderText("البحث في المنتجات...")
            search_line.setStyleSheet(AppStyles.INPUT_STYLE)
            search_line.textChanged.connect(self.search_products)
            
            # إضافة الأزرار إلى التخطيط
            toolbar_layout.addWidget(add_btn)
            toolbar_layout.addWidget(edit_btn)
            toolbar_layout.addWidget(delete_btn)
            toolbar_layout.addStretch()
            toolbar_layout.addWidget(QLabel("البحث:"))
            toolbar_layout.addWidget(search_line)
            
            return toolbar_frame
        except Exception as e:
            print("Error in create_toolbar:", e)
            import traceback
            traceback.print_exc()
            return QFrame()
    
    def create_products_table(self):
        """إنشاء جدول المنتجات"""
        try:
            # إنشاء الجدول
            self.products_table = QTableWidget()
            self.products_table.setColumnCount(7)
            
            # تعيين عناوين الأعمدة
            headers = ["الكود", "اسم المنتج", "الفئة", "سعر الشراء", "سعر البيع", "الكمية", "الحد الأدنى"]
            self.products_table.setHorizontalHeaderLabels(headers)
            
            # تنسيق الجدول
            self.products_table.setStyleSheet(AppStyles.TABLE_STYLE)
            self.products_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
            self.products_table.setAlternatingRowColors(True)
            self.products_table.setSelectionBehavior(QTableWidget.SelectRows)
            
            # إضافة بيانات وهمية
            self.load_sample_data()
            
            return self.products_table
        except Exception as e:
            print("Error in create_products_table:", e)
            import traceback
            traceback.print_exc()
            return QTableWidget()
    
    def load_sample_data(self):
        """تحميل بيانات وهمية للجدول"""
        try:
            sample_data = [
                ["P001", "لابتوب HP", "إلكترونيات", "15000", "18000", "10", "5"],
                ["P002", "ماوس لاسلكي", "إكسسوارات", "150", "200", "25", "10"],
                ["P003", "كيبورد ميكانيكي", "إكسسوارات", "800", "1200", "15", "5"],
                ["P004", "شاشة 24 بوصة", "إلكترونيات", "3000", "4000", "8", "3"],
                ["P005", "طابعة Canon", "مكتبية", "2500", "3200", "6", "2"],
            ]
            
            self.products_table.setRowCount(len(sample_data))
            
            for row, row_data in enumerate(sample_data):
                for col, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(cell_data)
                    
                    # تلوين الكمية إذا كانت أقل من الحد الأدنى
                    if col == 5:  # عمود الكمية
                        quantity = int(cell_data)
                        min_quantity = int(row_data[6])  # الحد الأدنى
                        if quantity <= min_quantity:
                            item.setBackground(QColor(AppStyles.WARNING_COLOR + "30"))
                    
                    self.products_table.setItem(row, col, item)
        except Exception as e:
            print("Error in load_sample_data:", e)
            import traceback
            traceback.print_exc()
    
    def add_product(self):
        """إضافة منتج جديد"""
        try:
            dialog = ProductDialog(self)
            if dialog.exec() == QDialog.Accepted:
                # هنا يمكن إضافة كود حفظ المنتج في قاعدة البيانات
                QMessageBox.information(self, "نجح", "تم إضافة المنتج بنجاح")
                self.refresh_table()
        except Exception as e:
            print("Error in add_product:", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة المنتج: {str(e)}")
    
    def edit_product(self):
        """تعديل منتج محدد"""
        try:
            current_row = self.products_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج للتعديل")
                return
            
            dialog = ProductDialog(self, edit_mode=True)
            if dialog.exec() == QDialog.Accepted:
                QMessageBox.information(self, "نجح", "تم تعديل المنتج بنجاح")
                self.refresh_table()
        except Exception as e:
            print("Error in edit_product:", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل المنتج: {str(e)}")
    
    def delete_product(self):
        """حذف منتج محدد"""
        try:
            current_row = self.products_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج للحذف")
                return
            
            product_name = self.products_table.item(current_row, 1).text()
            reply = QMessageBox.question(self, "تأكيد الحذف", 
                                       f"هل أنت متأكد من حذف المنتج '{product_name}'؟",
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                self.products_table.removeRow(current_row)
                QMessageBox.information(self, "نجح", "تم حذف المنتج بنجاح")
        except Exception as e:
            print("Error in delete_product:", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف المنتج: {str(e)}")
    
    def search_products(self, text):
        """البحث في المنتجات"""
        try:
            for row in range(self.products_table.rowCount()):
                match = False
                for col in range(self.products_table.columnCount()):
                    item = self.products_table.item(row, col)
                    if item and text.lower() in item.text().lower():
                        match = True
                        break
                self.products_table.setRowHidden(row, not match)
        except Exception as e:
            print("Error in search_products:", e)
    
    def refresh_table(self):
        """تحديث الجدول"""
        try:
            # هنا يمكن إضافة كود لتحديث البيانات من قاعدة البيانات
            self.load_sample_data()
        except Exception as e:
            print("Error in refresh_table:", e)


class ProductDialog(QDialog):
    """نافذة حوار إضافة/تعديل منتج"""
    
    def __init__(self, parent=None, edit_mode=False):
        """
        تهيئة نافذة الحوار
        :param parent: النافذة الأم
        :param edit_mode: وضع التعديل
        """
        super().__init__(parent)
        self.edit_mode = edit_mode
        self.translator = Translator()
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        try:
            # إعداد النافذة
            title = "تعديل منتج" if self.edit_mode else "إضافة منتج جديد"
            self.setWindowTitle(title)
            self.setMinimumSize(500, 400)
            self.setStyleSheet(f"""
                QDialog {{
                    background-color: {AppStyles.BACKGROUND_COLOR};
                }}
            """)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)
            
            # عنوان النافذة
            title_label = QLabel(title)
            title_label.setStyleSheet(AppStyles.LABEL_HEADER_STYLE)
            
            # نموذج البيانات
            form_frame = QFrame()
            form_frame.setStyleSheet(f"""
                background-color: white;
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 20px;
                border: 1px solid {AppStyles.BORDER_COLOR};
            """)
            form_layout = QFormLayout(form_frame)
            
            # حقول النموذج
            self.code_edit = QLineEdit()
            self.code_edit.setStyleSheet(AppStyles.INPUT_STYLE)
            
            self.name_edit = QLineEdit()
            self.name_edit.setStyleSheet(AppStyles.INPUT_STYLE)
            
            self.category_combo = QComboBox()
            self.category_combo.addItems(["إلكترونيات", "إكسسوارات", "مكتبية", "أخرى"])
            self.category_combo.setStyleSheet(AppStyles.COMBOBOX_STYLE)
            
            self.description_edit = QLineEdit()
            self.description_edit.setStyleSheet(AppStyles.INPUT_STYLE)
            
            self.purchase_price_spin = QDoubleSpinBox()
            self.purchase_price_spin.setMaximum(999999.99)
            self.purchase_price_spin.setSuffix(" ج.م")
            self.purchase_price_spin.setStyleSheet(AppStyles.INPUT_STYLE)
            
            self.selling_price_spin = QDoubleSpinBox()
            self.selling_price_spin.setMaximum(999999.99)
            self.selling_price_spin.setSuffix(" ج.م")
            self.selling_price_spin.setStyleSheet(AppStyles.INPUT_STYLE)
            
            self.quantity_spin = QSpinBox()
            self.quantity_spin.setMaximum(999999)
            self.quantity_spin.setStyleSheet(AppStyles.INPUT_STYLE)
            
            self.min_quantity_spin = QSpinBox()
            self.min_quantity_spin.setMaximum(999999)
            self.min_quantity_spin.setStyleSheet(AppStyles.INPUT_STYLE)
            
            # إضافة الحقول إلى النموذج
            form_layout.addRow("كود المنتج:", self.code_edit)
            form_layout.addRow("اسم المنتج:", self.name_edit)
            form_layout.addRow("الفئة:", self.category_combo)
            form_layout.addRow("الوصف:", self.description_edit)
            form_layout.addRow("سعر الشراء:", self.purchase_price_spin)
            form_layout.addRow("سعر البيع:", self.selling_price_spin)
            form_layout.addRow("الكمية:", self.quantity_spin)
            form_layout.addRow("الحد الأدنى:", self.min_quantity_spin)
            
            # أزرار الحوار
            button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            button_box.accepted.connect(self.accept)
            button_box.rejected.connect(self.reject)
            button_box.setStyleSheet(f"""
                QPushButton {{
                    {AppStyles.BUTTON_STYLE}
                    min-width: 80px;
                }}
            """)
            
            # إضافة العناصر إلى التخطيط
            main_layout.addWidget(title_label)
            main_layout.addWidget(form_frame)
            main_layout.addWidget(button_box)
            
            # إذا كان في وضع التعديل، تحميل البيانات
            if self.edit_mode:
                self.load_product_data()
        except Exception as e:
            print("Error in ProductDialog.init_ui:", e)
            import traceback
            traceback.print_exc()
    
    def load_product_data(self):
        """تحميل بيانات المنتج في وضع التعديل"""
        try:
            # هنا يمكن تحميل البيانات من قاعدة البيانات
            # للتجربة سأضع بيانات وهمية
            self.code_edit.setText("P001")
            self.name_edit.setText("لابتوب HP")
            self.category_combo.setCurrentText("إلكترونيات")
            self.description_edit.setText("لابتوب HP بمواصفات عالية")
            self.purchase_price_spin.setValue(15000.00)
            self.selling_price_spin.setValue(18000.00)
            self.quantity_spin.setValue(10)
            self.min_quantity_spin.setValue(5)
        except Exception as e:
            print("Error in load_product_data:", e)