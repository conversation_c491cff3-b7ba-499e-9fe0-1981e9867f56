#!/usr/bin/env python3
"""
🚀 بدء سريع لنظام المحاسبة المتقدم
Quick Start for Advanced Accounting System

ملف تشغيل مبسط للاختبار السريع
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """تشغيل سريع للتطبيق"""
    print("🚀 بدء سريع - Quick Start")
    print("=" * 40)
    
    try:
        # فحص PySide6
        import PySide6
        print("✅ PySide6 متوفر")
        
        # استيراد التطبيق
        from PySide6.QtWidgets import QApplication
        from accounting_app.ui.enhanced_dashboard import EnhancedDashboard
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # بيانات تجريبية
        user_data = (1, "admin", "admin123", "المدير", "<EMAIL>")
        
        # إنشاء لوحة التحكم
        dashboard = EnhancedDashboard(user_data)
        dashboard.setWindowTitle("🧪 اختبار سريع - Quick Test")
        dashboard.show()
        
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("✅ Application started successfully!")
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"❌ خطأ: {e}")
        print("💡 قم بتثبيت: pip install PySide6")
        input("اضغط Enter للخروج...")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
