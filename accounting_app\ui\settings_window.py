"""
صفحة الإعدادات
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QLineEdit, QComboBox,
                              QCheckBox, QSpinBox, QDoubleSpinBox, QTextEdit,
                              QTabWidget, QFormLayout, QGroupBox, QSlider,
                              QMessageBox, QFileDialog, QColorDialog, QProgressBar)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QColor, QPalette

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class SettingsWindow(QWidget):
    """صفحة الإعدادات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        try:
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)
            
            # عنوان الصفحة
            header_frame = self.create_header()
            
            # علامات التبويب
            tabs_widget = self.create_settings_tabs()
            
            # أزرار الحفظ والإلغاء
            buttons_frame = self.create_buttons_frame()
            
            # إضافة العناصر
            main_layout.addWidget(header_frame)
            main_layout.addWidget(tabs_widget)
            main_layout.addWidget(buttons_frame)
        except Exception as e:
            print("Error in SettingsWindow.init_ui:", e)
    
    def create_header(self):
        """إنشاء رأس الصفحة"""
        try:
            header_frame = QFrame()
            header_frame.setStyleSheet(f"""
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 30px;
                border: 1px solid {AppStyles.BORDER_COLOR};
                min-height: 120px;
            """)
            
            header_layout = QHBoxLayout(header_frame)
            
            # العنوان والوصف مع إحصائيات
            left_section = QWidget()
            left_layout = QVBoxLayout(left_section)
            
            title_label = QLabel("🛠️ إعدادات النظام المتقدمة")
            title_label.setStyleSheet(f"""
                color: white;
                font-size: {AppStyles.FONT_SIZE_XXLARGE}px;
                font-weight: bold;
                margin-bottom: 10px;
            """)
            
            subtitle_label = QLabel("تخصيص شامل لجميع جوانب النظام • تحكم كامل في المظهر والوظائف")
            subtitle_label.setStyleSheet(f"""
                color: white;
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                opacity: 0.95;
                margin-bottom: 15px;
            """)
            
            # إحصائيات سريعة
            stats_widget = QWidget()
            stats_layout = QHBoxLayout(stats_widget)
            stats_layout.setSpacing(20)
            
            # عدد الإعدادات
            settings_count = self.create_mini_stat("إعدادات متاحة", "25+", "⚙️")
            modules_count = self.create_mini_stat("وحدات النظام", "8", "📦")
            customization = self.create_mini_stat("خيارات التخصيص", "50+", "🎨")
            
            stats_layout.addWidget(settings_count)
            stats_layout.addWidget(modules_count)
            stats_layout.addWidget(customization)
            stats_layout.addStretch()
            
            left_layout.addWidget(title_label)
            left_layout.addWidget(subtitle_label)
            left_layout.addWidget(stats_widget)
            
            # أيقونة وحالة الإعدادات
            right_section = QWidget()
            right_layout = QVBoxLayout(right_section)
            right_layout.setAlignment(Qt.AlignCenter)
            
            # أيقونة كبيرة
            main_icon = QLabel("⚙️")
            main_icon.setStyleSheet(f"""
                font-size: 80px;
                color: white;
                background: rgba(255, 255, 255, 0.15);
                border-radius: 40px;
                padding: 25px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            """)
            main_icon.setAlignment(Qt.AlignCenter)
            main_icon.setFixedSize(150, 150)
            
            # حالة النظام
            status_label = QLabel("✅ النظام محدث")
            status_label.setStyleSheet(f"""
                color: #4CAF50;
                font-size: {AppStyles.FONT_SIZE_SMALL}px;
                font-weight: bold;
                background: rgba(255, 255, 255, 0.9);
                padding: 5px 15px;
                border-radius: 15px;
                margin-top: 10px;
            """)
            status_label.setAlignment(Qt.AlignCenter)
            
            right_layout.addWidget(main_icon)
            right_layout.addWidget(status_label)
            
            header_layout.addWidget(left_section, 7)
            header_layout.addWidget(right_section, 3)
            
            return header_frame
        except Exception as e:
            print("Error in create_header:", e)
            return QFrame()
    
    def create_mini_stat(self, title, value, icon):
        """إنشاء إحصائية صغيرة للرأس"""
        stat_widget = QWidget()
        stat_layout = QHBoxLayout(stat_widget)
        stat_layout.setContentsMargins(0, 0, 0, 0)
        stat_layout.setSpacing(8)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            font-size: 20px;
            color: white;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 8px;
            min-width: 30px;
            max-width: 30px;
            min-height: 30px;
            max-height: 30px;
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        
        # النص والقيمة
        text_widget = QWidget()
        text_layout = QVBoxLayout(text_widget)
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(2)
        
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            color: white;
            font-size: {AppStyles.FONT_SIZE_NORMAL}px;
            font-weight: bold;
        """)
        
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            color: white;
            font-size: {AppStyles.FONT_SIZE_SMALL}px;
            opacity: 0.8;
        """)
        
        text_layout.addWidget(value_label)
        text_layout.addWidget(title_label)
        
        stat_layout.addWidget(icon_label)
        stat_layout.addWidget(text_widget)
        
        return stat_widget
    
    def create_settings_tabs(self):
        """إنشاء علامات تبويب الإعدادات"""
        try:
            tabs_widget = QTabWidget()
            tabs_widget.setStyleSheet(f"""
                QTabWidget::pane {{
                    border: 1px solid {AppStyles.BORDER_COLOR};
                    border-radius: {AppStyles.BORDER_RADIUS}px;
                    background-color: white;
                    padding: 10px;
                }}
                
                QTabBar::tab {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #f8f9fa, stop:1 #e9ecef);
                    color: {AppStyles.TEXT_PRIMARY};
                    padding: 15px 25px;
                    margin-right: 3px;
                    border-top-left-radius: {AppStyles.BORDER_RADIUS}px;
                    border-top-right-radius: {AppStyles.BORDER_RADIUS}px;
                    font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                    font-weight: 600;
                    min-width: 120px;
                    border: 1px solid {AppStyles.BORDER_COLOR};
                }}
                
                QTabBar::tab:selected {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {AppStyles.PRIMARY_COLOR}, stop:1 {AppStyles.HIGHLIGHT_COLOR});
                    color: white;
                    font-weight: bold;
                    border-bottom: none;
                }}
                
                QTabBar::tab:hover:!selected {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #e3f2fd, stop:1 #bbdefb);
                    color: {AppStyles.PRIMARY_COLOR};
                }}
            """)
            
            # تبويب الإعدادات العامة
            general_tab = self.create_general_settings_tab()
            tabs_widget.addTab(general_tab, "🏢 الإعدادات العامة")
            
            # تبويب إعدادات المظهر
            appearance_tab = self.create_appearance_settings_tab()
            tabs_widget.addTab(appearance_tab, "🎨 المظهر والواجهة")
            
            # تبويب إعدادات قاعدة البيانات
            database_tab = self.create_database_settings_tab()
            tabs_widget.addTab(database_tab, "🗄️ قاعدة البيانات")
            
            # تبويب إعدادات النسخ الاحتياطي
            backup_tab = self.create_backup_settings_tab()
            tabs_widget.addTab(backup_tab, "💾 النسخ الاحتياطي")
            
            # تبويب إعدادات الطباعة
            print_tab = self.create_print_settings_tab()
            tabs_widget.addTab(print_tab, "🖨️ الطباعة")
            
            return tabs_widget
        except Exception as e:
            print("Error in create_settings_tabs:", e)
            return QTabWidget()
    
    def create_general_settings_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        # معلومات الشركة
        company_group = QGroupBox("معلومات الشركة")
        company_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                font-weight: bold;
                color: {AppStyles.PRIMARY_COLOR};
                border: 2px solid {AppStyles.BORDER_COLOR};
                border-radius: {AppStyles.BORDER_RADIUS}px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        company_layout = QFormLayout(company_group)
        
        # اسم الشركة
        self.company_name = QLineEdit("شركة المحاسبة المتقدمة")
        self.company_name.setStyleSheet(AppStyles.INPUT_STYLE)
        
        # عنوان الشركة
        self.company_address = QTextEdit("القاهرة، مصر")
        self.company_address.setStyleSheet(AppStyles.INPUT_STYLE)
        self.company_address.setMaximumHeight(80)
        
        # هاتف الشركة
        self.company_phone = QLineEdit("01234567890")
        self.company_phone.setStyleSheet(AppStyles.INPUT_STYLE)
        
        # البريد الإلكتروني
        self.company_email = QLineEdit("<EMAIL>")
        self.company_email.setStyleSheet(AppStyles.INPUT_STYLE)
        
        company_layout.addRow("اسم الشركة:", self.company_name)
        company_layout.addRow("العنوان:", self.company_address)
        company_layout.addRow("الهاتف:", self.company_phone)
        company_layout.addRow("البريد الإلكتروني:", self.company_email)
        
        # إعدادات العملة
        currency_group = QGroupBox("إعدادات العملة")
        currency_group.setStyleSheet(company_group.styleSheet())
        currency_layout = QFormLayout(currency_group)
        
        # العملة الافتراضية
        self.default_currency = QComboBox()
        self.default_currency.addItems(["جنيه مصري (ج.م)", "دولار أمريكي ($)", "يورو (€)", "ريال سعودي (ر.س)"])
        self.default_currency.setStyleSheet(AppStyles.COMBOBOX_STYLE)
        
        # عدد الخانات العشرية
        self.decimal_places = QSpinBox()
        self.decimal_places.setRange(0, 5)
        self.decimal_places.setValue(2)
        self.decimal_places.setStyleSheet(AppStyles.INPUT_STYLE)
        
        currency_layout.addRow("العملة الافتراضية:", self.default_currency)
        currency_layout.addRow("عدد الخانات العشرية:", self.decimal_places)
        
        # إعدادات الضرائب
        tax_group = QGroupBox("إعدادات الضرائب")
        tax_group.setStyleSheet(company_group.styleSheet())
        tax_layout = QFormLayout(tax_group)
        
        # معدل الضريبة الافتراضي
        self.default_tax_rate = QDoubleSpinBox()
        self.default_tax_rate.setRange(0, 100)
        self.default_tax_rate.setValue(14)
        self.default_tax_rate.setSuffix("%")
        self.default_tax_rate.setStyleSheet(AppStyles.INPUT_STYLE)
        
        # تفعيل الضرائب
        self.enable_tax = QCheckBox("تفعيل حساب الضرائب تلقائياً")
        self.enable_tax.setChecked(True)
        self.enable_tax.setStyleSheet(f"""
            QCheckBox {{
                color: {AppStyles.TEXT_PRIMARY};
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
            }}
        """)
        
        tax_layout.addRow("معدل الضريبة الافتراضي:", self.default_tax_rate)
        tax_layout.addRow("", self.enable_tax)
        
        layout.addWidget(company_group)
        layout.addWidget(currency_group)
        layout.addWidget(tax_group)
        layout.addStretch()
        
        return tab
    
    def create_appearance_settings_tab(self):
        """إنشاء تبويب إعدادات المظهر"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        # إعدادات السمة
        theme_group = QGroupBox("السمة والألوان")
        theme_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                font-weight: bold;
                color: {AppStyles.PRIMARY_COLOR};
                border: 2px solid {AppStyles.BORDER_COLOR};
                border-radius: {AppStyles.BORDER_RADIUS}px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        theme_layout = QFormLayout(theme_group)
        
        # اختيار السمة
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["سمة فاتحة", "سمة داكنة", "سمة زرقاء", "سمة خضراء"])
        self.theme_combo.setStyleSheet(AppStyles.COMBOBOX_STYLE)
        
        # اللون الأساسي
        self.primary_color_btn = QPushButton("اختيار اللون الأساسي")
        self.primary_color_btn.setStyleSheet(AppStyles.BUTTON_STYLE)
        self.primary_color_btn.clicked.connect(self.choose_primary_color)
        
        # اللون الثانوي
        self.secondary_color_btn = QPushButton("اختيار اللون الثانوي")
        self.secondary_color_btn.setStyleSheet(AppStyles.BUTTON_SECONDARY_STYLE)
        self.secondary_color_btn.clicked.connect(self.choose_secondary_color)
        
        theme_layout.addRow("السمة:", self.theme_combo)
        theme_layout.addRow("اللون الأساسي:", self.primary_color_btn)
        theme_layout.addRow("اللون الثانوي:", self.secondary_color_btn)
        
        # إعدادات الخط
        font_group = QGroupBox("إعدادات الخط")
        font_group.setStyleSheet(theme_group.styleSheet())
        font_layout = QFormLayout(font_group)
        
        # حجم الخط
        self.font_size = QSlider(Qt.Horizontal)
        self.font_size.setRange(10, 20)
        self.font_size.setValue(14)
        self.font_size.setTickPosition(QSlider.TicksBelow)
        self.font_size.setTickInterval(2)
        
        self.font_size_label = QLabel("14px")
        self.font_size.valueChanged.connect(lambda v: self.font_size_label.setText(f"{v}px"))
        
        font_size_widget = QWidget()
        font_size_layout = QHBoxLayout(font_size_widget)
        font_size_layout.addWidget(self.font_size)
        font_size_layout.addWidget(self.font_size_label)
        
        # نوع الخط
        self.font_family = QComboBox()
        self.font_family.addItems(["Arial", "Tahoma", "Calibri", "Times New Roman", "Georgia"])
        self.font_family.setStyleSheet(AppStyles.COMBOBOX_STYLE)
        
        font_layout.addRow("حجم الخط:", font_size_widget)
        font_layout.addRow("نوع الخط:", self.font_family)
        
        # إعدادات النوافذ
        window_group = QGroupBox("إعدادات النوافذ")
        window_group.setStyleSheet(theme_group.styleSheet())
        window_layout = QFormLayout(window_group)
        
        # شفافية النوافذ
        self.window_opacity = QSlider(Qt.Horizontal)
        self.window_opacity.setRange(70, 100)
        self.window_opacity.setValue(100)
        self.window_opacity.setTickPosition(QSlider.TicksBelow)
        self.window_opacity.setTickInterval(10)
        
        self.opacity_label = QLabel("100%")
        self.window_opacity.valueChanged.connect(lambda v: self.opacity_label.setText(f"{v}%"))
        
        opacity_widget = QWidget()
        opacity_layout = QHBoxLayout(opacity_widget)
        opacity_layout.addWidget(self.window_opacity)
        opacity_layout.addWidget(self.opacity_label)
        
        # تأثيرات بصرية
        self.enable_animations = QCheckBox("تفعيل التأثيرات المتحركة")
        self.enable_animations.setChecked(True)
        
        self.enable_shadows = QCheckBox("تفعيل الظلال")
        self.enable_shadows.setChecked(True)
        
        window_layout.addRow("شفافية النوافذ:", opacity_widget)
        window_layout.addRow("", self.enable_animations)
        window_layout.addRow("", self.enable_shadows)
        
        layout.addWidget(theme_group)
        layout.addWidget(font_group)
        layout.addWidget(window_group)
        layout.addStretch()
        
        return tab
    
    def create_database_settings_tab(self):
        """إنشاء تبويب إعدادات قاعدة البيانات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        # معلومات قاعدة البيانات
        db_info_group = QGroupBox("معلومات قاعدة البيانات")
        db_info_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                font-weight: bold;
                color: {AppStyles.PRIMARY_COLOR};
                border: 2px solid {AppStyles.BORDER_COLOR};
                border-radius: {AppStyles.BORDER_RADIUS}px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        db_info_layout = QFormLayout(db_info_group)
        
        # مسار قاعدة البيانات
        db_path_widget = QWidget()
        db_path_layout = QHBoxLayout(db_path_widget)
        
        self.db_path = QLineEdit("accounting.db")
        self.db_path.setStyleSheet(AppStyles.INPUT_STYLE)
        self.db_path.setReadOnly(True)
        
        browse_db_btn = QPushButton("استعراض")
        browse_db_btn.setStyleSheet(AppStyles.BUTTON_STYLE)
        browse_db_btn.clicked.connect(self.browse_database_path)
        
        db_path_layout.addWidget(self.db_path)
        db_path_layout.addWidget(browse_db_btn)
        
        # حجم قاعدة البيانات
        self.db_size = QLabel("2.5 MB")
        self.db_size.setStyleSheet(f"color: {AppStyles.TEXT_SECONDARY};")
        
        # عدد الجداول
        self.db_tables = QLabel("12 جدول")
        self.db_tables.setStyleSheet(f"color: {AppStyles.TEXT_SECONDARY};")
        
        db_info_layout.addRow("مسار قاعدة البيانات:", db_path_widget)
        db_info_layout.addRow("حجم قاعدة البيانات:", self.db_size)
        db_info_layout.addRow("عدد الجداول:", self.db_tables)
        
        # إعدادات الأداء
        performance_group = QGroupBox("إعدادات الأداء")
        performance_group.setStyleSheet(db_info_group.styleSheet())
        performance_layout = QFormLayout(performance_group)
        
        # حجم ذاكرة التخزين المؤقت
        self.cache_size = QSpinBox()
        self.cache_size.setRange(1, 100)
        self.cache_size.setValue(10)
        self.cache_size.setSuffix(" MB")
        self.cache_size.setStyleSheet(AppStyles.INPUT_STYLE)
        
        # تفعيل الفهرسة التلقائية
        self.auto_index = QCheckBox("تفعيل الفهرسة التلقائية")
        self.auto_index.setChecked(True)
        
        # تحسين قاعدة البيانات
        optimize_btn = QPushButton("تحسين قاعدة البيانات")
        optimize_btn.setStyleSheet(AppStyles.BUTTON_STYLE)
        optimize_btn.clicked.connect(self.optimize_database)
        
        performance_layout.addRow("حجم ذاكرة التخزين المؤقت:", self.cache_size)
        performance_layout.addRow("", self.auto_index)
        performance_layout.addRow("", optimize_btn)
        
        layout.addWidget(db_info_group)
        layout.addWidget(performance_group)
        layout.addStretch()
        
        return tab
    
    def create_backup_settings_tab(self):
        """إنشاء تبويب إعدادات النسخ الاحتياطي"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        # إعدادات النسخ الاحتياطي التلقائي
        auto_backup_group = QGroupBox("النسخ الاحتياطي التلقائي")
        auto_backup_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                font-weight: bold;
                color: {AppStyles.PRIMARY_COLOR};
                border: 2px solid {AppStyles.BORDER_COLOR};
                border-radius: {AppStyles.BORDER_RADIUS}px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        auto_backup_layout = QFormLayout(auto_backup_group)
        
        # تفعيل النسخ الاحتياطي التلقائي
        self.enable_auto_backup = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        self.enable_auto_backup.setChecked(True)
        
        # تكرار النسخ الاحتياطي
        self.backup_frequency = QComboBox()
        self.backup_frequency.addItems(["يومياً", "أسبوعياً", "شهرياً", "عند إغلاق البرنامج"])
        self.backup_frequency.setStyleSheet(AppStyles.COMBOBOX_STYLE)
        
        # مجلد النسخ الاحتياطي
        backup_folder_widget = QWidget()
        backup_folder_layout = QHBoxLayout(backup_folder_widget)
        
        self.backup_folder = QLineEdit("./backups")
        self.backup_folder.setStyleSheet(AppStyles.INPUT_STYLE)
        
        browse_folder_btn = QPushButton("استعراض")
        browse_folder_btn.setStyleSheet(AppStyles.BUTTON_STYLE)
        browse_folder_btn.clicked.connect(self.browse_backup_folder)
        
        backup_folder_layout.addWidget(self.backup_folder)
        backup_folder_layout.addWidget(browse_folder_btn)
        
        # عدد النسخ المحفوظة
        self.max_backups = QSpinBox()
        self.max_backups.setRange(1, 50)
        self.max_backups.setValue(10)
        self.max_backups.setStyleSheet(AppStyles.INPUT_STYLE)
        
        auto_backup_layout.addRow("", self.enable_auto_backup)
        auto_backup_layout.addRow("تكرار النسخ:", self.backup_frequency)
        auto_backup_layout.addRow("مجلد النسخ:", backup_folder_widget)
        auto_backup_layout.addRow("عدد النسخ المحفوظة:", self.max_backups)
        
        # عمليات النسخ الاحتياطي اليدوية
        manual_backup_group = QGroupBox("النسخ الاحتياطي اليدوي")
        manual_backup_group.setStyleSheet(auto_backup_group.styleSheet())
        manual_backup_layout = QVBoxLayout(manual_backup_group)
        
        # شريط التقدم
        self.backup_progress = QProgressBar()
        self.backup_progress.setVisible(False)
        
        # أزرار العمليات
        buttons_widget = QWidget()
        buttons_layout = QHBoxLayout(buttons_widget)
        
        create_backup_btn = QPushButton("إنشاء نسخة احتياطية")
        create_backup_btn.setStyleSheet(AppStyles.BUTTON_STYLE)
        create_backup_btn.clicked.connect(self.create_backup)
        
        restore_backup_btn = QPushButton("استعادة نسخة احتياطية")
        restore_backup_btn.setStyleSheet(AppStyles.BUTTON_SECONDARY_STYLE)
        restore_backup_btn.clicked.connect(self.restore_backup)
        
        buttons_layout.addWidget(create_backup_btn)
        buttons_layout.addWidget(restore_backup_btn)
        
        manual_backup_layout.addWidget(self.backup_progress)
        manual_backup_layout.addWidget(buttons_widget)
        
        layout.addWidget(auto_backup_group)
        layout.addWidget(manual_backup_group)
        layout.addStretch()
        
        return tab
    
    def create_print_settings_tab(self):
        """إنشاء تبويب إعدادات الطباعة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        # إعدادات الطابعة
        printer_group = QGroupBox("إعدادات الطابعة")
        printer_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                font-weight: bold;
                color: {AppStyles.PRIMARY_COLOR};
                border: 2px solid {AppStyles.BORDER_COLOR};
                border-radius: {AppStyles.BORDER_RADIUS}px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        printer_layout = QFormLayout(printer_group)
        
        # الطابعة الافتراضية
        self.default_printer = QComboBox()
        self.default_printer.addItems(["طابعة النظام الافتراضية", "HP LaserJet", "Canon PIXMA", "Brother HL"])
        self.default_printer.setStyleSheet(AppStyles.COMBOBOX_STYLE)
        
        # حجم الورق
        self.paper_size = QComboBox()
        self.paper_size.addItems(["A4", "A5", "Letter", "Legal"])
        self.paper_size.setStyleSheet(AppStyles.COMBOBOX_STYLE)
        
        # اتجاه الطباعة
        self.orientation = QComboBox()
        self.orientation.addItems(["عمودي", "أفقي"])
        self.orientation.setStyleSheet(AppStyles.COMBOBOX_STYLE)
        
        printer_layout.addRow("الطابعة الافتراضية:", self.default_printer)
        printer_layout.addRow("حجم الورق:", self.paper_size)
        printer_layout.addRow("اتجاه الطباعة:", self.orientation)
        
        # إعدادات التقارير
        reports_group = QGroupBox("إعدادات التقارير")
        reports_group.setStyleSheet(printer_group.styleSheet())
        reports_layout = QFormLayout(reports_group)
        
        # تضمين الشعار
        self.include_logo = QCheckBox("تضمين شعار الشركة")
        self.include_logo.setChecked(True)
        
        # تضمين التاريخ والوقت
        self.include_datetime = QCheckBox("تضمين التاريخ والوقت")
        self.include_datetime.setChecked(True)
        
        # رقم الصفحة
        self.include_page_numbers = QCheckBox("تضمين أرقام الصفحات")
        self.include_page_numbers.setChecked(True)
        
        reports_layout.addRow("", self.include_logo)
        reports_layout.addRow("", self.include_datetime)
        reports_layout.addRow("", self.include_page_numbers)
        
        layout.addWidget(printer_group)
        layout.addWidget(reports_group)
        layout.addStretch()
        
        return tab
    
    def create_buttons_frame(self):
        """إنشاء إطار الأزرار"""
        try:
            buttons_frame = QFrame()
            buttons_frame.setStyleSheet(f"""
                background-color: white;
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 20px;
                border: 1px solid {AppStyles.BORDER_COLOR};
            """)
            
            buttons_layout = QHBoxLayout(buttons_frame)
            
            # زر الحفظ
            save_btn = QPushButton("حفظ الإعدادات")
            save_btn.setStyleSheet(AppStyles.BUTTON_STYLE)
            save_btn.clicked.connect(self.save_settings)
            
            # زر الإلغاء
            cancel_btn = QPushButton("إلغاء")
            cancel_btn.setStyleSheet(AppStyles.BUTTON_SECONDARY_STYLE)
            cancel_btn.clicked.connect(self.cancel_settings)
            
            # زر إعادة التعيين
            reset_btn = QPushButton("إعادة تعيين افتراضية")
            reset_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {AppStyles.WARNING_COLOR};
                    color: white;
                    border: none;
                    border-radius: {AppStyles.BORDER_RADIUS}px;
                    padding: 10px 20px;
                    font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: #F57C00;
                }}
            """)
            reset_btn.clicked.connect(self.reset_settings)
            
            buttons_layout.addStretch()
            buttons_layout.addWidget(reset_btn)
            buttons_layout.addWidget(cancel_btn)
            buttons_layout.addWidget(save_btn)
            
            return buttons_frame
        except Exception as e:
            print("Error in create_buttons_frame:", e)
            return QFrame()
    
    def choose_primary_color(self):
        """اختيار اللون الأساسي"""
        try:
            color = QColorDialog.getColor(QColor(AppStyles.PRIMARY_COLOR), self, "اختيار اللون الأساسي")
            if color.isValid():
                self.primary_color_btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color.name()};
                        color: white;
                        border: none;
                        border-radius: {AppStyles.BORDER_RADIUS}px;
                        padding: 10px 20px;
                        font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                        font-weight: bold;
                    }}
                """)
        except Exception as e:
            print("Error in choose_primary_color:", e)
    
    def choose_secondary_color(self):
        """اختيار اللون الثانوي"""
        try:
            color = QColorDialog.getColor(QColor(AppStyles.SECONDARY_COLOR), self, "اختيار اللون الثانوي")
            if color.isValid():
                self.secondary_color_btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color.name()};
                        color: white;
                        border: none;
                        border-radius: {AppStyles.BORDER_RADIUS}px;
                        padding: 10px 20px;
                        font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                        font-weight: bold;
                    }}
                """)
        except Exception as e:
            print("Error in choose_secondary_color:", e)
    
    def browse_database_path(self):
        """استعراض مسار قاعدة البيانات"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(self, "اختيار قاعدة البيانات", "", "Database Files (*.db)")
            if file_path:
                self.db_path.setText(file_path)
        except Exception as e:
            print("Error in browse_database_path:", e)
    
    def browse_backup_folder(self):
        """استعراض مجلد النسخ الاحتياطي"""
        try:
            folder_path = QFileDialog.getExistingDirectory(self, "اختيار مجلد النسخ الاحتياطي")
            if folder_path:
                self.backup_folder.setText(folder_path)
        except Exception as e:
            print("Error in browse_backup_folder:", e)
    
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            QMessageBox.information(self, "تحسين قاعدة البيانات", "تم تحسين قاعدة البيانات بنجاح!")
        except Exception as e:
            print("Error in optimize_database:", e)
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            self.backup_progress.setVisible(True)
            self.backup_progress.setValue(0)
            
            # محاكاة عملية النسخ الاحتياطي
            timer = QTimer()
            progress = 0
            
            def update_progress():
                nonlocal progress
                progress += 10
                self.backup_progress.setValue(progress)
                if progress >= 100:
                    timer.stop()
                    self.backup_progress.setVisible(False)
                    QMessageBox.information(self, "نسخ احتياطي", "تم إنشاء النسخة الاحتياطية بنجاح!")
            
            timer.timeout.connect(update_progress)
            timer.start(200)
            
        except Exception as e:
            print("Error in create_backup:", e)
    
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(self, "اختيار النسخة الاحتياطية", "", "Backup Files (*.bak)")
            if file_path:
                QMessageBox.information(self, "استعادة", "تم استعادة النسخة الاحتياطية بنجاح!")
        except Exception as e:
            print("Error in restore_backup:", e)
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            QMessageBox.information(self, "حفظ الإعدادات", "تم حفظ جميع الإعدادات بنجاح!")
        except Exception as e:
            print("Error in save_settings:", e)
    
    def cancel_settings(self):
        """إلغاء الإعدادات"""
        try:
            reply = QMessageBox.question(self, "إلغاء", "هل تريد إلغاء التغييرات؟",
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)
            if reply == QMessageBox.Yes:
                # إعادة تحميل الإعدادات الافتراضية
                pass
        except Exception as e:
            print("Error in cancel_settings:", e)
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات الافتراضية"""
        try:
            reply = QMessageBox.question(self, "إعادة تعيين", "هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)
            if reply == QMessageBox.Yes:
                # إعادة تعيين القيم
                self.company_name.setText("شركة المحاسبة المتقدمة")
                self.default_currency.setCurrentIndex(0)
                self.decimal_places.setValue(2)
                self.default_tax_rate.setValue(14)
                self.enable_tax.setChecked(True)
                QMessageBox.information(self, "إعادة تعيين", "تم إعادة تعيين الإعدادات بنجاح!")
        except Exception as e:
            print("Error in reset_settings:", e)