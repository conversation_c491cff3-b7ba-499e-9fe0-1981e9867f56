"""
لوحة تحكم محسنة مع عرض بيانات مفصل وأداء محسن
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QGridLayout, QProgressBar,
                              QScrollArea, QGroupBox, QSizePolicy, QTableWidget,
                              QTableWidgetItem, QHeaderView, QSplitter)
from PySide6.QtCore import Qt, QTimer, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QFont, QColor, QPainter, QPen, QBrush, QLinearGradient
import random
import time
from datetime import datetime, timedelta
from accounting_app.ui.styles import AppStyles


class DataLoader(QThread):
    """Worker thread لتحميل البيانات بدون تجميد الواجهة"""
    dataLoaded = pyqtSignal(dict)
    progressUpdate = pyqtSignal(int, str)
    
    def run(self):
        """تحميل البيانات التجريبية"""
        try:
            # محاكاة تحميل البيانات
            data_sources = [
                ("المبيعات", "🛒"),
                ("المخزون", "📦"),
                ("العملاء", "👥"),
                ("التقارير المالية", "💰"),
                ("الموردين", "🏢"),
                ("الفواتير", "🧾")
            ]
            
            total_steps = len(data_sources)
            loaded_data = {}
            
            for i, (source, icon) in enumerate(data_sources):
                self.progressUpdate.emit(int((i / total_steps) * 100), f"تحميل {source}...")
                
                # محاكاة وقت التحميل
                self.msleep(300)
                
                # إنشاء بيانات تجريبية
                loaded_data[source] = self.generate_sample_data(source)
            
            self.progressUpdate.emit(100, "تم التحميل بنجاح!")
            self.dataLoaded.emit(loaded_data)
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def generate_sample_data(self, source):
        """إنشاء بيانات تجريبية حسب المصدر"""
        if source == "المبيعات":
            return {
                "total": 125000.75,
                "currency": "ج.م",
                "change": 12.5,
                "trend": "up",
                "details": {
                    "today": 1250.50,
                    "week": 8750.25,
                    "month": 45000.75,
                    "orders_count": 89,
                    "avg_order": 1404.49
                }
            }
        elif source == "المخزون":
            return {
                "total": 15000,
                "unit": "قطعة",
                "change": -2.3,
                "trend": "down",
                "details": {
                    "low_stock": 23,
                    "out_of_stock": 5,
                    "total_products": 450,
                    "value": 89000.50
                }
            }
        elif source == "العملاء":
            return {
                "total": 1247,
                "unit": "عميل",
                "change": 5.8,
                "trend": "up",
                "details": {
                    "new_month": 48,
                    "active": 892,
                    "vip": 156,
                    "total_orders": 3247
                }
            }
        elif source == "التقارير المالية":
            return {
                "total": 78500.25,
                "currency": "ج.م",
                "change": 18.7,
                "trend": "up",
                "details": {
                    "revenue": 125000.75,
                    "expenses": 46500.50,
                    "profit": 78500.25,
                    "profit_margin": 62.8
                }
            }
        elif source == "الموردين":
            return {
                "total": 89,
                "unit": "مورد",
                "change": 3.2,
                "trend": "up",
                "details": {
                    "active": 67,
                    "new": 8,
                    "total_purchases": 234500.75,
                    "avg_rating": 4.2
                }
            }
        elif source == "الفواتير":
            return {
                "total": 456,
                "unit": "فاتورة",
                "change": 8.9,
                "trend": "up",
                "details": {
                    "paid": 398,
                    "pending": 58,
                    "overdue": 12,
                    "total_amount": 125000.75
                }
            }
        return {}


class AnimatedCard(QFrame):
    """بطاقة متحركة للإحصائيات"""
    
    def __init__(self, title, value, unit, change, trend, icon, color, parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.unit = unit
        self.change = change
        self.trend = trend
        self.icon = icon
        self.color = color
        self.setup_ui()
        self.setup_animation()
    
    def setup_ui(self):
        """إعداد واجهة البطاقة"""
        self.setFixedHeight(140)
        self.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}, stop:1 {self.darken_color(self.color)});
                border-radius: 12px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }}
            QFrame:hover {{
                border: 2px solid rgba(255, 255, 255, 0.4);
            }}
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        
        # الجانب الأيسر - المعلومات
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(5)
        
        # العنوان
        title_label = QLabel(self.title)
        title_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            font-weight: 600;
        """)
        
        # القيمة الرئيسية
        self.value_label = QLabel(f"{self.format_number(self.value)} {self.unit}")
        self.value_label.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin: 5px 0;
        """)
        
        # التغيير
        trend_icon = "📈" if self.trend == "up" else "📉"
        trend_color = "#4CAF50" if self.trend == "up" else "#F44336"
        change_sign = "+" if self.change > 0 else ""
        
        change_label = QLabel(f"{trend_icon} {change_sign}{self.change}%")
        change_label.setStyleSheet(f"""
            color: {trend_color};
            font-size: 12px;
            font-weight: bold;
            background: rgba(255, 255, 255, 0.9);
            padding: 4px 8px;
            border-radius: 8px;
        """)
        change_label.setFixedWidth(80)
        change_label.setAlignment(Qt.AlignCenter)
        
        info_layout.addWidget(title_label)
        info_layout.addWidget(self.value_label)
        info_layout.addWidget(change_label)
        info_layout.addStretch()
        
        # الجانب الأيمن - الأيقونة
        icon_label = QLabel(self.icon)
        icon_label.setStyleSheet("""
            font-size: 48px;
            color: rgba(255, 255, 255, 0.8);
            background: rgba(255, 255, 255, 0.1);
            border-radius: 30px;
            padding: 15px;
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setFixedSize(80, 80)
        
        layout.addWidget(info_widget, 7)
        layout.addWidget(icon_label, 3)
        
        # إضافة مؤشر النقر
        self.setCursor(Qt.PointingHandCursor)
    
    def setup_animation(self):
        """إعداد الأنيميشن"""
        self.animation = QPropertyAnimation(self, b"pos")
        self.animation.setEasingCurve(QEasingCurve.OutBounce)
        self.animation.setDuration(300)
    
    def format_number(self, number):
        """تنسيق الأرقام مع فواصل الآلاف"""
        if isinstance(number, (int, float)):
            if number >= 1000000:
                return f"{number/1000000:.1f}م"
            elif number >= 1000:
                return f"{number/1000:.1f}ك"
            else:
                return f"{number:,.1f}"
        return str(number)
    
    def darken_color(self, color, factor=0.2):
        """تظليل اللون"""
        try:
            color_obj = QColor(color)
            h, s, l, a = color_obj.getHslF()
            return QColor.fromHslF(h, s, max(0, l - factor), a).name()
        except:
            return "#333333"
    
    def mousePressEvent(self, event):
        """معالج النقر"""
        if event.button() == Qt.LeftButton:
            # إنشاء نافذة تفاصيل (يمكن تطويرها لاحقاً)
            from PySide6.QtWidgets import QMessageBox
            details = f"""
            📊 تفاصيل {self.title}
            
            القيمة الحالية: {self.format_number(self.value)} {self.unit}
            التغيير: {self.change}%
            الاتجاه: {'صاعد' if self.trend == 'up' else 'هابط'}
            """
            QMessageBox.information(self, f"تفاصيل {self.title}", details)
        super().mousePressEvent(event)


class ChartWidget(QWidget):
    """رسم بياني بسيط"""
    
    def __init__(self, title, data, chart_type="line", parent=None):
        super().__init__(parent)
        self.title = title
        self.data = data
        self.chart_type = chart_type
        self.setMinimumHeight(200)
        self.setStyleSheet("""
            background: white;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        """)
    
    def paintEvent(self, event):
        """رسم الرسم البياني"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # خلفية
        painter.fillRect(self.rect(), QColor("#ffffff"))
        
        # العنوان
        painter.setPen(QPen(QColor("#333333"), 2))
        painter.setFont(QFont("Arial", 14, QFont.Bold))
        painter.drawText(20, 30, self.title)
        
        if not self.data:
            painter.drawText(self.width()//2 - 50, self.height()//2, "لا توجد بيانات")
            return
        
        # إعدادات الرسم
        margin = 40
        chart_width = self.width() - 2 * margin
        chart_height = self.height() - 2 * margin - 40
        
        if self.chart_type == "line":
            self.draw_line_chart(painter, margin, chart_width, chart_height)
        elif self.chart_type == "bar":
            self.draw_bar_chart(painter, margin, chart_width, chart_height)
    
    def draw_line_chart(self, painter, margin, width, height):
        """رسم خط بياني"""
        if len(self.data) < 2:
            return
        
        # تحديد النقاط
        points = []
        max_val = max(self.data.values()) if self.data.values() else 1
        min_val = min(self.data.values()) if self.data.values() else 0
        
        step_x = width / (len(self.data) - 1) if len(self.data) > 1 else width
        
        for i, (key, value) in enumerate(self.data.items()):
            x = margin + i * step_x
            y = margin + 40 + height - ((value - min_val) / (max_val - min_val) * height)
            points.append((x, y))
        
        # رسم الخط
        painter.setPen(QPen(QColor("#2196F3"), 3))
        for i in range(len(points) - 1):
            painter.drawLine(points[i][0], points[i][1], points[i+1][0], points[i+1][1])
        
        # رسم النقاط
        painter.setBrush(QBrush(QColor("#2196F3")))
        for x, y in points:
            painter.drawEllipse(x - 4, y - 4, 8, 8)
    
    def draw_bar_chart(self, painter, margin, width, height):
        """رسم أعمدة بيانية"""
        if not self.data:
            return
        
        bar_width = width / len(self.data) - 10
        max_val = max(self.data.values()) if self.data.values() else 1
        
        colors = ["#2196F3", "#4CAF50", "#FF9800", "#9C27B0", "#F44336"]
        
        for i, (key, value) in enumerate(self.data.items()):
            x = margin + i * (bar_width + 10)
            bar_height = (value / max_val) * height
            y = margin + 40 + height - bar_height
            
            # رسم العمود
            color = colors[i % len(colors)]
            painter.fillRect(x, y, bar_width, bar_height, QColor(color))
            
            # رسم القيمة
            painter.setPen(QPen(QColor("#333333"), 1))
            painter.drawText(x, y - 5, str(int(value)))


class EnhancedDashboard(QWidget):
    """لوحة تحكم محسنة"""
    
    def __init__(self, user_data, parent=None):
        super().__init__(parent)
        self.user_data = user_data
        self.data = {}
        self.cards = []
        self.loading = False
        self.init_ui()
        self.load_dashboard_data()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # شريط التحميل
        self.loading_widget = self.create_loading_widget()
        main_layout.addWidget(self.loading_widget)
        
        # المحتوى الرئيسي
        content_scroll = QScrollArea()
        content_scroll.setWidgetResizable(True)
        content_scroll.setStyleSheet("border: none;")
        
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setSpacing(20)
        
        content_scroll.setWidget(self.content_widget)
        main_layout.addWidget(content_scroll)
        
        # إخفاء المحتوى أثناء التحميل
        content_scroll.setVisible(False)
        self.content_scroll = content_scroll
    
    def create_header(self):
        """إنشاء رأس لوحة التحكم"""
        header_frame = QFrame()
        header_frame.setFixedHeight(120)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #667eea);
                border-radius: 15px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # معلومات المستخدم
        user_info = QWidget()
        user_layout = QVBoxLayout(user_info)
        
        welcome_label = QLabel(f"مرحباً بك، {self.user_data[3] if len(self.user_data) > 3 and self.user_data[3] else 'المستخدم'}!")
        welcome_label.setStyleSheet("""
            color: white;
            font-size: 22px;
            font-weight: bold;
        """)
        
        date_label = QLabel(f"📅 اليوم: {datetime.now().strftime('%Y/%m/%d - %A')}")
        date_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-top: 5px;
        """)
        
        status_label = QLabel("🟢 النظام يعمل بكفاءة عالية")
        status_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-top: 5px;
        """)
        
        user_layout.addWidget(welcome_label)
        user_layout.addWidget(date_label)
        user_layout.addWidget(status_label)
        
        # أزرار سريعة
        quick_actions = QWidget()
        actions_layout = QHBoxLayout(quick_actions)
        actions_layout.setSpacing(10)
        
        quick_buttons = [
            ("💰 مبيعة سريعة", "#4CAF50"),
            ("📦 جرد سريع", "#2196F3"),
            ("📊 تقرير فوري", "#FF9800"),
            ("🔄 تحديث البيانات", "#9C27B0")
        ]
        
        for text, color in quick_buttons:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 8px 12px;
                    font-size: 12px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
            """)
            btn.setFixedHeight(35)
            actions_layout.addWidget(btn)
        
        layout.addWidget(user_info, 7)
        layout.addWidget(quick_actions, 3)
        
        return header_frame
    
    def create_loading_widget(self):
        """إنشاء واجهة التحميل"""
        loading_frame = QFrame()
        loading_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 1px solid #e0e0e0;
                padding: 30px;
            }
        """)
        loading_frame.setFixedHeight(150)
        
        layout = QVBoxLayout(loading_frame)
        layout.setAlignment(Qt.AlignCenter)
        
        # أيقونة التحميل
        loading_icon = QLabel("⏳")
        loading_icon.setStyleSheet("font-size: 48px;")
        loading_icon.setAlignment(Qt.AlignCenter)
        
        # رسالة التحميل
        self.loading_message = QLabel("جاري تحميل بيانات لوحة التحكم...")
        self.loading_message.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
            margin: 10px 0;
        """)
        self.loading_message.setAlignment(Qt.AlignCenter)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                color: #495057;
                background: #f8f9fa;
            }
            QProgressBar::chunk {
                background-color: #007bff;
                border-radius: 6px;
            }
        """)
        self.progress_bar.setFixedHeight(25)
        
        layout.addWidget(loading_icon)
        layout.addWidget(self.loading_message)
        layout.addWidget(self.progress_bar)
        
        return loading_frame
    
    def load_dashboard_data(self):
        """تحميل بيانات لوحة التحكم"""
        if self.loading:
            return
        
        self.loading = True
        self.loading_widget.setVisible(True)
        self.content_scroll.setVisible(False)
        
        # إنشاء worker thread
        self.data_loader = DataLoader()
        self.data_loader.dataLoaded.connect(self.on_data_loaded)
        self.data_loader.progressUpdate.connect(self.on_progress_update)
        self.data_loader.start()
    
    def on_progress_update(self, progress, message):
        """تحديث التقدم"""
        self.progress_bar.setValue(progress)
        self.loading_message.setText(message)
    
    def on_data_loaded(self, data):
        """معالجة البيانات المحملة"""
        self.data = data
        self.loading = False
        
        # إخفاء شاشة التحميل
        self.loading_widget.setVisible(False)
        
        # إنشاء المحتوى
        self.create_dashboard_content()
        
        # إظهار المحتوى مع تأثير
        self.content_scroll.setVisible(True)
    
    def create_dashboard_content(self):
        """إنشاء محتوى لوحة التحكم"""
        # مسح المحتوى السابق
        for i in reversed(range(self.content_layout.count())):
            self.content_layout.itemAt(i).widget().setParent(None)
        
        # بطاقات الإحصائيات الرئيسية
        stats_section = self.create_stats_section()
        self.content_layout.addWidget(stats_section)
        
        # الرسوم البيانية
        charts_section = self.create_charts_section()
        self.content_layout.addWidget(charts_section)
        
        # الجداول والتفاصيل
        details_section = self.create_details_section()
        self.content_layout.addWidget(details_section)
    
    def create_stats_section(self):
        """إنشاء قسم الإحصائيات"""
        section_frame = QFrame()
        section_frame.setStyleSheet("background: transparent;")
        
        layout = QVBoxLayout(section_frame)
        layout.setSpacing(15)
        
        # عنوان القسم
        title_label = QLabel("📊 الإحصائيات الرئيسية")
        title_label.setStyleSheet("""
            color: #495057;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)
        
        # شبكة البطاقات
        cards_widget = QWidget()
        cards_layout = QGridLayout(cards_widget)
        cards_layout.setSpacing(15)
        
        # تحديد البطاقات والألوان
        card_configs = [
            ("المبيعات", "🛒", "#4CAF50"),
            ("المخزون", "📦", "#2196F3"),
            ("العملاء", "👥", "#FF9800"),
            ("التقارير المالية", "💰", "#9C27B0"),
            ("الموردين", "🏢", "#F44336"),
            ("الفواتير", "🧾", "#795548")
        ]
        
        self.cards = []
        row, col = 0, 0
        
        for title, icon, color in card_configs:
            if title in self.data:
                data_item = self.data[title]
                card = AnimatedCard(
                    title=title,
                    value=data_item.get("total", 0),
                    unit=data_item.get("unit", data_item.get("currency", "")),
                    change=data_item.get("change", 0),
                    trend=data_item.get("trend", "up"),
                    icon=icon,
                    color=color
                )
                cards_layout.addWidget(card, row, col)
                self.cards.append(card)
                
                col += 1
                if col >= 3:  # 3 بطاقات في كل صف
                    col = 0
                    row += 1
        
        layout.addWidget(cards_widget)
        return section_frame
    
    def create_charts_section(self):
        """إنشاء قسم الرسوم البيانية"""
        section_frame = QFrame()
        section_frame.setStyleSheet("background: transparent;")
        
        layout = QVBoxLayout(section_frame)
        layout.setSpacing(15)
        
        # عنوان القسم
        title_label = QLabel("📈 الرسوم البيانية")
        title_label.setStyleSheet("""
            color: #495057;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)
        
        # حاوية الرسوم البيانية
        charts_widget = QWidget()
        charts_layout = QHBoxLayout(charts_widget)
        charts_layout.setSpacing(15)
        
        # رسم بياني للمبيعات الشهرية
        sales_data = {
            "يناير": 25000,
            "فبراير": 32000,
            "مارس": 28000,
            "أبريل": 35000,
            "مايو": 42000,
            "يونيو": 38000
        }
        sales_chart = ChartWidget("المبيعات الشهرية", sales_data, "line")
        
        # رسم بياني للمنتجات الأكثر مبيعاً
        products_data = {
            "منتج أ": 150,
            "منتج ب": 120,
            "منتج ج": 90,
            "منتج د": 80,
            "منتج هـ": 70
        }
        products_chart = ChartWidget("المنتجات الأكثر مبيعاً", products_data, "bar")
        
        charts_layout.addWidget(sales_chart)
        charts_layout.addWidget(products_chart)
        
        layout.addWidget(charts_widget)
        return section_frame
    
    def create_details_section(self):
        """إنشاء قسم التفاصيل"""
        section_frame = QFrame()
        section_frame.setStyleSheet("background: transparent;")
        
        layout = QVBoxLayout(section_frame)
        layout.setSpacing(15)
        
        # عنوان القسم
        title_label = QLabel("📋 التفاصيل السريعة")
        title_label.setStyleSheet("""
            color: #495057;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)
        
        # Splitter للتفاصيل
        details_splitter = QSplitter(Qt.Horizontal)
        
        # جدول المعاملات الأخيرة
        transactions_table = self.create_transactions_table()
        details_splitter.addWidget(transactions_table)
        
        # جدول التنبيهات
        alerts_table = self.create_alerts_table()
        details_splitter.addWidget(alerts_table)
        
        details_splitter.setSizes([500, 400])
        layout.addWidget(details_splitter)
        
        return section_frame
    
    def create_transactions_table(self):
        """إنشاء جدول المعاملات الأخيرة"""
        group = QGroupBox("💳 آخر المعاملات")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #495057;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 8px;
                background: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px;
                background: white;
            }
        """)
        
        layout = QVBoxLayout(group)
        
        table = QTableWidget(5, 4)
        table.setHorizontalHeaderLabels(["التاريخ", "النوع", "المبلغ", "الحالة"])
        
        # بيانات تجريبية للمعاملات
        transactions = [
            ("2024-01-15", "مبيعة", "1,250.50 ج.م", "مدفوعة"),
            ("2024-01-15", "شراء", "850.25 ج.م", "معلقة"),
            ("2024-01-14", "مبيعة", "2,100.00 ج.م", "مدفوعة"),
            ("2024-01-14", "مرتجع", "-150.75 ج.م", "مكتملة"),
            ("2024-01-13", "مبيعة", "950.30 ج.م", "مدفوعة")
        ]
        
        for row, (date, type_, amount, status) in enumerate(transactions):
            table.setItem(row, 0, QTableWidgetItem(date))
            table.setItem(row, 1, QTableWidgetItem(type_))
            table.setItem(row, 2, QTableWidgetItem(amount))
            
            status_item = QTableWidgetItem(status)
            if status == "مدفوعة" or status == "مكتملة":
                status_item.setBackground(QColor("#d4edda"))
            elif status == "معلقة":
                status_item.setBackground(QColor("#fff3cd"))
            table.setItem(row, 3, status_item)
        
        # تنسيق الجدول
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: none;
            }
            QHeaderView::section {
                background: #f8f9fa;
                color: #495057;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)
        
        table.horizontalHeader().setStretchLastSection(True)
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setMaximumHeight(200)
        
        layout.addWidget(table)
        return group
    
    def create_alerts_table(self):
        """إنشاء جدول التنبيهات"""
        group = QGroupBox("🔔 التنبيهات والإشعارات")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #495057;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 8px;
                background: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px;
                background: white;
            }
        """)
        
        layout = QVBoxLayout(group)
        
        # تنبيهات مهمة
        alerts = [
            ("⚠️", "مخزون منخفض", "23 منتج بحاجة لإعادة تموين", "#ffc107"),
            ("🔴", "فواتير متأخرة", "12 فاتورة تجاوزت موعد الاستحقاق", "#dc3545"),
            ("💰", "هدف المبيعات", "تم تحقيق 87% من هدف الشهر", "#28a745"),
            ("📅", "مواعيد مهمة", "اجتماع مع المورد الجديد غداً", "#17a2b8"),
            ("📊", "تقرير جاهز", "تقرير المبيعات الشهري جاهز للمراجعة", "#6f42c1")
        ]
        
        for icon, title, desc, color in alerts:
            alert_widget = QFrame()
            alert_widget.setStyleSheet(f"""
                QFrame {{
                    background: white;
                    border-left: 4px solid {color};
                    border-radius: 4px;
                    padding: 8px;
                    margin: 3px 0;
                }}
                QFrame:hover {{
                    background: #f8f9fa;
                }}
            """)
            
            alert_layout = QHBoxLayout(alert_widget)
            alert_layout.setContentsMargins(8, 8, 8, 8)
            
            icon_label = QLabel(icon)
            icon_label.setStyleSheet("font-size: 16px;")
            icon_label.setFixedWidth(30)
            
            text_widget = QWidget()
            text_layout = QVBoxLayout(text_widget)
            text_layout.setContentsMargins(0, 0, 0, 0)
            text_layout.setSpacing(2)
            
            title_label = QLabel(title)
            title_label.setStyleSheet("font-weight: bold; font-size: 13px; color: #495057;")
            
            desc_label = QLabel(desc)
            desc_label.setStyleSheet("font-size: 11px; color: #6c757d;")
            desc_label.setWordWrap(True)
            
            text_layout.addWidget(title_label)
            text_layout.addWidget(desc_label)
            
            alert_layout.addWidget(icon_label)
            alert_layout.addWidget(text_widget)
            
            layout.addWidget(alert_widget)
        
        return group
    
    def darken_color(self, color, factor=0.2):
        """تظليل اللون"""
        try:
            color_obj = QColor(color)
            h, s, l, a = color_obj.getHslF()
            return QColor.fromHslF(h, s, max(0, l - factor), a).name()
        except:
            return "#333333"