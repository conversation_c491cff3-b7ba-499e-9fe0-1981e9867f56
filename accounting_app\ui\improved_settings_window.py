"""
صفحة الإعدادات المحسنة مع خانات كبيرة وواضحة
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                              QLabel, QFrame, QLineEdit, QComboBox, QSpinBox,
                              QPushButton, QGroupBox, QScrollArea, QGridLayout,
                              QCheckBox, QSlider, QProgressBar, QTextEdit,
                              QFileDialog, QMessageBox, QDateEdit, QTimeEdit)
from PySide6.QtCore import Qt, QTimer, QDate, QTime
from PySide6.QtGui import QFont, QColor
from accounting_app.ui.styles import AppStyles
import json
import os


class ImprovedSettingsWindow(QWidget):
    """نافذة الإعدادات المحسنة مع خانات كبيرة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("⚙️ إعدادات النظام المتطورة")
        self.setMinimumSize(1000, 700)
        self.settings_data = {}
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # الرأس المحسن
        header = self.create_enhanced_header()
        main_layout.addWidget(header)
        
        # التبويبات المحسنة
        tabs = self.create_enhanced_tabs()
        main_layout.addWidget(tabs)
        
        # شريط الأزرار
        buttons_bar = self.create_buttons_bar()
        main_layout.addWidget(buttons_bar)
    
    def create_enhanced_header(self):
        """إنشاء رأس محسن"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                border-radius: 0px;
                padding: 30px;
                color: white;
                min-height: 140px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # القسم النصي
        text_section = QWidget()
        text_layout = QVBoxLayout(text_section)
        
        title = QLabel("⚙️ إعدادات النظام الشاملة")
        title.setStyleSheet("""
            font-size: 32px;
            font-weight: bold;
            color: white;
            margin-bottom: 10px;
        """)
        
        subtitle = QLabel("تخصيص كامل للنظام • إعدادات متقدمة • حفظ تلقائي")
        subtitle.setStyleSheet("""
            font-size: 18px;
            color: white;
            opacity: 0.95;
            margin-bottom: 15px;
        """)
        
        # معلومات سريعة
        info_layout = QHBoxLayout()
        info_cards = [
            self.create_info_card("✅ الحالة", "نشط", "🟢"),
            self.create_info_card("💾 الحفظ", "تلقائي", "🔄"),
            self.create_info_card("🔧 الإعدادات", "25+", "⚙️")
        ]
        
        for card in info_cards:
            info_layout.addWidget(card)
        info_layout.addStretch()
        
        text_layout.addWidget(title)
        text_layout.addWidget(subtitle)
        text_layout.addSpacing(15)
        text_layout.addLayout(info_layout)
        
        # الأيقونة الجانبية
        icon_section = QWidget()
        icon_layout = QVBoxLayout(icon_section)
        icon_layout.setAlignment(Qt.AlignCenter)
        
        main_icon = QLabel("⚙️")
        main_icon.setStyleSheet("""
            font-size: 100px;
            color: white;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 50px;
            padding: 30px;
            border: 3px solid rgba(255, 255, 255, 0.3);
        """)
        main_icon.setAlignment(Qt.AlignCenter)
        main_icon.setFixedSize(180, 180)
        
        status_label = QLabel("✅ جميع الإعدادات محدثة")
        status_label.setStyleSheet("""
            color: #4CAF50;
            font-size: 14px;
            font-weight: bold;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 15px;
            border-radius: 20px;
            margin-top: 15px;
        """)
        status_label.setAlignment(Qt.AlignCenter)
        
        icon_layout.addWidget(main_icon)
        icon_layout.addWidget(status_label)
        
        layout.addWidget(text_section, 7)
        layout.addWidget(icon_section, 3)
        
        return header_frame
    
    def create_info_card(self, title, value, icon):
        """إنشاء بطاقة معلومات"""
        card = QWidget()
        card.setStyleSheet("""
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 12px 18px;
            margin: 5px;
        """)
        
        layout = QHBoxLayout(card)
        layout.setContentsMargins(12, 10, 12, 10)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 24px; color: white;")
        
        text_widget = QWidget()
        text_layout = QVBoxLayout(text_widget)
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(3)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 13px; color: white; opacity: 0.8;")
        
        text_layout.addWidget(value_label)
        text_layout.addWidget(title_label)
        
        layout.addWidget(icon_label)
        layout.addWidget(text_widget)
        
        return card
    
    def create_enhanced_tabs(self):
        """إنشاء تبويبات محسنة"""
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background: #f8f9fa;
                padding: 0px;
            }
            QTabBar::tab {
                background: #ffffff;
                color: #333;
                padding: 18px 30px;
                margin: 3px;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
                font-size: 16px;
                font-weight: 600;
                min-width: 160px;
                border: 2px solid #e0e0e0;
                border-bottom: none;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                font-weight: bold;
                border-color: #4CAF50;
            }
            QTabBar::tab:hover:!selected {
                background: #e8f5e8;
                color: #2e7d32;
                border-color: #4CAF50;
            }
        """)
        
        tabs.addTab(self.create_company_tab(), "🏢 معلومات الشركة")
        tabs.addTab(self.create_appearance_tab(), "🎨 المظهر والواجهة")
        tabs.addTab(self.create_system_tab(), "🔧 إعدادات النظام")
        tabs.addTab(self.create_backup_tab(), "💾 النسخ الاحتياطي")
        tabs.addTab(self.create_advanced_tab(), "⚡ إعدادات متقدمة")
        
        return tabs
    
    def create_company_tab(self):
        """تبويب معلومات الشركة"""
        tab = QWidget()
        tab.setStyleSheet("background: #f8f9fa;")
        
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; background: #f8f9fa; }")
        
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # مجموعة البيانات الأساسية
        basic_group = self.create_settings_group(
            "📋 البيانات الأساسية",
            [
                ("📝 اسم الشركة:", "company_name", "شركة التكنولوجيا المتقدمة", "text"),
                ("📍 العنوان الكامل:", "company_address", "123 شارع النهضة، القاهرة، مصر", "text"),
                ("📞 رقم الهاتف:", "company_phone", "+20 2 1234 5678", "text"),
                ("📧 البريد الإلكتروني:", "company_email", "<EMAIL>", "text"),
                ("🌐 الموقع الإلكتروني:", "company_website", "www.company.com", "text"),
            ]
        )
        
        # مجموعة البيانات الضريبية
        tax_group = self.create_settings_group(
            "💰 البيانات الضريبية والمالية",
            [
                ("🏢 الرقم الضريبي:", "tax_number", "*********", "text"),
                ("💵 العملة الافتراضية:", "currency", "جنيه مصري (ج.م)", "combo", 
                 ["جنيه مصري (ج.م)", "دولار أمريكي ($)", "يورو (€)", "ريال سعودي (ر.س)"]),
                ("🔢 عدد الخانات العشرية:", "decimal_places", 2, "spin"),
                ("📊 معدل الضريبة (%):", "tax_rate", 14.0, "double"),
            ]
        )
        
        layout.addWidget(basic_group)
        layout.addWidget(tax_group)
        layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(scroll_area)
        
        return tab
    
    def create_appearance_tab(self):
        """تبويب المظهر والواجهة"""
        tab = QWidget()
        tab.setStyleSheet("background: #f8f9fa;")
        
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; background: #f8f9fa; }")
        
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # مجموعة السمة
        theme_group = self.create_settings_group(
            "🎨 سمة النظام",
            [
                ("🌙 الوضع:", "theme_mode", "فاتح", "combo", ["فاتح", "داكن", "تلقائي"]),
                ("🎯 اللون الأساسي:", "primary_color", "#1976D2", "color"),
                ("📏 حجم الخط:", "font_size", 14, "spin"),
                ("👁️ شفافية النوافذ:", "window_opacity", 95, "slider"),
            ]
        )
        
        # مجموعة التخطيط
        layout_group = self.create_settings_group(
            "📐 تخطيط الواجهة",
            [
                ("🗂️ عرض الشريط الجانبي:", "sidebar_visible", True, "check"),
                ("📊 عرض شريط الحالة:", "statusbar_visible", True, "check"),
                ("🔧 عرض شريط الأدوات:", "toolbar_visible", True, "check"),
                ("📱 وضع مضغوط:", "compact_mode", False, "check"),
            ]
        )
        
        layout.addWidget(theme_group)
        layout.addWidget(layout_group)
        layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(scroll_area)
        
        return tab
    
    def create_system_tab(self):
        """تبويب إعدادات النظام"""
        tab = QWidget()
        tab.setStyleSheet("background: #f8f9fa;")
        
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; background: #f8f9fa; }")
        
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # إعدادات الأداء
        performance_group = self.create_settings_group(
            "⚡ الأداء والسرعة",
            [
                ("🔄 الحفظ التلقائي:", "auto_save", True, "check"),
                ("⏱️ فترة الحفظ التلقائي (دقائق):", "auto_save_interval", 5, "spin"),
                ("📊 تحديث الرسوم البيانية (ثواني):", "chart_update_interval", 30, "spin"),
                ("💾 حجم ذاكرة التخزين المؤقت (MB):", "cache_size", 256, "spin"),
            ]
        )
        
        # إعدادات الأمان
        security_group = self.create_settings_group(
            "🔒 الأمان والحماية",
            [
                ("🔐 طلب كلمة مرور عند البدء:", "require_password", True, "check"),
                ("⏰ مهلة انتهاء الجلسة (دقائق):", "session_timeout", 60, "spin"),
                ("📝 تسجيل العمليات:", "log_operations", True, "check"),
                ("🔄 نسخة احتياطية يومية:", "daily_backup", True, "check"),
            ]
        )
        
        layout.addWidget(performance_group)
        layout.addWidget(security_group)
        layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(scroll_area)
        
        return tab
    
    def create_backup_tab(self):
        """تبويب النسخ الاحتياطي"""
        tab = QWidget()
        tab.setStyleSheet("background: #f8f9fa;")
        
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; background: #f8f9fa; }")
        
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # إعدادات النسخ الاحتياطي
        backup_group = self.create_settings_group(
            "💾 إعدادات النسخ الاحتياطي",
            [
                ("📁 مجلد النسخ الاحتياطي:", "backup_folder", "C:/Backup/Accounting", "folder"),
                ("⏰ النسخ التلقائي:", "auto_backup", True, "check"),
                ("🕐 وقت النسخ اليومي:", "backup_time", "02:00", "time"),
                ("📅 الاحتفاظ بالنسخ (أيام):", "backup_retention_days", 30, "spin"),
            ]
        )
        
        # أزرار النسخ الاحتياطي
        backup_actions = self.create_backup_actions()
        
        layout.addWidget(backup_group)
        layout.addWidget(backup_actions)
        layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(scroll_area)
        
        return tab
    
    def create_advanced_tab(self):
        """تبويب الإعدادات المتقدمة"""
        tab = QWidget()
        tab.setStyleSheet("background: #f8f9fa;")
        
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; background: #f8f9fa; }")
        
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # إعدادات قاعدة البيانات
        database_group = self.create_settings_group(
            "🗄️ قاعدة البيانات",
            [
                ("📍 مسار قاعدة البيانات:", "database_path", "data/accounting.db", "text"),
                ("🔄 تحسين تلقائي:", "auto_optimize", True, "check"),
                ("📊 إحصائيات الاستخدام:", "usage_stats", True, "check"),
                ("🛠️ وضع التطوير:", "debug_mode", False, "check"),
            ]
        )
        
        # إعدادات الطباعة
        print_group = self.create_settings_group(
            "🖨️ الطباعة والتقارير",
            [
                ("🖨️ الطابعة الافتراضية:", "default_printer", "طابعة النظام", "combo", 
                 ["طابعة النظام", "Microsoft Print to PDF", "طابعة الشبكة"]),
                ("📄 حجم الورق:", "paper_size", "A4", "combo", ["A4", "Letter", "A3"]),
                ("📝 تضمين الشعار:", "include_logo", True, "check"),
                ("🎨 طباعة ملونة:", "color_printing", False, "check"),
            ]
        )
        
        layout.addWidget(database_group)
        layout.addWidget(print_group)
        layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(scroll_area)
        
        return tab
    
    def create_settings_group(self, title, settings_list):
        """إنشاء مجموعة إعدادات"""
        group = QGroupBox(title)
        group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 20px;
                font-weight: bold;
                color: {AppStyles.PRIMARY_COLOR};
                border: 3px solid #e0e0e0;
                border-radius: 15px;
                margin-top: 25px;
                padding-top: 20px;
                background-color: white;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 20px;
                padding: 8px 20px;
                background-color: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
            }}
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 40, 30, 30)
        
        for i, setting in enumerate(settings_list):
            if len(setting) >= 4:
                label_text, key, default_value, input_type = setting[:4]
                options = setting[4] if len(setting) > 4 else []
                
                # التسمية
                label = QLabel(label_text)
                label.setStyleSheet("""
                    font-size: 18px;
                    font-weight: 600;
                    color: #333;
                    padding: 10px 0;
                """)
                
                # عنصر الإدخال
                input_widget = self.create_input_widget(input_type, default_value, options, key)
                
                layout.addWidget(label, i, 0)
                layout.addWidget(input_widget, i, 1)
        
        return group
    
    def create_input_widget(self, input_type, default_value, options, key):
        """إنشاء عنصر الإدخال حسب النوع"""
        base_style = f"""
            font-size: 16px;
            padding: 18px 25px;
            border: 3px solid #e0e0e0;
            border-radius: 12px;
            background-color: white;
            min-height: 25px;
        """
        
        focus_style = f"""
            border-color: {AppStyles.PRIMARY_COLOR};
            background-color: #f8fffe;
        """
        
        if input_type == "text":
            widget = QLineEdit(str(default_value))
            widget.setStyleSheet(f"""
                QLineEdit {{ {base_style} }}
                QLineEdit:focus {{ {focus_style} }}
            """)
            widget.textChanged.connect(lambda value, k=key: self.update_setting(k, value))
            
        elif input_type == "combo":
            widget = QComboBox()
            widget.addItems(options)
            if default_value in options:
                widget.setCurrentText(str(default_value))
            widget.setStyleSheet(f"""
                QComboBox {{ {base_style} }}
                QComboBox:focus {{ {focus_style} }}
                QComboBox::drop-down {{
                    subcontrol-origin: padding;
                    subcontrol-position: top right;
                    width: 40px;
                    border-left: 3px solid #e0e0e0;
                    border-radius: 8px;
                }}
                QComboBox::down-arrow {{
                    width: 16px;
                    height: 16px;
                }}
            """)
            widget.currentTextChanged.connect(lambda value, k=key: self.update_setting(k, value))
            
        elif input_type == "spin":
            widget = QSpinBox()
            widget.setMinimum(0)
            widget.setMaximum(999999)
            widget.setValue(int(default_value))
            widget.setStyleSheet(f"""
                QSpinBox {{ {base_style} }}
                QSpinBox:focus {{ {focus_style} }}
                QSpinBox::up-button, QSpinBox::down-button {{
                    width: 30px;
                    border-radius: 6px;
                }}
            """)
            widget.valueChanged.connect(lambda value, k=key: self.update_setting(k, value))
            
        elif input_type == "double":
            widget = QSpinBox()  # يمكن استخدام QDoubleSpinBox للعشرية
            widget.setMinimum(0)
            widget.setMaximum(100)
            widget.setValue(int(default_value))
            widget.setSuffix(" %")
            widget.setStyleSheet(f"""
                QSpinBox {{ {base_style} }}
                QSpinBox:focus {{ {focus_style} }}
            """)
            widget.valueChanged.connect(lambda value, k=key: self.update_setting(k, value))
            
        elif input_type == "check":
            widget = QCheckBox()
            widget.setChecked(bool(default_value))
            widget.setStyleSheet(f"""
                QCheckBox {{
                    font-size: 16px;
                    font-weight: 600;
                    color: #333;
                    spacing: 15px;
                }}
                QCheckBox::indicator {{
                    width: 25px;
                    height: 25px;
                    border-radius: 8px;
                    border: 3px solid #e0e0e0;
                    background-color: white;
                }}
                QCheckBox::indicator:checked {{
                    background-color: {AppStyles.PRIMARY_COLOR};
                    border-color: {AppStyles.PRIMARY_COLOR};
                }}
                QCheckBox::indicator:hover {{
                    border-color: {AppStyles.PRIMARY_COLOR};
                }}
            """)
            widget.toggled.connect(lambda value, k=key: self.update_setting(k, value))
            
        elif input_type == "slider":
            widget = QSlider(Qt.Horizontal)
            widget.setMinimum(50)
            widget.setMaximum(100)
            widget.setValue(int(default_value))
            widget.setStyleSheet(f"""
                QSlider::groove:horizontal {{
                    border: 2px solid #e0e0e0;
                    height: 10px;
                    background: #f0f0f0;
                    border-radius: 6px;
                }}
                QSlider::handle:horizontal {{
                    background: {AppStyles.PRIMARY_COLOR};
                    border: 2px solid {AppStyles.PRIMARY_COLOR};
                    width: 25px;
                    margin: -8px 0;
                    border-radius: 12px;
                }}
                QSlider::handle:horizontal:hover {{
                    background: {AppStyles.HIGHLIGHT_COLOR};
                }}
            """)
            widget.valueChanged.connect(lambda value, k=key: self.update_setting(k, value))
            
        elif input_type == "folder":
            container = QWidget()
            layout = QHBoxLayout(container)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setSpacing(10)
            
            line_edit = QLineEdit(str(default_value))
            line_edit.setStyleSheet(f"""
                QLineEdit {{ {base_style} }}
                QLineEdit:focus {{ {focus_style} }}
            """)
            
            browse_btn = QPushButton("📁 تصفح")
            browse_btn.setStyleSheet(f"""
                QPushButton {{
                    background: {AppStyles.PRIMARY_COLOR};
                    color: white;
                    border: none;
                    padding: 18px 25px;
                    border-radius: 12px;
                    font-size: 16px;
                    font-weight: 600;
                    min-width: 100px;
                }}
                QPushButton:hover {{
                    background: {AppStyles.HIGHLIGHT_COLOR};
                }}
            """)
            browse_btn.clicked.connect(lambda: self.browse_folder(line_edit, key))
            
            layout.addWidget(line_edit, 8)
            layout.addWidget(browse_btn, 2)
            
            line_edit.textChanged.connect(lambda value, k=key: self.update_setting(k, value))
            widget = container
            
        elif input_type == "time":
            widget = QTimeEdit()
            widget.setTime(QTime.fromString(str(default_value), "hh:mm"))
            widget.setStyleSheet(f"""
                QTimeEdit {{ {base_style} }}
                QTimeEdit:focus {{ {focus_style} }}
            """)
            widget.timeChanged.connect(lambda value, k=key: self.update_setting(k, value.toString("hh:mm")))
            
        elif input_type == "color":
            widget = QPushButton(str(default_value))
            widget.setStyleSheet(f"""
                QPushButton {{
                    {base_style}
                    background-color: {default_value};
                    color: white;
                    font-weight: bold;
                }}
            """)
            widget.clicked.connect(lambda: self.choose_color(widget, key))
            
        else:
            widget = QLineEdit(str(default_value))
            widget.setStyleSheet(f"""
                QLineEdit {{ {base_style} }}
                QLineEdit:focus {{ {focus_style} }}
            """)
        
        return widget
    
    def create_backup_actions(self):
        """إنشاء أزرار النسخ الاحتياطي"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 3px solid #e0e0e0;
                border-radius: 15px;
                padding: 25px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        layout.setSpacing(20)
        
        title = QLabel("🎯 عمليات النسخ الاحتياطي")
        title.setStyleSheet(f"""
            font-size: 20px;
            font-weight: bold;
            color: {AppStyles.PRIMARY_COLOR};
            margin-bottom: 15px;
        """)
        
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        
        # زر النسخ الاحتياطي اليدوي
        backup_btn = QPushButton("💾 إنشاء نسخة احتياطية الآن")
        backup_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                padding: 18px 30px;
                border-radius: 12px;
                font-size: 16px;
                font-weight: 600;
                min-width: 200px;
                min-height: 25px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #3d8b40);
            }}
            QPushButton:pressed {{
                background: #3d8b40;
            }}
        """)
        backup_btn.clicked.connect(self.create_backup)
        
        # زر الاستعادة
        restore_btn = QPushButton("🔄 استعادة من نسخة احتياطية")
        restore_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                border: none;
                padding: 18px 30px;
                border-radius: 12px;
                font-size: 16px;
                font-weight: 600;
                min-width: 200px;
                min-height: 25px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976D2, stop:1 #1565C0);
            }}
        """)
        restore_btn.clicked.connect(self.restore_backup)
        
        buttons_layout.addWidget(backup_btn)
        buttons_layout.addWidget(restore_btn)
        buttons_layout.addStretch()
        
        # شريط التقدم
        self.backup_progress = QProgressBar()
        self.backup_progress.setVisible(False)
        self.backup_progress.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                text-align: center;
                font-size: 14px;
                font-weight: bold;
                color: white;
                background-color: #f0f0f0;
                min-height: 25px;
            }}
            QProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                border-radius: 6px;
            }}
        """)
        
        layout.addWidget(title)
        layout.addLayout(buttons_layout)
        layout.addWidget(self.backup_progress)
        
        return frame
    
    def create_buttons_bar(self):
        """إنشاء شريط الأزرار السفلي"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border-top: 2px solid #e0e0e0;
                padding: 20px 30px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        layout.setSpacing(15)
        
        # معلومات الحفظ
        save_info = QLabel("💾 يتم الحفظ تلقائياً عند التغيير")
        save_info.setStyleSheet("""
            font-size: 14px;
            color: #666;
            font-weight: 500;
        """)
        
        layout.addWidget(save_info)
        layout.addStretch()
        
        # زر التطبيق
        apply_btn = QPushButton("✅ تطبيق الإعدادات")
        apply_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-size: 16px;
                font-weight: 600;
                min-width: 150px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #3d8b40);
            }}
        """)
        apply_btn.clicked.connect(self.apply_settings)
        
        # زر الإعادة
        reset_btn = QPushButton("🔄 إعادة تعيين")
        reset_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f44336, stop:1 #d32f2f);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-size: 16px;
                font-weight: 600;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d32f2f, stop:1 #c62828);
            }}
        """)
        reset_btn.clicked.connect(self.reset_settings)
        
        # زر الحفظ
        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-size: 16px;
                font-weight: 600;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976D2, stop:1 #1565C0);
            }}
        """)
        save_btn.clicked.connect(self.save_settings)
        
        layout.addWidget(reset_btn)
        layout.addWidget(apply_btn)
        layout.addWidget(save_btn)
        
        return frame
    
    def update_setting(self, key, value):
        """تحديث إعداد معين"""
        self.settings_data[key] = value
        print(f"تم تحديث الإعداد: {key} = {value}")
    
    def browse_folder(self, line_edit, key):
        """تصفح مجلد"""
        folder = QFileDialog.getExistingDirectory(self, "اختر المجلد")
        if folder:
            line_edit.setText(folder)
            self.update_setting(key, folder)
    
    def choose_color(self, button, key):
        """اختيار لون"""
        from PySide6.QtWidgets import QColorDialog
        color = QColorDialog.getColor()
        if color.isValid():
            color_name = color.name()
            button.setText(color_name)
            button.setStyleSheet(f"""
                QPushButton {{
                    font-size: 16px;
                    padding: 18px 25px;
                    border: 3px solid #e0e0e0;
                    border-radius: 12px;
                    background-color: {color_name};
                    color: white;
                    font-weight: bold;
                    min-height: 25px;
                }}
            """)
            self.update_setting(key, color_name)
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        self.backup_progress.setVisible(True)
        self.backup_progress.setValue(0)
        
        # محاكاة عملية النسخ الاحتياطي
        timer = QTimer()
        progress = 0
        
        def update_progress():
            nonlocal progress
            progress += 10
            self.backup_progress.setValue(progress)
            
            if progress >= 100:
                timer.stop()
                self.backup_progress.setVisible(False)
                QMessageBox.information(self, "نجح", "تم إنشاء النسخة الاحتياطية بنجاح!")
        
        timer.timeout.connect(update_progress)
        timer.start(200)
    
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        file_path, _ = QFileDialog.getOpenFileName(self, "اختر ملف النسخة الاحتياطية", "", "ملفات النسخ الاحتياطي (*.bak *.db)")
        if file_path:
            reply = QMessageBox.question(self, "تأكيد", "هل تريد استعادة النسخة الاحتياطية؟\nسيتم استبدال البيانات الحالية.")
            if reply == QMessageBox.Yes:
                QMessageBox.information(self, "نجح", "تم استعادة النسخة الاحتياطية بنجاح!")
    
    def apply_settings(self):
        """تطبيق الإعدادات"""
        QMessageBox.information(self, "تم التطبيق", "تم تطبيق جميع الإعدادات بنجاح!")
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(self, "تأكيد", "هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟")
        if reply == QMessageBox.Yes:
            self.settings_data.clear()
            QMessageBox.information(self, "تم", "تم إعادة تعيين الإعدادات بنجاح!")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # إنشاء مجلد الإعدادات إذا لم يكن موجوداً
            settings_dir = os.path.join(os.path.expanduser("~"), ".accounting_app")
            if not os.path.exists(settings_dir):
                os.makedirs(settings_dir)
            
            # حفظ الإعدادات في ملف JSON
            settings_file = os.path.join(settings_dir, "app_settings.json")
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings_data, f, ensure_ascii=False, indent=2)
            
            QMessageBox.information(self, "تم الحفظ", "تم حفظ جميع الإعدادات بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات: {str(e)}")
    
    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            settings_dir = os.path.join(os.path.expanduser("~"), ".accounting_app")
            settings_file = os.path.join(settings_dir, "app_settings.json")
            
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    self.settings_data = json.load(f)
                print("تم تحميل الإعدادات المحفوظة")
            else:
                print("لا توجد إعدادات محفوظة، سيتم استخدام القيم الافتراضية")
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
            self.settings_data = {}


class ImprovedSettingsWindow(QWidget):
    """نافذة الإعدادات المحسنة مع خانات كبيرة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("⚙️ إعدادات النظام المتطورة")
        self.setMinimumSize(1000, 700)
        self.settings_data = {}
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # الرأس المحسن
        header = self.create_enhanced_header()
        main_layout.addWidget(header)
        
        # التبويبات المحسنة
        tabs = self.create_enhanced_tabs()
        main_layout.addWidget(tabs)
        
        # شريط الأزرار
        buttons_bar = self.create_buttons_bar()
        main_layout.addWidget(buttons_bar)
    
    def create_enhanced_header(self):
        """إنشاء رأس محسن"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                border-radius: 0px;
                padding: 30px;
                color: white;
                min-height: 140px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # القسم النصي
        text_section = QWidget()
        text_layout = QVBoxLayout(text_section)
        
        title = QLabel("⚙️ إعدادات النظام الشاملة")
        title.setStyleSheet("""
            font-size: 32px;
            font-weight: bold;
            color: white;
            margin-bottom: 10px;
        """)
        
        subtitle = QLabel("تخصيص كامل للنظام • إعدادات متقدمة • حفظ تلقائي")
        subtitle.setStyleSheet("""
            font-size: 18px;
            color: white;
            opacity: 0.95;
            margin-bottom: 15px;
        """)
        
        # معلومات سريعة
        info_layout = QHBoxLayout()
        info_cards = [
            self.create_info_card("✅ الحالة", "نشط", "🟢"),
            self.create_info_card("💾 الحفظ", "تلقائي", "🔄"),
            self.create_info_card("🔧 الإعدادات", "25+", "⚙️")
        ]
        
        for card in info_cards:
            info_layout.addWidget(card)
        info_layout.addStretch()
        
        text_layout.addWidget(title)
        text_layout.addWidget(subtitle)
        text_layout.addSpacing(15)
        text_layout.addLayout(info_layout)
        
        # الأيقونة الجانبية
        icon_section = QWidget()
        icon_layout = QVBoxLayout(icon_section)
        icon_layout.setAlignment(Qt.AlignCenter)
        
        main_icon = QLabel("⚙️")
        main_icon.setStyleSheet("""
            font-size: 100px;
            color: white;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 50px;
            padding: 30px;
            border: 3px solid rgba(255, 255, 255, 0.3);
        """)
        main_icon.setAlignment(Qt.AlignCenter)
        main_icon.setFixedSize(180, 180)
        
        status_label = QLabel("✅ جميع الإعدادات محدثة")
        status_label.setStyleSheet("""
            color: #4CAF50;
            font-size: 14px;
            font-weight: bold;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 15px;
            border-radius: 20px;
            margin-top: 15px;
        """)
        status_label.setAlignment(Qt.AlignCenter)
        
        icon_layout.addWidget(main_icon)
        icon_layout.addWidget(status_label)
        
        layout.addWidget(text_section, 7)
        layout.addWidget(icon_section, 3)
        
        return header_frame
    
    def create_info_card(self, title, value, icon):
        """إنشاء بطاقة معلومات"""
        card = QWidget()
        card.setStyleSheet("""
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 12px 18px;
            margin: 5px;
        """)
        
        layout = QHBoxLayout(card)
        layout.setContentsMargins(12, 10, 12, 10)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 24px; color: white;")
        
        text_widget = QWidget()
        text_layout = QVBoxLayout(text_widget)
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(3)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 13px; color: white; opacity: 0.8;")
        
        text_layout.addWidget(value_label)
        text_layout.addWidget(title_label)
        
        layout.addWidget(icon_label)
        layout.addWidget(text_widget)
        
        return card
    
    def create_enhanced_tabs(self):
        """إنشاء تبويبات محسنة"""
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background: #f8f9fa;
                padding: 0px;
            }
            QTabBar::tab {
                background: #ffffff;
                color: #333;
                padding: 18px 30px;
                margin: 3px;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
                font-size: 16px;
                font-weight: 600;
                min-width: 160px;
                border: 2px solid #e0e0e0;
                border-bottom: none;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                font-weight: bold;
                border-color: #4CAF50;
            }
            QTabBar::tab:hover:!selected {
                background: #e8f5e8;
                color: #2e7d32;
                border-color: #4CAF50;
            }
        """)
        
        tabs.addTab(self.create_company_tab(), "🏢 معلومات الشركة")
        tabs.addTab(self.create_appearance_tab(), "🎨 المظهر والواجهة")
        tabs.addTab(self.create_system_tab(), "🔧 إعدادات النظام")
        tabs.addTab(self.create_backup_tab(), "💾 النسخ الاحتياطي")
        tabs.addTab(self.create_advanced_tab(), "⚡ إعدادات متقدمة")
        
        return tabs
    
    def create_company_tab(self):
        """تبويب معلومات الشركة"""
        tab = QWidget()
        tab.setStyleSheet("background: #f8f9fa;")
        
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; background: #f8f9fa; }")
        
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # مجموعة البيانات الأساسية
        basic_group = self.create_settings_group(
            "📋 البيانات الأساسية",
            [
                ("📝 اسم الشركة:", "company_name", "شركة التكنولوجيا المتقدمة", "text"),
                ("📍 العنوان الكامل:", "company_address", "123 شارع النهضة، القاهرة، مصر", "text"),
                ("📞 رقم الهاتف:", "company_phone", "+20 2 1234 5678", "text"),
                ("📧 البريد الإلكتروني:", "company_email", "<EMAIL>", "text"),
                ("🌐 الموقع الإلكتروني:", "company_website", "www.company.com", "text"),
            ]
        )
        
        # مجموعة البيانات الضريبية
        tax_group = self.create_settings_group(
            "💰 البيانات الضريبية والمالية",
            [
                ("🏢 الرقم الضريبي:", "tax_number", "*********", "text"),
                ("💵 العملة الافتراضية:", "currency", "جنيه مصري (ج.م)", "combo", 
                 ["جنيه مصري (ج.م)", "دولار أمريكي ($)", "يورو (€)", "ريال سعودي (ر.س)"]),
                ("🔢 عدد الخانات العشرية:", "decimal_places", 2, "spin"),
                ("📊 معدل الضريبة (%):", "tax_rate", 14.0, "double"),
            ]
        )
        
        layout.addWidget(basic_group)
        layout.addWidget(tax_group)
        layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(scroll_area)
        
        return tab
    
    def create_appearance_tab(self):
        """تبويب المظهر والواجهة"""
        tab = QWidget()
        tab.setStyleSheet("background: #f8f9fa;")
        
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; background: #f8f9fa; }")
        
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # مجموعة السمة
        theme_group = self.create_settings_group(
            "🎨 سمة النظام",
            [
                ("🌙 الوضع:", "theme_mode", "فاتح", "combo", ["فاتح", "داكن", "تلقائي"]),
                ("🎯 اللون الأساسي:", "primary_color", "#1976D2", "color"),
                ("📏 حجم الخط:", "font_size", 14, "spin"),
                ("👁️ شفافية النوافذ:", "window_opacity", 95, "slider"),
            ]
        )
        
        # مجموعة التخطيط
        layout_group = self.create_settings_group(
            "📐 تخطيط الواجهة",
            [
                ("🗂️ عرض الشريط الجانبي:", "sidebar_visible", True, "check"),
                ("📊 عرض شريط الحالة:", "statusbar_visible", True, "check"),
                ("🔧 عرض شريط الأدوات:", "toolbar_visible", True, "check"),
                ("📱 وضع مضغوط:", "compact_mode", False, "check"),
            ]
        )
        
        layout.addWidget(theme_group)
        layout.addWidget(layout_group)
        layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(scroll_area)
        
        return tab
    
    def create_system_tab(self):
        """تبويب إعدادات النظام"""
        tab = QWidget()
        tab.setStyleSheet("background: #f8f9fa;")
        
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; background: #f8f9fa; }")
        
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # إعدادات الأداء
        performance_group = self.create_settings_group(
            "⚡ الأداء والسرعة",
            [
                ("🔄 الحفظ التلقائي:", "auto_save", True, "check"),
                ("⏱️ فترة الحفظ التلقائي (دقائق):", "auto_save_interval", 5, "spin"),
                ("📊 تحديث الرسوم البيانية (ثواني):", "chart_update_interval", 30, "spin"),
                ("💾 حجم ذاكرة التخزين المؤقت (MB):", "cache_size", 256, "spin"),
            ]
        )
        
        # إعدادات الأمان
        security_group = self.create_settings_group(
            "🔒 الأمان والحماية",
            [
                ("🔐 طلب كلمة مرور عند البدء:", "require_password", True, "check"),
                ("⏰ مهلة انتهاء الجلسة (دقائق):", "session_timeout", 60, "spin"),
                ("📝 تسجيل العمليات:", "log_operations", True, "check"),
                ("🔄 نسخة احتياطية يومية:", "daily_backup", True, "check"),
            ]
        )
        
        layout.addWidget(performance_group)
        layout.addWidget(security_group)
        layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(scroll_area)
        
        return tab
    
    def create_backup_tab(self):
        """تبويب النسخ الاحتياطي"""
        tab = QWidget()
        tab.setStyleSheet("background: #f8f9fa;")
        
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; background: #f8f9fa; }")
        
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # إعدادات النسخ الاحتياطي
        backup_group = self.create_settings_group(
            "💾 إعدادات النسخ الاحتياطي",
            [
                ("📁 مجلد النسخ الاحتياطي:", "backup_folder", "C:/Backup/Accounting", "folder"),
                ("⏰ النسخ التلقائي:", "auto_backup", True, "check"),
                ("🕐 وقت النسخ اليومي:", "backup_time", "02:00", "time"),
                ("📅 الاحتفاظ بالنسخ (أيام):", "backup_retention_days", 30, "spin"),
            ]
        )
        
        # أزرار النسخ الاحتياطي
        backup_actions = self.create_backup_actions()
        
        layout.addWidget(backup_group)
        layout.addWidget(backup_actions)
        layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(scroll_area)
        
        return tab
    
    def create_advanced_tab(self):
        """تبويب الإعدادات المتقدمة"""
        tab = QWidget()
        tab.setStyleSheet("background: #f8f9fa;")
        
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; background: #f8f9fa; }")
        
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # إعدادات قاعدة البيانات
        database_group = self.create_settings_group(
            "🗄️ قاعدة البيانات",
            [
                ("📍 مسار قاعدة البيانات:", "database_path", "data/accounting.db", "text"),
                ("🔄 تحسين تلقائي:", "auto_optimize", True, "check"),
                ("📊 إحصائيات الاستخدام:", "usage_stats", True, "check"),
                ("🛠️ وضع التطوير:", "debug_mode", False, "check"),
            ]
        )
        
        # إعدادات الطباعة
        print_group = self.create_settings_group(
            "🖨️ الطباعة والتقارير",
            [
                ("🖨️ الطابعة الافتراضية:", "default_printer", "طابعة النظام", "combo", 
                 ["طابعة النظام", "Microsoft Print to PDF", "طابعة الشبكة"]),
                ("📄 حجم الورق:", "paper_size", "A4", "combo", ["A4", "Letter", "A3"]),
                ("📝 تضمين الشعار:", "include_logo", True, "check"),
                ("🎨 طباعة ملونة:", "color_printing", False, "check"),
            ]
        )
        
        layout.addWidget(database_group)
        layout.addWidget(print_group)
        layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(scroll_area)
        
        return tab
    
    def create_settings_group(self, title, settings_list):
        """إنشاء مجموعة إعدادات"""
        group = QGroupBox(title)
        group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 20px;
                font-weight: bold;
                color: {AppStyles.PRIMARY_COLOR};
                border: 3px solid #e0e0e0;
                border-radius: 15px;
                margin-top: 25px;
                padding-top: 20px;
                background-color: white;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 20px;
                padding: 8px 20px;
                background-color: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
            }}
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 40, 30, 30)
        
        for i, setting in enumerate(settings_list):
            if len(setting) >= 4:
                label_text, key, default_value, input_type = setting[:4]
                options = setting[4] if len(setting) > 4 else []
                
                # التسمية
                label = QLabel(label_text)
                label.setStyleSheet("""
                    font-size: 18px;
                    font-weight: 600;
                    color: #333;
                    padding: 10px 0;
                """)
                
                # عنصر الإدخال
                input_widget = self.create_input_widget(input_type, default_value, options, key)
                
                layout.addWidget(label, i, 0)
                layout.addWidget(input_widget, i, 1)
        
        return group
    
    def create_input_widget(self, input_type, default_value, options, key):
        """إنشاء عنصر الإدخال حسب النوع"""
        base_style = f"""
            font-size: 16px;
            padding: 18px 25px;
            border: 3px solid #e0e0e0;
            border-radius: 12px;
            background-color: white;
            min-height: 25px;
        """
        
        focus_style = f"""
            border-color: {AppStyles.PRIMARY_COLOR};
            background-color: #f8fffe;
        """
        
        if input_type == "text":
            widget = QLineEdit(str(default_value))
            widget.setStyleSheet(f"""
                QLineEdit {{ {base_style} }}
                QLineEdit:focus {{ {focus_style} }}
            """)
            widget.textChanged.connect(lambda value, k=key: self.update_setting(k, value))
            
        elif input_type == "combo":
            widget = QComboBox()
            widget.addItems(options)
            if default_value in options:
                widget.setCurrentText(str(default_value))
            widget.setStyleSheet(f"""
                QComboBox {{ {base_style} }}
                QComboBox:focus {{ {focus_style} }}
                QComboBox::drop-down {{
                    subcontrol-origin: padding;
                    subcontrol-position: top right;
                    width: 40px;
                    border-left: 3px solid #e0e0e0;
                    border-radius: 8px;
                }}
                QComboBox::down-arrow {{
                    width: 16px;
                    height: 16px;
                }}
            """)
            widget.currentTextChanged.connect(lambda value, k=key: self.update_setting(k, value))
            
        elif input_type == "spin":
            widget = QSpinBox()
            widget.setMinimum(0)
            widget.setMaximum(999999)
            widget.setValue(int(default_value))
            widget.setStyleSheet(f"""
                QSpinBox {{ {base_style} }}
                QSpinBox:focus {{ {focus_style} }}
                QSpinBox::up-button, QSpinBox::down-button {{
                    width: 30px;
                    border-radius: 6px;
                }}
            """)
            widget.valueChanged.connect(lambda value, k=key: self.update_setting(k, value))
            
        elif input_type == "double":
            widget = QSpinBox()  # يمكن استخدام QDoubleSpinBox للعشرية
            widget.setMinimum(0)
            widget.setMaximum(100)
            widget.setValue(int(default_value))
            widget.setSuffix(" %")
            widget.setStyleSheet(f"""
                QSpinBox {{ {base_style} }}
                QSpinBox:focus {{ {focus_style} }}
            """)
            widget.valueChanged.connect(lambda value, k=key: self.update_setting(k, value))
            
        elif input_type == "check":
            widget = QCheckBox()
            widget.setChecked(bool(default_value))
            widget.setStyleSheet(f"""
                QCheckBox {{
                    font-size: 16px;
                    font-weight: 600;
                    color: #333;
                    spacing: 15px;
                }}
                QCheckBox::indicator {{
                    width: 25px;
                    height: 25px;
                    border-radius: 8px;
                    border: 3px solid #e0e0e0;
                    background-color: white;
                }}
                QCheckBox::indicator:checked {{
                    background-color: {AppStyles.PRIMARY_COLOR};
                    border-color: {AppStyles.PRIMARY_COLOR};
                }}
                QCheckBox::indicator:hover {{
                    border-color: {AppStyles.PRIMARY_COLOR};
                }}
            """)
            widget.toggled.connect(lambda value, k=key: self.update_setting(k, value))
            
        elif input_type == "slider":
            widget = QSlider(Qt.Horizontal)
            widget.setMinimum(50)
            widget.setMaximum(100)
            widget.setValue(int(default_value))
            widget.setStyleSheet(f"""
                QSlider::groove:horizontal {{
                    border: 2px solid #e0e0e0;
                    height: 10px;
                    background: #f0f0f0;
                    border-radius: 6px;
                }}
                QSlider::handle:horizontal {{
                    background: {AppStyles.PRIMARY_COLOR};
                    border: 2px solid {AppStyles.PRIMARY_COLOR};
                    width: 25px;
                    margin: -8px 0;
                    border-radius: 12px;
                }}
                QSlider::handle:horizontal:hover {{
                    background: {AppStyles.HIGHLIGHT_COLOR};
                }}
            """)
            widget.valueChanged.connect(lambda value, k=key: self.update_setting(k, value))
            
        elif input_type == "folder":
            container = QWidget()
            layout = QHBoxLayout(container)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setSpacing(10)
            
            line_edit = QLineEdit(str(default_value))
            line_edit.setStyleSheet(f"""
                QLineEdit {{ {base_style} }}
                QLineEdit:focus {{ {focus_style} }}
            """)
            
            browse_btn = QPushButton("📁 تصفح")
            browse_btn.setStyleSheet(f"""
                QPushButton {{
                    background: {AppStyles.PRIMARY_COLOR};
                    color: white;
                    border: none;
                    padding: 18px 25px;
                    border-radius: 12px;
                    font-size: 16px;
                    font-weight: 600;
                    min-width: 100px;
                }}
                QPushButton:hover {{
                    background: {AppStyles.HIGHLIGHT_COLOR};
                }}
            """)
            browse_btn.clicked.connect(lambda: self.browse_folder(line_edit, key))
            
            layout.addWidget(line_edit, 8)
            layout.addWidget(browse_btn, 2)
            
            line_edit.textChanged.connect(lambda value, k=key: self.update_setting(k, value))
            widget = container
            
        elif input_type == "time":
            widget = QTimeEdit()
            widget.setTime(QTime.fromString(str(default_value), "hh:mm"))
            widget.setStyleSheet(f"""
                QTimeEdit {{ {base_style} }}
                QTimeEdit:focus {{ {focus_style} }}
            """)
            widget.timeChanged.connect(lambda value, k=key: self.update_setting(k, value.toString("hh:mm")))
            
        elif input_type == "color":
            widget = QPushButton(str(default_value))
            widget.setStyleSheet(f"""
                QPushButton {{
                    {base_style}
                    background-color: {default_value};
                    color: white;
                    font-weight: bold;
                }}
            """)
            widget.clicked.connect(lambda: self.choose_color(widget, key))
            
        else:
            widget = QLineEdit(str(default_value))
            widget.setStyleSheet(f"""
                QLineEdit {{ {base_style} }}
                QLineEdit:focus {{ {focus_style} }}
            """)
        
        return widget
    
    def create_backup_actions(self):
        """إنشاء أزرار النسخ الاحتياطي"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 3px solid #e0e0e0;
                border-radius: 15px;
                padding: 25px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        layout.setSpacing(20)
        
        title = QLabel("🎯 عمليات النسخ الاحتياطي")
        title.setStyleSheet(f"""
            font-size: 20px;
            font-weight: bold;
            color: {AppStyles.PRIMARY_COLOR};
            margin-bottom: 15px;
        """)
        
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        
        # زر النسخ الاحتياطي اليدوي
        backup_btn = QPushButton("💾 إنشاء نسخة احتياطية الآن")
        backup_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                padding: 18px 30px;
                border-radius: 12px;
                font-size: 16px;
                font-weight: 600;
                min-width: 200px;
                min-height: 25px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #3d8b40);
            }}
            QPushButton:pressed {{
                background: #3d8b40;
            }}
        """)
        backup_btn.clicked.connect(self.create_backup)
        
        # زر الاستعادة
        restore_btn = QPushButton("🔄 استعادة من نسخة احتياطية")
        restore_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                border: none;
                padding: 18px 30px;
                border-radius: 12px;
                font-size: 16px;
                font-weight: 600;
                min-width: 200px;
                min-height: 25px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976D2, stop:1 #1565C0);
            }}
        """)
        restore_btn.clicked.connect(self.restore_backup)
        
        buttons_layout.addWidget(backup_btn)
        buttons_layout.addWidget(restore_btn)
        buttons_layout.addStretch()
        
        # شريط التقدم
        self.backup_progress = QProgressBar()
        self.backup_progress.setVisible(False)
        self.backup_progress.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                text-align: center;
                font-size: 14px;
                font-weight: bold;
                color: white;
                background-color: #f0f0f0;
                min-height: 25px;
            }}
            QProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                border-radius: 6px;
            }}
        """)
        
        layout.addWidget(title)
        layout.addLayout(buttons_layout)
        layout.addWidget(self.backup_progress)
        
        return frame
    
    def create_buttons_bar(self):
        """إنشاء شريط الأزرار السفلي"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border-top: 2px solid #e0e0e0;
                padding: 20px 30px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        layout.setSpacing(15)
        
        # معلومات الحفظ
        save_info = QLabel("💾 يتم الحفظ تلقائياً عند التغيير")
        save_info.setStyleSheet("""
            font-size: 14px;
            color: #666;
            font-weight: 500;
        """)
        
        layout.addWidget(save_info)
        layout.addStretch()
        
        # زر التطبيق
        apply_btn = QPushButton("✅ تطبيق الإعدادات")
        apply_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-size: 16px;
                font-weight: 600;
                min-width: 150px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #3d8b40);
            }}
        """)
        apply_btn.clicked.connect(self.apply_settings)
        
        # زر الإعادة
        reset_btn = QPushButton("🔄 إعادة تعيين")
        reset_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f44336, stop:1 #d32f2f);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-size: 16px;
                font-weight: 600;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d32f2f, stop:1 #c62828);
            }}
        """)
        reset_btn.clicked.connect(self.reset_settings)
        
        # زر الحفظ
        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-size: 16px;
                font-weight: 600;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976D2, stop:1 #1565C0);
            }}
        """)
        save_btn.clicked.connect(self.save_settings)
        
        layout.addWidget(reset_btn)
        layout.addWidget(apply_btn)
        layout.addWidget(save_btn)
        
        return frame
    
    def update_setting(self, key, value):
        """تحديث إعداد معين"""
        self.settings_data[key] = value
        print(f"تم تحديث الإعداد: {key} = {value}")
    
    def browse_folder(self, line_edit, key):
        """تصفح مجلد"""
        folder = QFileDialog.getExistingDirectory(self, "اختر المجلد")
        if folder:
            line_edit.setText(folder)
            self.update_setting(key, folder)
    
    def choose_color(self, button, key):
        """اختيار لون"""
        from PySide6.QtWidgets import QColorDialog
        color = QColorDialog.getColor()
        if color.isValid():
            color_name = color.name()
            button.setText(color_name)
            button.setStyleSheet(f"""
                QPushButton {{
                    font-size: 16px;
                    padding: 18px 25px;
                    border: 3px solid #e0e0e0;
                    border-radius: 12px;
                    background-color: {color_name};
                    color: white;
                    font-weight: bold;
                    min-height: 25px;
                }}
            """)
            self.update_setting(key, color_name)
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        self.backup_progress.setVisible(True)
        self.backup_progress.setValue(0)
        
        # محاكاة عملية النسخ الاحتياطي
        timer = QTimer()
        progress = 0
        
        def update_progress():
            nonlocal progress
            progress += 10
            self.backup_progress.setValue(progress)
            
            if progress >= 100:
                timer.stop()
                self.backup_progress.setVisible(False)
                QMessageBox.information(self, "نجح", "تم إنشاء النسخة الاحتياطية بنجاح!")
        
        timer.timeout.connect(update_progress)
        timer.start(200)
    
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        file_path, _ = QFileDialog.getOpenFileName(self, "اختر ملف النسخة الاحتياطية", "", "ملفات النسخ الاحتياطي (*.bak *.db)")
        if file_path:
            reply = QMessageBox.question(self, "تأكيد", "هل تريد استعادة النسخة الاحتياطية؟\nسيتم استبدال البيانات الحالية.")
            if reply == QMessageBox.Yes:
                QMessageBox.information(self, "نجح", "تم استعادة النسخة الاحتياطية بنجاح!")
    
    def apply_settings(self):
        """تطبيق الإعدادات"""
        QMessageBox.information(self, "تم التطبيق", "تم تطبيق جميع الإعدادات بنجاح!")
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(self, "تأكيد", "هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟")
        if reply == QMessageBox.Yes:
            self.settings_data.clear()
            QMessageBox.information(self, "تم", "تم إعادة تعيين الإعدادات بنجاح!")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # إنشاء مجلد الإعدادات إذا لم يكن موجوداً
            settings_dir = os.path.join(os.path.expanduser("~"), ".accounting_app")
            if not os.path.exists(settings_dir):
                os.makedirs(settings_dir)
            
            # حفظ الإعدادات في ملف JSON
            settings_file = os.path.join(settings_dir, "app_settings.json")
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings_data, f, ensure_ascii=False, indent=2)
            
            QMessageBox.information(self, "تم الحفظ", "تم حفظ جميع الإعدادات بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات: {str(e)}")
    
    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            settings_dir = os.path.join(os.path.expanduser("~"), ".accounting_app")
            settings_file = os.path.join(settings_dir, "app_settings.json")
            
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    self.settings_data = json.load(f)
                print("تم تحميل الإعدادات المحفوظة")
            else:
                print("لا توجد إعدادات محفوظة، سيتم استخدام القيم الافتراضية")
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
            self.settings_data = {}