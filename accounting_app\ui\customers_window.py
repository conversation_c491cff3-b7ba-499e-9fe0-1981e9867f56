"""
صفحة إدارة العملاء
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QTableWidget, QTableWidgetItem, 
                              QHeaderView, QFrame, QLineEdit, QComboBox,
                              QDialog, QFormLayout, QDialogButtonBox, 
                              QMessageBox, QDoubleSpinBox, QTextEdit)
from PySide6.QtCore import Qt
from PySide6.QtGui import QColor

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class CustomersWindow(QWidget):
    """صفحة إدارة العملاء"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.customers_data = []  # قائمة العملاء
        self.init_ui()
        self.load_sample_customers()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        try:
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)
            
            # شريط الأدوات
            toolbar_frame = self.create_toolbar()
            
            # جدول العملاء
            customers_table = self.create_customers_table()
            
            # إضافة العناصر
            main_layout.addWidget(toolbar_frame)
            main_layout.addWidget(customers_table)
        except Exception as e:
            print("Error in CustomersWindow.init_ui:", e)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        try:
            toolbar_frame = QFrame()
            toolbar_frame.setStyleSheet(f"""
                background-color: white;
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 15px;
                border: 1px solid {AppStyles.BORDER_COLOR};
            """)
            
            toolbar_layout = QHBoxLayout(toolbar_frame)
            
            # الأزرار
            add_btn = QPushButton("إضافة عميل جديد")
            add_btn.setStyleSheet(AppStyles.BUTTON_STYLE)
            add_btn.clicked.connect(self.add_customer)
            
            edit_btn = QPushButton("تعديل")
            edit_btn.setStyleSheet(AppStyles.BUTTON_SECONDARY_STYLE)
            edit_btn.clicked.connect(self.edit_customer)
            
            delete_btn = QPushButton("حذف")
            delete_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {AppStyles.ERROR_COLOR};
                    color: white;
                    border: none;
                    border-radius: {AppStyles.BORDER_RADIUS}px;
                    padding: 10px 20px;
                    font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: #C62828;
                }}
            """)
            delete_btn.clicked.connect(self.delete_customer)
            
            # شريط البحث
            search_line = QLineEdit()
            search_line.setPlaceholderText("البحث في العملاء...")
            search_line.setStyleSheet(AppStyles.INPUT_STYLE)
            search_line.textChanged.connect(self.search_customers)
            
            toolbar_layout.addWidget(add_btn)
            toolbar_layout.addWidget(edit_btn)
            toolbar_layout.addWidget(delete_btn)
            toolbar_layout.addStretch()
            toolbar_layout.addWidget(QLabel("البحث:"))
            toolbar_layout.addWidget(search_line)
            
            return toolbar_frame
        except Exception as e:
            print("Error in create_toolbar:", e)
            return QFrame()
    
    def create_customers_table(self):
        """إنشاء جدول العملاء"""
        try:
            self.customers_table = QTableWidget()
            self.customers_table.setColumnCount(5)
            
            headers = ["اسم العميل", "رقم الهاتف", "البريد الإلكتروني", "العنوان", "الرصيد"]
            self.customers_table.setHorizontalHeaderLabels(headers)
            
            self.customers_table.setStyleSheet(AppStyles.TABLE_STYLE)
            self.customers_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
            self.customers_table.setAlternatingRowColors(True)
            self.customers_table.setSelectionBehavior(QTableWidget.SelectRows)
            
            self.load_sample_customers()
            
            return self.customers_table
        except Exception as e:
            print("Error in create_customers_table:", e)
            return QTableWidget()
    
    def load_sample_customers(self):
        """تحميل بيانات العملاء الوهمية"""
        try:
            sample_data = [
                ["أحمد محمد علي", "01234567890", "<EMAIL>", "القاهرة - مصر الجديدة", "1,500.00"],
                ["فاطمة أحمد", "01098765432", "<EMAIL>", "الجيزة - المهندسين", "-500.00"],
                ["محمد حسن", "01123456789", "<EMAIL>", "الإسكندرية - سيدي جابر", "2,300.00"],
                ["سارة محمود", "01087654321", "<EMAIL>", "القاهرة - مدينة نصر", "0.00"],
                ["علي عبدالله", "01156789012", "<EMAIL>", "الجيزة - الدقي", "750.00"],
            ]
            
            self.customers_table.setRowCount(len(sample_data))
            
            for row, row_data in enumerate(sample_data):
                for col, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(cell_data)
                    
                    # تلوين الرصيد حسب القيمة
                    if col == 4:  # عمود الرصيد
                        balance_text = cell_data.replace(",", "").replace(" ج.م", "")
                        try:
                            balance = float(balance_text)
                            if balance > 0:
                                item.setBackground(QColor(AppStyles.SUCCESS_COLOR + "30"))
                            elif balance < 0:
                                item.setBackground(QColor(AppStyles.ERROR_COLOR + "30"))
                        except:
                            pass
                    
                    self.customers_table.setItem(row, col, item)
        except Exception as e:
            print("Error in load_sample_customers:", e)
    
    def add_customer(self):
        """إضافة عميل جديد"""
        try:
            dialog = CustomerDialog(self)
            if dialog.exec() == QDialog.Accepted:
                QMessageBox.information(self, "نجح", "تم إضافة العميل بنجاح")
                self.refresh_table()
        except Exception as e:
            print("Error in add_customer:", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    def edit_customer(self):
        """تعديل عميل"""
        try:
            current_row = self.customers_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للتعديل")
                return
            
            dialog = CustomerDialog(self, edit_mode=True)
            if dialog.exec() == QDialog.Accepted:
                QMessageBox.information(self, "نجح", "تم تعديل العميل بنجاح")
                self.refresh_table()
        except Exception as e:
            print("Error in edit_customer:", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    def delete_customer(self):
        """حذف عميل"""
        try:
            current_row = self.customers_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للحذف")
                return
            
            customer_name = self.customers_table.item(current_row, 0).text()
            reply = QMessageBox.question(self, "تأكيد الحذف", 
                                       f"هل أنت متأكد من حذف العميل '{customer_name}'؟",
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                self.customers_table.removeRow(current_row)
                QMessageBox.information(self, "نجح", "تم حذف العميل بنجاح")
        except Exception as e:
            print("Error in delete_customer:", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    def search_customers(self, text):
        """البحث في العملاء"""
        try:
            for row in range(self.customers_table.rowCount()):
                match = False
                for col in range(self.customers_table.columnCount()):
                    item = self.customers_table.item(row, col)
                    if item and text.lower() in item.text().lower():
                        match = True
                        break
                self.customers_table.setRowHidden(row, not match)
        except Exception as e:
            print("Error in search_customers:", e)
    
    def refresh_table(self):
        """تحديث الجدول"""
        try:
            self.load_sample_customers()
        except Exception as e:
            print("Error in refresh_table:", e)


class CustomerDialog(QDialog):
    """نافذة حوار إضافة/تعديل عميل"""
    
    def __init__(self, parent=None, edit_mode=False):
        super().__init__(parent)
        self.edit_mode = edit_mode
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        try:
            title = "تعديل عميل" if self.edit_mode else "إضافة عميل جديد"
            self.setWindowTitle(title)
            self.setMinimumSize(500, 400)
            self.setStyleSheet(f"""
                QDialog {{
                    background-color: {AppStyles.BACKGROUND_COLOR};
                }}
            """)
            
            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)
            
            # عنوان
            title_label = QLabel(title)
            title_label.setStyleSheet(AppStyles.LABEL_HEADER_STYLE)
            
            # نموذج البيانات
            form_frame = QFrame()
            form_frame.setStyleSheet(f"""
                background-color: white;
                border-radius: {AppStyles.BORDER_RADIUS}px;
                padding: 20px;
                border: 1px solid {AppStyles.BORDER_COLOR};
            """)
            form_layout = QFormLayout(form_frame)
            
            # الحقول
            self.name_edit = QLineEdit()
            self.name_edit.setStyleSheet(AppStyles.INPUT_STYLE)
            
            self.phone_edit = QLineEdit()
            self.phone_edit.setStyleSheet(AppStyles.INPUT_STYLE)
            
            self.email_edit = QLineEdit()
            self.email_edit.setStyleSheet(AppStyles.INPUT_STYLE)
            
            self.address_edit = QTextEdit()
            self.address_edit.setMaximumHeight(80)
            self.address_edit.setStyleSheet(AppStyles.INPUT_STYLE)
            
            self.balance_spin = QDoubleSpinBox()
            self.balance_spin.setMinimum(-999999.99)
            self.balance_spin.setMaximum(999999.99)
            self.balance_spin.setSuffix(" ج.م")
            self.balance_spin.setStyleSheet(AppStyles.INPUT_STYLE)
            
            # إضافة الحقول
            form_layout.addRow("اسم العميل:", self.name_edit)
            form_layout.addRow("رقم الهاتف:", self.phone_edit)
            form_layout.addRow("البريد الإلكتروني:", self.email_edit)
            form_layout.addRow("العنوان:", self.address_edit)
            form_layout.addRow("الرصيد:", self.balance_spin)
            
            # أزرار الحوار
            button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            button_box.accepted.connect(self.accept)
            button_box.rejected.connect(self.reject)
            button_box.setStyleSheet(f"""
                QPushButton {{
                    {AppStyles.BUTTON_STYLE}
                    min-width: 80px;
                }}
            """)
            
            main_layout.addWidget(title_label)
            main_layout.addWidget(form_frame)
            main_layout.addWidget(button_box)
            
            if self.edit_mode:
                self.load_customer_data()
        except Exception as e:
            print("Error in CustomerDialog.init_ui:", e)
    
    def load_customer_data(self):
        """تحميل بيانات العميل في وضع التعديل"""
        try:
            # بيانات وهمية للتجربة
            self.name_edit.setText("أحمد محمد علي")
            self.phone_edit.setText("01234567890")
            self.email_edit.setText("<EMAIL>")
            self.address_edit.setText("القاهرة - مصر الجديدة")
            self.balance_spin.setValue(1500.00)
        except Exception as e:
            print("Error in load_customer_data:", e)