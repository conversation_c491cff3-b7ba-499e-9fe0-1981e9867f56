"""
نافذة تسجيل الدخول
"""
import os
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QLineEdit, QPushButton, QCheckBox, QFrame,
                              QMessageBox, QApplication, QSpacerItem, QSizePolicy)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon, QPixmap, QFont

from accounting_app.bll.user_manager import UserManager
from accounting_app.bll.translator import Translator
from accounting_app.bll.settings_manager import SettingsManager
from accounting_app.ui.styles import AppStyles


class LoginWindow(QWidget):
    """نافذة تسجيل الدخول"""
    
    def __init__(self, parent=None):
        """
        تهيئة نافذة تسجيل الدخول
        :param parent: النافذة الأم
        """
        super().__init__(parent)
        self.user_manager = UserManager()
        self.translator = Translator()
        self.settings_manager = SettingsManager()
        
        # تهيئة قاعدة البيانات
        self.user_manager.db.connect()
        self.user_manager.db.create_tables()
        self.user_manager.db.close()
        
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # إعداد النافذة
        self.setWindowTitle(self.translator.translate('login_title'))
        self.setMinimumSize(800, 600)
        self.setStyleSheet(AppStyles.LOGIN_WINDOW_STYLE)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # القسم الأيسر (الشعار والترحيب)
        left_widget = QWidget()
        left_widget.setStyleSheet(f"background-color: {AppStyles.PRIMARY_COLOR};")
        left_widget.setMinimumWidth(400)
        
        left_layout = QVBoxLayout(left_widget)
        left_layout.setAlignment(Qt.AlignCenter)
        left_layout.setSpacing(20)
        
        # شعار التطبيق
        logo_label = QLabel()
        # هنا يمكنك إضافة شعار التطبيق إذا كان متاحًا
        # logo_pixmap = QPixmap(":/images/logo.png")
        # logo_label.setPixmap(logo_pixmap.scaled(150, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        logo_label.setText("🏪")  # استخدام رمز تعبيري كشعار مؤقت
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("font-size: 100px;")
        
        # عنوان التطبيق
        app_name_label = QLabel(self.translator.translate('app_name'))
        app_name_label.setAlignment(Qt.AlignCenter)
        app_name_label.setStyleSheet(f"""
            color: white;
            font-size: {AppStyles.FONT_SIZE_XXLARGE}px;
            font-weight: bold;
        """)
        
        # وصف التطبيق
        app_desc_label = QLabel("نظام محاسبة متكامل للمحلات التجارية")
        app_desc_label.setAlignment(Qt.AlignCenter)
        app_desc_label.setStyleSheet(f"""
            color: white;
            font-size: {AppStyles.FONT_SIZE_LARGE}px;
        """)
        app_desc_label.setWordWrap(True)
        
        left_layout.addStretch()
        left_layout.addWidget(logo_label)
        left_layout.addWidget(app_name_label)
        left_layout.addWidget(app_desc_label)
        left_layout.addStretch()
        
        # القسم الأيمن (نموذج تسجيل الدخول)
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setAlignment(Qt.AlignCenter)
        right_layout.setContentsMargins(50, 50, 50, 50)
        
        # إطار تسجيل الدخول
        login_frame = QFrame()
        login_frame.setObjectName("loginFrame")
        login_frame.setStyleSheet(f"""
            QFrame#loginFrame {{
                background-color: white;
                border-radius: {AppStyles.BORDER_RADIUS * 2}px;
                padding: 20px;
            }}
        """)
        login_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        login_layout = QVBoxLayout(login_frame)
        login_layout.setSpacing(20)
        login_layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان تسجيل الدخول
        title_label = QLabel(self.translator.translate('login_title'))
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignCenter)
        
        # حقل اسم المستخدم
        username_label = QLabel(self.translator.translate('username'))
        username_label.setStyleSheet(AppStyles.LABEL_STYLE)
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText(self.translator.translate('username'))
        self.username_input.setStyleSheet(AppStyles.INPUT_STYLE)
        self.username_input.setMinimumHeight(40)
        
        # حقل كلمة المرور
        password_label = QLabel(self.translator.translate('password'))
        password_label.setStyleSheet(AppStyles.LABEL_STYLE)
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText(self.translator.translate('password'))
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(AppStyles.INPUT_STYLE)
        self.password_input.setMinimumHeight(40)
        
        # خيار تذكرني
        self.remember_me = QCheckBox(self.translator.translate('remember_me'))
        self.remember_me.setStyleSheet(f"""
            QCheckBox {{
                color: {AppStyles.TEXT_PRIMARY};
                font-size: {AppStyles.FONT_SIZE_NORMAL}px;
                spacing: 10px;
                font-weight: 500;
            }}
            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
                border-radius: 4px;
                border: 2px solid {AppStyles.BORDER_COLOR};
                background-color: white;
            }}
            QCheckBox::indicator:checked {{
                background-color: {AppStyles.PRIMARY_COLOR};
                border-color: {AppStyles.PRIMARY_COLOR};
            }}
            QCheckBox::indicator:hover {{
                border-color: {AppStyles.PRIMARY_COLOR};
            }}
        """)
        
        # تحميل حالة تذكرني
        self.load_saved_credentials()
        
        # زر تسجيل الدخول
        self.login_button = QPushButton(self.translator.translate('login_button'))
        self.login_button.setStyleSheet(AppStyles.BUTTON_STYLE)
        self.login_button.setMinimumHeight(40)
        self.login_button.setCursor(Qt.PointingHandCursor)
        self.login_button.clicked.connect(self.login)
        
        # رابط إنشاء حساب جديد
        self.create_account_button = QPushButton(self.translator.translate('create_account'))
        self.create_account_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #1976D2;
                border: none;
                text-decoration: underline;
            }
            QPushButton:hover {
                color: #1565C0;
            }
        """)
        self.create_account_button.setCursor(Qt.PointingHandCursor)
        self.create_account_button.clicked.connect(self.open_register_window)
        
        # إضافة العناصر إلى تخطيط تسجيل الدخول
        login_layout.addWidget(title_label)
        login_layout.addItem(QSpacerItem(20, 20))
        login_layout.addWidget(username_label)
        login_layout.addWidget(self.username_input)
        login_layout.addItem(QSpacerItem(20, 10))
        login_layout.addWidget(password_label)
        login_layout.addWidget(self.password_input)
        login_layout.addItem(QSpacerItem(20, 10))
        login_layout.addWidget(self.remember_me)
        login_layout.addItem(QSpacerItem(20, 20))
        login_layout.addWidget(self.login_button)
        login_layout.addItem(QSpacerItem(20, 20))
        login_layout.addWidget(self.create_account_button, alignment=Qt.AlignCenter)
        
        # إضافة إطار تسجيل الدخول إلى القسم الأيمن
        right_layout.addStretch()
        right_layout.addWidget(login_frame)
        right_layout.addStretch()
        
        # إضافة القسمين إلى التخطيط الرئيسي
        main_layout.addWidget(left_widget)
        main_layout.addWidget(right_widget)
        
        # تعيين التركيز على حقل اسم المستخدم
        self.username_input.setFocus()
    
    def login(self):
        """التحقق من بيانات تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        if not username or not password:
            QMessageBox.warning(
                self,
                self.translator.translate('login_error'),
                self.translator.translate('required_field')
            )
            return
        
        # تعطيل زر تسجيل الدخول وتغيير النص
        self.login_button.setEnabled(False)
        self.login_button.setText("جاري تسجيل الدخول...")
        
        # استخدام QTimer لتأخير التحقق قليلاً للسماح بتحديث واجهة المستخدم
        from PySide6.QtCore import QTimer
        QTimer.singleShot(100, lambda: self.process_login(username, password))
    
    def process_login(self, username, password):
        """معالجة عملية تسجيل الدخول"""
        try:
            # التحقق من صحة بيانات المستخدم
            user = self.user_manager.authenticate(username, password)
            
            if user:
                # حفظ بيانات تذكرني إذا كان الخيار مفعل
                if self.remember_me.isChecked():
                    self.save_credentials(username, password)
                else:
                    self.clear_saved_credentials()
                
                # تحميل النافذة الرئيسية مسبقاً
                from accounting_app.ui.dashboard_window import DashboardWindow
                self.dashboard_window = DashboardWindow(user)
                
                # عرض رسالة ترحيب مختصرة
                from PySide6.QtWidgets import QLabel
                from PySide6.QtCore import Qt, QTimer
                
                # إنشاء رسالة ترحيب مؤقتة
                welcome_message = QLabel(f"مرحباً {user[3] if user[3] else username}!", self)
                welcome_message.setStyleSheet(f"""
                    background-color: {AppStyles.PRIMARY_COLOR};
                    color: white;
                    padding: 15px;
                    border-radius: {AppStyles.BORDER_RADIUS}px;
                    font-size: {AppStyles.FONT_SIZE_LARGE}px;
                    font-weight: bold;
                """)
                welcome_message.setAlignment(Qt.AlignCenter)
                welcome_message.setFixedSize(300, 80)
                welcome_message.move(
                    (self.width() - welcome_message.width()) // 2,
                    (self.height() - welcome_message.height()) // 2
                )
                welcome_message.show()
                
                # فتح النافذة الرئيسية بعد فترة قصيرة
                QTimer.singleShot(800, lambda: self.show_dashboard(welcome_message))
            else:
                # فشل تسجيل الدخول
                QMessageBox.critical(
                    self,
                    self.translator.translate('login_error'),
                    self.translator.translate('invalid_credentials')
                )
                # إعادة تفعيل زر تسجيل الدخول
                self.login_button.setEnabled(True)
                self.login_button.setText(self.translator.translate('login_button'))
        except Exception as e:
            print("Error in process_login:", e)
            import traceback
            traceback.print_exc()
            QMessageBox.critical(
                self,
                "خطأ في تسجيل الدخول",
                f"حدث خطأ أثناء تسجيل الدخول: {str(e)}"
            )
            # إعادة تفعيل زر تسجيل الدخول
            self.login_button.setEnabled(True)
            self.login_button.setText(self.translator.translate('login_button'))
    
    def show_dashboard(self, welcome_message=None):
        """عرض لوحة التحكم"""
        try:
            # إخفاء رسالة الترحيب إذا كانت موجودة
            if welcome_message:
                welcome_message.hide()
            
            # عرض النافذة الرئيسية
            self.dashboard_window.show()
            
            # السماح للتطبيق بالإغلاق عند إغلاق النافذة الرئيسية
            QApplication.instance().setQuitOnLastWindowClosed(True)
            
            # إخفاء نافذة تسجيل الدخول بدلاً من إغلاقها
            self.hide()
        except Exception as e:
            print("Error in show_dashboard:", e)
            import traceback
            traceback.print_exc()
            QMessageBox.critical(
                self,
                "خطأ في فتح النافذة الرئيسية",
                f"حدث خطأ أثناء فتح النافذة الرئيسية: {str(e)}"
            )
    
    def open_register_window(self):
        from accounting_app.ui.register_window import RegisterWindow
        self.register_window = RegisterWindow()
        self.register_window.show()
        self.close()
    
    def open_main_window(self, user):
        """
        فتح النافذة الرئيسية (للتوافق مع الكود القديم)
        :param user: بيانات المستخدم
        """
        # تعطيل زر تسجيل الدخول وتغيير النص
        self.login_button.setEnabled(False)
        self.login_button.setText("جاري تسجيل الدخول...")
        
        # استخدام الطريقة الجديدة لفتح النافذة الرئيسية
        self.process_login(user[1], user[2])
    
    def save_credentials(self, username, password):
        """حفظ بيانات تسجيل الدخول"""
        try:
            import json
            import os
            from base64 import b64encode
            
            # إنشاء مجلد الإعدادات إذا لم يكن موجوداً
            settings_dir = os.path.join(os.path.expanduser("~"), ".accounting_app")
            if not os.path.exists(settings_dir):
                os.makedirs(settings_dir)
            
            # حفظ البيانات (مشفرة بسيطة)
            credentials = {
                "username": username,
                "password": b64encode(password.encode()).decode(),
                "remember": True
            }
            
            settings_file = os.path.join(settings_dir, "credentials.json")
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(credentials, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print("Error saving credentials:", e)
    
    def load_saved_credentials(self):
        """تحميل بيانات تسجيل الدخول المحفوظة"""
        try:
            import json
            import os
            from base64 import b64decode
            
            settings_dir = os.path.join(os.path.expanduser("~"), ".accounting_app")
            settings_file = os.path.join(settings_dir, "credentials.json")
            
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    credentials = json.load(f)
                
                if credentials.get("remember", False):
                    self.username_input.setText(credentials.get("username", ""))
                    self.password_input.setText(b64decode(credentials.get("password", "")).decode())
                    self.remember_me.setChecked(True)
                    
        except Exception as e:
            print("Error loading credentials:", e)
    
    def clear_saved_credentials(self):
        """مسح بيانات تسجيل الدخول المحفوظة"""
        try:
            import os
            
            settings_dir = os.path.join(os.path.expanduser("~"), ".accounting_app")
            settings_file = os.path.join(settings_dir, "credentials.json")
            
            if os.path.exists(settings_file):
                os.remove(settings_file)
                
        except Exception as e:
            print("Error clearing credentials:", e)
    
    def closeEvent(self, event):
        """معالجة حدث إغلاق نافذة تسجيل الدخول"""
        try:
            # عرض رسالة تأكيد قبل الإغلاق
            reply = QMessageBox.question(
                self, 
                "تأكيد الإغلاق", 
                "هل تريد فعلاً إغلاق التطبيق؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # قبول الإغلاق
                event.accept()
                # إغلاق التطبيق بالكامل
                QApplication.instance().quit()
            else:
                # رفض الإغلاق
                event.ignore()
                
        except Exception as e:
            print("Error in closeEvent:", e)
            # في حالة الخطأ، السماح بالإغلاق
            event.accept()
            QApplication.instance().quit()