"""
صفحة المنتجات المحسنة مع كتالوج تفاعلي وإدارة متقدمة
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QTableWidget, QTableWidgetItem,
                              QHeaderView, QComboBox, QLineEdit, QSpinBox,
                              QDoubleSpinBox, QTextEdit, QGroupBox, QGridLayout,
                              QScrollArea, QSplitter, QTabWidget, QFormLayout,
                              QMessageBox, QDialog, QDialogButtonBox, QCheckBox,
                              QProgressBar, QFileDialog, QMenu, QApplication,
                              QCalendarWidget, QDateEdit, QListWidget, QListWidgetItem)
from PySide6.QtCore import Qt, QThread, Signal, QTimer, QPropertyAnimation, QEasingCurve, QDate
from PySide6.QtGui import QFont, QColor, QPainter, QPen, QBrush, QPixmap, QAction, QCursor
import json
import random
from datetime import datetime, timedelta

from accounting_app.bll.translator import Translator
from accounting_app.ui.styles import AppStyles


class ProductFormDialog(QDialog):
    """نافذة إضافة/تعديل المنتجات"""
    
    def __init__(self, product_data=None, parent=None):
        super().__init__(parent)
        self.product_data = product_data
        self.is_edit_mode = product_data is not None
        self.init_ui()
        if self.is_edit_mode:
            self.load_product_data()
    
    def init_ui(self):
        """إعداد واجهة النافذة"""
        title = "تعديل المنتج" if self.is_edit_mode else "إضافة منتج جديد"
        self.setWindowTitle(title)
        self.setFixedSize(600, 550)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # عنوان النافذة
        header_label = QLabel(f"🛍️ {title}")
        header_label.setStyleSheet("""
            color: #28a745;
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
        """)
        header_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(header_label)
        
        # التبويبات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background: white;
                border-radius: 8px;
            }
            QTabBar::tab {
                background: #f8f9fa;
                padding: 10px 15px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                border: 1px solid #dee2e6;
                color: #495057;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background: white;
                color: #28a745;
                border-bottom: 1px solid white;
            }
            QTabBar::tab:hover {
                background: #e9ecef;
            }
        """)
        
        # تبويب المعلومات الأساسية
        basic_tab = self.create_basic_info_tab()
        self.tabs.addTab(basic_tab, "📝 المعلومات الأساسية")
        
        # تبويب الأسعار والمخزون
        pricing_tab = self.create_pricing_tab()
        self.tabs.addTab(pricing_tab, "💰 الأسعار والمخزون")
        
        layout.addWidget(self.tabs)
        
        # أزرار التحكم
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        buttons_layout = QHBoxLayout(buttons_frame)
        
        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet(self.get_button_style("#28a745"))
        save_btn.clicked.connect(self.save_product)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet(self.get_button_style("#6c757d"))
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addWidget(buttons_frame)
    
    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        tab_widget = QWidget()
        layout = QFormLayout(tab_widget)
        layout.setSpacing(15)
        
        # كود المنتج
        self.product_code = QLineEdit()
        self.product_code.setReadOnly(True)
        self.product_code.setText(f"PRD-{datetime.now().strftime('%Y%m%d%H%M')}")
        self.product_code.setStyleSheet(self.get_input_style())
        layout.addRow("كود المنتج:", self.product_code)
        
        # اسم المنتج
        self.product_name = QLineEdit()
        self.product_name.setPlaceholderText("اسم المنتج")  
        self.product_name.setStyleSheet(self.get_input_style())
        layout.addRow("اسم المنتج:", self.product_name)
        
        # الفئة
        self.category_combo = QComboBox()
        self.category_combo.addItems(["إلكترونيات", "ملابس", "أغذية", "أدوات منزلية", "كتب"])
        self.category_combo.setStyleSheet(self.get_input_style())
        layout.addRow("الفئة:", self.category_combo)
        
        # الوصف
        self.description = QTextEdit()
        self.description.setPlaceholderText("وصف المنتج...")
        self.description.setMaximumHeight(80)
        self.description.setStyleSheet(self.get_input_style())
        layout.addRow("الوصف:", self.description)
        
        # الوحدة
        self.unit_combo = QComboBox()
        self.unit_combo.addItems(["قطعة", "كيلو", "لتر", "متر", "علبة", "كرتونة"])
        self.unit_combo.setStyleSheet(self.get_input_style())
        layout.addRow("الوحدة:", self.unit_combo)
        
        # الباركود
        self.barcode = QLineEdit()
        self.barcode.setPlaceholderText("الباركود...")
        self.barcode.setStyleSheet(self.get_input_style())
        layout.addRow("الباركود:", self.barcode)
        
        return tab_widget
    
    def create_pricing_tab(self):
        """إنشاء تبويب الأسعار والمخزون"""
        tab_widget = QWidget()
        layout = QFormLayout(tab_widget)
        layout.setSpacing(15)
        
        # سعر الشراء
        self.purchase_price = QDoubleSpinBox()
        self.purchase_price.setRange(0, 999999)
        self.purchase_price.setSuffix(" ج.م")
        self.purchase_price.setStyleSheet(self.get_input_style())
        layout.addRow("سعر الشراء:", self.purchase_price)
        
        # سعر البيع
        self.selling_price = QDoubleSpinBox()
        self.selling_price.setRange(0, 999999)
        self.selling_price.setSuffix(" ج.م")
        self.selling_price.setStyleSheet(self.get_input_style())
        layout.addRow("سعر البيع:", self.selling_price)
        
        # الكمية المتاحة
        self.stock_quantity = QSpinBox()
        self.stock_quantity.setRange(0, 99999)
        self.stock_quantity.setStyleSheet(self.get_input_style())
        layout.addRow("الكمية المتاحة:", self.stock_quantity)
        
        # الحد الأدنى للمخزون
        self.min_stock = QSpinBox()
        self.min_stock.setRange(0, 9999)
        self.min_stock.setStyleSheet(self.get_input_style())
        layout.addRow("الحد الأدنى للمخزون:", self.min_stock)
        
        # تاريخ انتهاء الصلاحية
        self.expiry_date = QDateEdit()
        self.expiry_date.setDate(QDate.currentDate().addYears(1))
        self.expiry_date.setCalendarPopup(True)
        self.expiry_date.setStyleSheet(self.get_input_style())
        layout.addRow("تاريخ انتهاء الصلاحية:", self.expiry_date)
        
        # المورد
        self.supplier_combo = QComboBox()
        self.supplier_combo.addItems(["مورد 1", "مورد 2", "مورد 3"])
        self.supplier_combo.setStyleSheet(self.get_input_style())
        layout.addRow("المورد:", self.supplier_combo)
        
        return tab_widget
    
    def load_product_data(self):
        """تحميل بيانات المنتج للتعديل"""
        if not self.product_data:
            return
        
        self.product_name.setText(self.product_data.get("name", ""))
        self.description.setPlainText(self.product_data.get("description", ""))
        self.purchase_price.setValue(self.product_data.get("purchase_price", 0))
        self.selling_price.setValue(self.product_data.get("selling_price", 0))
        self.stock_quantity.setValue(self.product_data.get("stock", 0))
    
    def save_product(self):
        """حفظ بيانات المنتج"""
        if not self.product_name.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المنتج!")
            return
        
        product_data = {
            'code': self.product_code.text(),
            'name': self.product_name.text().strip(),
            'category': self.category_combo.currentText(),
            'description': self.description.toPlainText().strip(),
            'unit': self.unit_combo.currentText(),
            'barcode': self.barcode.text().strip(),
            'purchase_price': self.purchase_price.value(),
            'selling_price': self.selling_price.value(),
            'stock_quantity': self.stock_quantity.value(),
            'min_stock': self.min_stock.value(),
            'expiry_date': self.expiry_date.date().toString(),
            'supplier': self.supplier_combo.currentText()
        }
        
        action = "تحديث" if self.is_edit_mode else "إضافة"
        QMessageBox.information(self, "تم الحفظ", f"تم {action} المنتج بنجاح!")
        self.accept()
    
    def get_input_style(self):
        return """
            QLineEdit, QComboBox, QTextEdit, QSpinBox, QDoubleSpinBox, QDateEdit {
                padding: 8px 12px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 12px;
                background: white;
            }
            QLineEdit:focus, QComboBox:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                border-color: #28a745;
            }
        """
    
    def get_button_style(self, color):
        return f"""
            QPushButton {{
                background: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 25px;
                font-size: 14px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                opacity: 0.9;
            }}
        """


class ProductCatalogWidget(QWidget):
    """واجهة كتالوج المنتجات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.load_catalog()
    
    def init_ui(self):
        """إعداد واجهة الكتالوج"""
        self.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 8px;
                border: 1px solid #dee2e6;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # عنوان الكتالوج
        title = QLabel("📚 كتالوج المنتجات")
        title.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
            padding: 10px 0;
            border-bottom: 2px solid #e9ecef;
        """)
        layout.addWidget(title)
        
        # الإحصائيات السريعة
        stats_frame = QFrame()
        stats_layout = QGridLayout(stats_frame)
        
        # إجمالي المنتجات
        total_products = self.create_stat_card("📦", "1,847", "إجمالي المنتجات", "#28a745")
        stats_layout.addWidget(total_products, 0, 0)
        
        # منتجات جديدة
        new_products = self.create_stat_card("🆕", "23", "منتجات جديدة", "#007bff")
        stats_layout.addWidget(new_products, 0, 1)
        
        # نفدت من المخزون
        out_of_stock = self.create_stat_card("⚠️", "8", "نفدت من المخزون", "#dc3545")
        stats_layout.addWidget(out_of_stock, 1, 0)
        
        # الأكثر مبيعاً
        best_selling = self.create_stat_card("🔥", "156", "الأكثر مبيعاً", "#ffc107")
        stats_layout.addWidget(best_selling, 1, 1)
        
        layout.addWidget(stats_frame)
        
        # الفئات السريعة
        categories_title = QLabel("📂 الفئات الرئيسية")
        categories_title.setStyleSheet("""
            color: #495057;
            font-size: 14px;
            font-weight: bold;
            margin-top: 10px;
        """)
        layout.addWidget(categories_title)
        
        self.categories_list = QListWidget()
        self.categories_list.setMaximumHeight(150)
        self.categories_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #e9ecef;
                border-radius: 4px;
                background: #f8f9fa;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }
            QListWidget::item:hover {
                background: #e8f5e8;
            }
            QListWidget::item:selected {
                background: #28a745;
                color: white;
            }
        """)
        layout.addWidget(self.categories_list)
        
        layout.addStretch()
    
    def create_stat_card(self, icon, value, label, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: {color};
                border-radius: 8px;
                padding: 15px;
                border: none;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("color: white; font-size: 24px;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 20px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: white; font-size: 12px;")
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(label_widget)
        
        return card
    
    def load_catalog(self):
        """تحميل فئات الكتالوج"""
        categories = [
            "📱 إلكترونيات - 345 منتج",
            "👕 ملابس - 289 منتج", 
            "🍎 أغذية - 567 منتج",
            "🏠 أدوات منزلية - 123 منتج",
            "📚 كتب - 234 منتج",
            "⚽ رياضة - 89 منتج"
        ]
        
        for category in categories:
            item = QListWidgetItem(category)
            self.categories_list.addItem(item)


class ProductsTableWidget(QTableWidget):
    """جدول المنتجات المحسن"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_table()
        self.load_products_data()
    
    def init_table(self):
        """إعداد الجدول"""
        columns = [
            "الكود", "اسم المنتج", "الفئة", "الوحدة", "سعر الشراء", 
            "سعر البيع", "المخزون", "الحد الأدنى", "الحالة", "الإجراءات"
        ]
        
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e8f5e8;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 12px;
            }
            QHeaderView::section {
                background: #28a745;
                color: white;
                padding: 12px 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #28a745;
                color: white;
            }
        """)
        
        # إعدادات الجدول
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        self.verticalHeader().setVisible(False)
        
        # تحديد عرض الأعمدة
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)   # الكود  
        header.setSectionResizeMode(1, QHeaderView.Stretch) # اسم المنتج
        header.setSectionResizeMode(2, QHeaderView.Fixed)   # الفئة
        header.setSectionResizeMode(3, QHeaderView.Fixed)   # الوحدة
        header.setSectionResizeMode(4, QHeaderView.Fixed)   # سعر الشراء
        header.setSectionResizeMode(5, QHeaderView.Fixed)   # سعر البيع
        header.setSectionResizeMode(6, QHeaderView.Fixed)   # المخزون
        header.setSectionResizeMode(7, QHeaderView.Fixed)   # الحد الأدنى
        header.setSectionResizeMode(8, QHeaderView.Fixed)   # الحالة
        header.setSectionResizeMode(9, QHeaderView.Fixed)   # الإجراءات
        
        self.setColumnWidth(0, 80)   # الكود
        self.setColumnWidth(2, 100)  # الفئة
        self.setColumnWidth(3, 70)   # الوحدة
        self.setColumnWidth(4, 90)   # سعر الشراء
        self.setColumnWidth(5, 90)   # سعر البيع
        self.setColumnWidth(6, 80)   # المخزون
        self.setColumnWidth(7, 90)   # الحد الأدنى
        self.setColumnWidth(8, 80)   # الحالة
        self.setColumnWidth(9, 120)  # الإجراءات
    
    def load_products_data(self):
        """تحميل بيانات المنتجات"""
        products_data = [
            {"code": "PRD001", "name": "لابتوب ديل XPS", "category": "إلكترونيات", 
             "unit": "قطعة", "purchase_price": 12000, "selling_price": 15000,
             "stock": 25, "min_stock": 5, "status": "متاح"},
            {"code": "PRD002", "name": "هاتف ذكي سامسونج", "category": "إلكترونيات", 
             "unit": "قطعة", "purchase_price": 6000, "selling_price": 8500,
             "stock": 3, "min_stock": 5, "status": "منخفض"},
            {"code": "PRD003", "name": "قميص قطني", "category": "ملابس", 
             "unit": "قطعة", "purchase_price": 50, "selling_price": 80,
             "stock": 0, "min_stock": 10, "status": "نفد"},
            {"code": "PRD004", "name": "كتاب البرمجة", "category": "كتب", 
             "unit": "قطعة", "purchase_price": 80, "selling_price": 120,
             "stock": 45, "min_stock": 15, "status": "متاح"},
            {"code": "PRD005", "name": "مكواة كهربائية", "category": "أدوات منزلية", 
             "unit": "قطعة", "purchase_price": 150, "selling_price": 220,
             "stock": 8, "min_stock": 12, "status": "منخفض"},
        ]
        
        self.setRowCount(len(products_data))
        
        for row, product in enumerate(products_data):
            # الكود
            self.setItem(row, 0, QTableWidgetItem(product["code"]))
            
            # اسم المنتج
            self.setItem(row, 1, QTableWidgetItem(product["name"]))
            
            # الفئة
            category_item = QTableWidgetItem(product["category"])
            category_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 2, category_item)
            
            # الوحدة
            unit_item = QTableWidgetItem(product["unit"])
            unit_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 3, unit_item)
            
            # سعر الشراء
            purchase_item = QTableWidgetItem(f"{product['purchase_price']:,.0f}")
            purchase_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 4, purchase_item)
            
            # سعر البيع
            selling_item = QTableWidgetItem(f"{product['selling_price']:,.0f}")
            selling_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 5, selling_item)
            
            # المخزون
            stock_item = QTableWidgetItem(str(product["stock"]))
            stock_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 6, stock_item)
            
            # الحد الأدنى
            min_item = QTableWidgetItem(str(product["min_stock"]))
            min_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 7, min_item)
            
            # الحالة
            status_colors = {
                "متاح": {"bg": "#d4edda", "fg": "#155724"},
                "منخفض": {"bg": "#fff3cd", "fg": "#856404"},
                "نفد": {"bg": "#f8d7da", "fg": "#721c24"}
            }
            
            status_item = QTableWidgetItem(product["status"])
            status_item.setTextAlignment(Qt.AlignCenter)
            color = status_colors.get(product["status"], status_colors["متاح"])
            status_item.setBackground(QColor(color["bg"]))
            status_item.setForeground(QColor(color["fg"]))
            self.setItem(row, 8, status_item)
            
            # الإجراءات
            actions_widget = self.create_actions_widget(row, product)
            self.setCellWidget(row, 9, actions_widget)
    
    def create_actions_widget(self, row, product_data):
        """إنشاء أزرار الإجراءات"""
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(5, 2, 5, 2)
        actions_layout.setSpacing(3)
        
        # زر التعديل
        edit_btn = QPushButton("✏️")
        edit_btn.setFixedSize(22, 22)
        edit_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        edit_btn.setToolTip("تعديل المنتج")
        edit_btn.clicked.connect(lambda: self.edit_product(row, product_data))
        
        # زر عرض التفاصيل
        view_btn = QPushButton("👁️")
        view_btn.setFixedSize(22, 22)
        view_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        view_btn.setToolTip("عرض التفاصيل")
        view_btn.clicked.connect(lambda: self.view_product_details(row, product_data))
        
        # زر الحذف
        delete_btn = QPushButton("🗑️")
        delete_btn.setFixedSize(22, 22)
        delete_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        delete_btn.setToolTip("حذف المنتج")
        delete_btn.clicked.connect(lambda: self.delete_product(row, product_data))
        
        actions_layout.addWidget(edit_btn)
        actions_layout.addWidget(view_btn)
        actions_layout.addWidget(delete_btn)
        actions_layout.addStretch()
        
        return actions_widget
    
    def edit_product(self, row, product_data):
        """تعديل المنتج"""
        dialog = ProductFormDialog(product_data, self)
        if dialog.exec() == QDialog.Accepted:
            self.load_products_data()
    
    def view_product_details(self, row, product_data):
        """عرض تفاصيل المنتج"""
        details_text = f"""
        🛍️ تفاصيل المنتج:
        
        🏷️ الكود: {product_data['code']}
        📛 الاسم: {product_data['name']}
        📂 الفئة: {product_data['category']}
        📏 الوحدة: {product_data['unit']}
        💰 سعر الشراء: {product_data['purchase_price']:,.0f} ج.م
        💵 سعر البيع: {product_data['selling_price']:,.0f} ج.م
        📦 المخزون: {product_data['stock']} {product_data['unit']}
        ⚠️ الحد الأدنى: {product_data['min_stock']} {product_data['unit']}
        ✅ الحالة: {product_data['status']}
        """
        
        QMessageBox.information(self, "تفاصيل المنتج", details_text)
    
    def delete_product(self, row, product_data):
        """حذف المنتج"""
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل تريد حذف المنتج '{product_data['name']}'؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الحذف", "تم حذف المنتج بنجاح!")
            self.load_products_data()


class EnhancedProductsWindow(QWidget):
    """نافذة المنتجات المحسنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # المحتوى الرئيسي
        content_splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - الكتالوج
        left_panel = self.create_left_panel()
        left_panel.setMaximumWidth(300)
        content_splitter.addWidget(left_panel)
        
        # الجانب الأيمن - جدول المنتجات
        right_panel = self.create_right_panel()
        content_splitter.addWidget(right_panel)
        
        content_splitter.setSizes([300, 900])
        main_layout.addWidget(content_splitter)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
    
    def create_header(self):
        """إنشاء الرأس"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #28a745, stop:0.5 #20c997, stop:1 #28a745);
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # معلومات الصفحة
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        title = QLabel("🛍️ إدارة المنتجات")
        title.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
        """)
        
        subtitle = QLabel("كتالوج تفاعلي وإدارة شاملة للمنتجات والمخزون")
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-top: 5px;
        """)
        
        info_layout.addWidget(title)
        info_layout.addWidget(subtitle)
        
        # الإحصائيات السريعة
        stats_widget = self.create_products_stats()
        
        layout.addWidget(info_widget, 7)
        layout.addWidget(stats_widget, 3)
        
        return header_frame
    
    def create_products_stats(self):
        """إنشاء إحصائيات المنتجات"""
        stats_widget = QWidget()
        stats_layout = QHBoxLayout(stats_widget)
        stats_layout.setSpacing(10)
        
        # إجمالي المنتجات
        total_stat = self.create_stat_card("📦", "1,847", "إجمالي المنتجات")
        stats_layout.addWidget(total_stat)
        
        # نفدت من المخزون
        out_of_stock_stat = self.create_stat_card("⚠️", "8", "نفدت من المخزون")
        stats_layout.addWidget(out_of_stock_stat)
        
        # الأكثر مبيعاً
        best_selling_stat = self.create_stat_card("🔥", "156", "الأكثر مبيعاً")
        stats_layout.addWidget(best_selling_stat)
        
        return stats_widget
    
    def create_stat_card(self, icon, value, label):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("color: white; font-size: 20px;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 10px;")
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(label_widget)
        
        return card
    
    def create_left_panel(self):
        """إنشاء اللوحة اليسرى"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(15)
        
        # الكتالوج
        self.catalog_widget = ProductCatalogWidget()
        left_layout.addWidget(self.catalog_widget)
        
        return left_widget
    
    def create_right_panel(self):
        """إنشاء اللوحة اليمنى"""
        right_frame = QFrame()
        right_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        
        layout = QVBoxLayout(right_frame)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # الجدول
        self.products_table = ProductsTableWidget()
        layout.addWidget(self.products_table)
        
        return right_frame
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        
        # زر إضافة منتج جديد
        add_btn = QPushButton("➕ إضافة منتج")
        add_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        add_btn.clicked.connect(self.add_new_product)
        
        # زر استيراد
        import_btn = QPushButton("📥 استيراد")
        import_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        import_btn.clicked.connect(self.import_products)
        
        # زر تصدير
        export_btn = QPushButton("📤 تصدير")
        export_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        export_btn.clicked.connect(self.export_products)
        
        layout.addWidget(add_btn)
        layout.addWidget(import_btn)
        layout.addWidget(export_btn)
        layout.addStretch()
        
        # البحث والفلتر
        filter_combo = QComboBox()
        filter_combo.addItems(["جميع المنتجات", "إلكترونيات", "ملابس", "أغذية", "أدوات منزلية", "كتب"])
        filter_combo.setStyleSheet("""
            QComboBox {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
                font-size: 12px;
            }
        """)
        
        search_input = QLineEdit()
        search_input.setPlaceholderText("البحث في المنتجات...")
        search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #28a745;
                border-radius: 20px;
                font-size: 12px;
                background: white;
            }
            QLineEdit:focus {
                border-color: #218838;
            }
        """)
        search_input.textChanged.connect(self.search_products)
        
        layout.addWidget(QLabel("الفلتر:"))
        layout.addWidget(filter_combo)
        layout.addWidget(search_input)
        
        return toolbar_frame
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFixedHeight(40)
        status_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 8px 15px;
            }
        """)
        
        layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز - كتالوج المنتجات محدث")
        self.status_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 12px;")
        
        self.products_count_label = QLabel("5 منتجات")
        self.products_count_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        
        self.total_value_label = QLabel("قيمة المخزون: 545,820 ج.م")
        self.total_value_label.setStyleSheet("color: #007bff; font-weight: bold; font-size: 12px;")
        
        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.products_count_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.total_value_label)
        
        return status_frame
    
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_data)
        self.update_timer.start(60000)  # تحديث كل دقيقة
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.catalog_widget.load_catalog()
        self.status_label.setText(f"تم التحديث - {datetime.now().strftime('%H:%M')}")
    
    def add_new_product(self):
        """إضافة منتج جديد"""
        dialog = ProductFormDialog(parent=self)
        if dialog.exec() == QDialog.Accepted:
            self.products_table.load_products_data()
            self.status_label.setText("تم إضافة منتج جديد")
    
    def import_products(self):
        """استيراد المنتجات"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "استيراد المنتجات", "",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;All Files (*)"
        )
        
        if file_path:
            QMessageBox.information(self, "استيراد", f"تم استيراد المنتجات من:\n{file_path}")
    
    def export_products(self):
        """تصدير المنتجات"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير المنتجات", "products.xlsx",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;PDF Files (*.pdf)"
        )
        
        if file_path:
            QMessageBox.information(self, "تصدير", f"تم تصدير المنتجات إلى:\n{file_path}")
    
    def search_products(self, search_text):
        """البحث في المنتجات"""
        if not search_text.strip():
            self.products_table.load_products_data()
            return
        
        # هنا يمكن تطبيق منطق البحث الفعلي
        self.status_label.setText(f"البحث عن: {search_text}")


class ProductCatalogWidget(QWidget):
    """واجهة كتالوج المنتجات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.load_catalog()
    
    def init_ui(self):
        """إعداد واجهة الكتالوج"""
        self.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 8px;
                border: 1px solid #dee2e6;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # عنوان الكتالوج
        title = QLabel("📚 كتالوج المنتجات")
        title.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
            padding: 10px 0;
            border-bottom: 2px solid #e9ecef;
        """)
        layout.addWidget(title)
        
        # الإحصائيات السريعة
        stats_frame = QFrame()
        stats_layout = QGridLayout(stats_frame)
        
        # إجمالي المنتجات
        total_products = self.create_stat_card("📦", "1,847", "إجمالي المنتجات", "#28a745")
        stats_layout.addWidget(total_products, 0, 0)
        
        # منتجات جديدة
        new_products = self.create_stat_card("🆕", "23", "منتجات جديدة", "#007bff")
        stats_layout.addWidget(new_products, 0, 1)
        
        # نفدت من المخزون
        out_of_stock = self.create_stat_card("⚠️", "8", "نفدت من المخزون", "#dc3545")
        stats_layout.addWidget(out_of_stock, 1, 0)
        
        # الأكثر مبيعاً
        best_selling = self.create_stat_card("🔥", "156", "الأكثر مبيعاً", "#ffc107")
        stats_layout.addWidget(best_selling, 1, 1)
        
        layout.addWidget(stats_frame)
        
        # الفئات السريعة
        categories_title = QLabel("📂 الفئات الرئيسية")
        categories_title.setStyleSheet("""
            color: #495057;
            font-size: 14px;
            font-weight: bold;
            margin-top: 10px;
        """)
        layout.addWidget(categories_title)
        
        self.categories_list = QListWidget()
        self.categories_list.setMaximumHeight(150)
        self.categories_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #e9ecef;
                border-radius: 4px;
                background: #f8f9fa;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }
            QListWidget::item:hover {
                background: #e8f5e8;
            }
            QListWidget::item:selected {
                background: #28a745;
                color: white;
            }
        """)
        layout.addWidget(self.categories_list)
        
        layout.addStretch()
    
    def create_stat_card(self, icon, value, label, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: {color};
                border-radius: 8px;
                padding: 15px;
                border: none;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("color: white; font-size: 24px;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 20px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: white; font-size: 12px;")
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(label_widget)
        
        return card
    
    def load_catalog(self):
        """تحميل فئات الكتالوج"""
        categories = [
            "📱 إلكترونيات - 345 منتج",
            "👕 ملابس - 289 منتج", 
            "🍎 أغذية - 567 منتج",
            "🏠 أدوات منزلية - 123 منتج",
            "📚 كتب - 234 منتج",
            "⚽ رياضة - 89 منتج"
        ]
        
        for category in categories:
            item = QListWidgetItem(category)
            self.categories_list.addItem(item)


class ProductsTableWidget(QTableWidget):
    """جدول المنتجات المحسن"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_table()
        self.load_products_data()
    
    def init_table(self):
        """إعداد الجدول"""
        columns = [
            "الكود", "اسم المنتج", "الفئة", "الوحدة", "سعر الشراء", 
            "سعر البيع", "المخزون", "الحد الأدنى", "الحالة", "الإجراءات"
        ]
        
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e8f5e8;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 12px;
            }
            QHeaderView::section {
                background: #28a745;
                color: white;
                padding: 12px 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #28a745;
                color: white;
            }
        """)
        
        # إعدادات الجدول
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        self.verticalHeader().setVisible(False)
        
        # تحديد عرض الأعمدة
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)   # الكود  
        header.setSectionResizeMode(1, QHeaderView.Stretch) # اسم المنتج
        header.setSectionResizeMode(2, QHeaderView.Fixed)   # الفئة
        header.setSectionResizeMode(3, QHeaderView.Fixed)   # الوحدة
        header.setSectionResizeMode(4, QHeaderView.Fixed)   # سعر الشراء
        header.setSectionResizeMode(5, QHeaderView.Fixed)   # سعر البيع
        header.setSectionResizeMode(6, QHeaderView.Fixed)   # المخزون
        header.setSectionResizeMode(7, QHeaderView.Fixed)   # الحد الأدنى
        header.setSectionResizeMode(8, QHeaderView.Fixed)   # الحالة
        header.setSectionResizeMode(9, QHeaderView.Fixed)   # الإجراءات
        
        self.setColumnWidth(0, 80)   # الكود
        self.setColumnWidth(2, 100)  # الفئة
        self.setColumnWidth(3, 70)   # الوحدة
        self.setColumnWidth(4, 90)   # سعر الشراء
        self.setColumnWidth(5, 90)   # سعر البيع
        self.setColumnWidth(6, 80)   # المخزون
        self.setColumnWidth(7, 90)   # الحد الأدنى
        self.setColumnWidth(8, 80)   # الحالة
        self.setColumnWidth(9, 120)  # الإجراءات
    
    def load_products_data(self):
        """تحميل بيانات المنتجات"""
        products_data = [
            {"code": "PRD001", "name": "لابتوب ديل XPS", "category": "إلكترونيات", 
             "unit": "قطعة", "purchase_price": 12000, "selling_price": 15000,
             "stock": 25, "min_stock": 5, "status": "متاح"},
            {"code": "PRD002", "name": "هاتف ذكي سامسونج", "category": "إلكترونيات", 
             "unit": "قطعة", "purchase_price": 6000, "selling_price": 8500,
             "stock": 3, "min_stock": 5, "status": "منخفض"},
            {"code": "PRD003", "name": "قميص قطني", "category": "ملابس", 
             "unit": "قطعة", "purchase_price": 50, "selling_price": 80,
             "stock": 0, "min_stock": 10, "status": "نفد"},
            {"code": "PRD004", "name": "كتاب البرمجة", "category": "كتب", 
             "unit": "قطعة", "purchase_price": 80, "selling_price": 120,
             "stock": 45, "min_stock": 15, "status": "متاح"},
            {"code": "PRD005", "name": "مكواة كهربائية", "category": "أدوات منزلية", 
             "unit": "قطعة", "purchase_price": 150, "selling_price": 220,
             "stock": 8, "min_stock": 12, "status": "منخفض"},
        ]
        
        self.setRowCount(len(products_data))
        
        for row, product in enumerate(products_data):
            # الكود
            self.setItem(row, 0, QTableWidgetItem(product["code"]))
            
            # اسم المنتج
            self.setItem(row, 1, QTableWidgetItem(product["name"]))
            
            # الفئة
            category_item = QTableWidgetItem(product["category"])
            category_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 2, category_item)
            
            # الوحدة
            unit_item = QTableWidgetItem(product["unit"])
            unit_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 3, unit_item)
            
            # سعر الشراء
            purchase_item = QTableWidgetItem(f"{product['purchase_price']:,.0f}")
            purchase_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 4, purchase_item)
            
            # سعر البيع
            selling_item = QTableWidgetItem(f"{product['selling_price']:,.0f}")
            selling_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 5, selling_item)
            
            # المخزون
            stock_item = QTableWidgetItem(str(product["stock"]))
            stock_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 6, stock_item)
            
            # الحد الأدنى
            min_item = QTableWidgetItem(str(product["min_stock"]))
            min_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 7, min_item)
            
            # الحالة
            status_colors = {
                "متاح": {"bg": "#d4edda", "fg": "#155724"},
                "منخفض": {"bg": "#fff3cd", "fg": "#856404"},
                "نفد": {"bg": "#f8d7da", "fg": "#721c24"}
            }
            
            status_item = QTableWidgetItem(product["status"])
            status_item.setTextAlignment(Qt.AlignCenter)
            color = status_colors.get(product["status"], status_colors["متاح"])
            status_item.setBackground(QColor(color["bg"]))
            status_item.setForeground(QColor(color["fg"]))
            self.setItem(row, 8, status_item)
            
            # الإجراءات
            actions_widget = self.create_actions_widget(row, product)
            self.setCellWidget(row, 9, actions_widget)
    
    def create_actions_widget(self, row, product_data):
        """إنشاء أزرار الإجراءات"""
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(5, 2, 5, 2)
        actions_layout.setSpacing(3)
        
        # زر التعديل
        edit_btn = QPushButton("✏️")
        edit_btn.setFixedSize(22, 22)
        edit_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        edit_btn.setToolTip("تعديل المنتج")
        edit_btn.clicked.connect(lambda: self.edit_product(row, product_data))
        
        # زر عرض التفاصيل
        view_btn = QPushButton("👁️")
        view_btn.setFixedSize(22, 22)
        view_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        view_btn.setToolTip("عرض التفاصيل")
        view_btn.clicked.connect(lambda: self.view_product_details(row, product_data))
        
        # زر الحذف
        delete_btn = QPushButton("🗑️")
        delete_btn.setFixedSize(22, 22)
        delete_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 11px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        delete_btn.setToolTip("حذف المنتج")
        delete_btn.clicked.connect(lambda: self.delete_product(row, product_data))
        
        actions_layout.addWidget(edit_btn)
        actions_layout.addWidget(view_btn)
        actions_layout.addWidget(delete_btn)
        actions_layout.addStretch()
        
        return actions_widget
    
    def edit_product(self, row, product_data):
        """تعديل المنتج"""
        dialog = ProductFormDialog(product_data, self)
        if dialog.exec() == QDialog.Accepted:
            self.load_products_data()
    
    def view_product_details(self, row, product_data):
        """عرض تفاصيل المنتج"""
        details_text = f"""
        🛍️ تفاصيل المنتج:
        
        🏷️ الكود: {product_data['code']}
        📛 الاسم: {product_data['name']}
        📂 الفئة: {product_data['category']}
        📏 الوحدة: {product_data['unit']}
        💰 سعر الشراء: {product_data['purchase_price']:,.0f} ج.م
        💵 سعر البيع: {product_data['selling_price']:,.0f} ج.م
        📦 المخزون: {product_data['stock']} {product_data['unit']}
        ⚠️ الحد الأدنى: {product_data['min_stock']} {product_data['unit']}
        ✅ الحالة: {product_data['status']}
        """
        
        QMessageBox.information(self, "تفاصيل المنتج", details_text)
    
    def delete_product(self, row, product_data):
        """حذف المنتج"""
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل تريد حذف المنتج '{product_data['name']}'؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الحذف", "تم حذف المنتج بنجاح!")
            self.load_products_data()


class EnhancedProductsWindow(QWidget):
    """نافذة المنتجات المحسنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translator = Translator()
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # الرأس
        header = self.create_header()
        main_layout.addWidget(header)
        
        # المحتوى الرئيسي
        content_splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - الكتالوج
        left_panel = self.create_left_panel()
        left_panel.setMaximumWidth(300)
        content_splitter.addWidget(left_panel)
        
        # الجانب الأيمن - جدول المنتجات
        right_panel = self.create_right_panel()
        content_splitter.addWidget(right_panel)
        
        content_splitter.setSizes([300, 900])
        main_layout.addWidget(content_splitter)
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
    
    def create_header(self):
        """إنشاء الرأس"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #28a745, stop:0.5 #20c997, stop:1 #28a745);
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(header_frame)
        
        # معلومات الصفحة
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        
        title = QLabel("🛍️ إدارة المنتجات")
        title.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
        """)
        
        subtitle = QLabel("كتالوج تفاعلي وإدارة شاملة للمنتجات والمخزون")
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin-top: 5px;
        """)
        
        info_layout.addWidget(title)
        info_layout.addWidget(subtitle)
        
        # الإحصائيات السريعة
        stats_widget = self.create_products_stats()
        
        layout.addWidget(info_widget, 7)
        layout.addWidget(stats_widget, 3)
        
        return header_frame
    
    def create_products_stats(self):
        """إنشاء إحصائيات المنتجات"""
        stats_widget = QWidget()
        stats_layout = QHBoxLayout(stats_widget)
        stats_layout.setSpacing(10)
        
        # إجمالي المنتجات
        total_stat = self.create_stat_card("📦", "1,847", "إجمالي المنتجات")
        stats_layout.addWidget(total_stat)
        
        # نفدت من المخزون
        out_of_stock_stat = self.create_stat_card("⚠️", "8", "نفدت من المخزون")
        stats_layout.addWidget(out_of_stock_stat)
        
        # الأكثر مبيعاً
        best_selling_stat = self.create_stat_card("🔥", "156", "الأكثر مبيعاً")
        stats_layout.addWidget(best_selling_stat)
        
        return stats_widget
    
    def create_stat_card(self, icon, value, label):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("color: white; font-size: 20px;")
        icon_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        value_label.setAlignment(Qt.AlignCenter)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 10px;")
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(value_label)
        layout.addWidget(label_widget)
        
        return card
    
    def create_left_panel(self):
        """إنشاء اللوحة اليسرى"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(15)
        
        # الكتالوج
        self.catalog_widget = ProductCatalogWidget()
        left_layout.addWidget(self.catalog_widget)
        
        return left_widget
    
    def create_right_panel(self):
        """إنشاء اللوحة اليمنى"""
        right_frame = QFrame()
        right_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        
        layout = QVBoxLayout(right_frame)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # الجدول
        self.products_table = ProductsTableWidget()
        layout.addWidget(self.products_table)
        
        return right_frame
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        
        # زر إضافة منتج جديد
        add_btn = QPushButton("➕ إضافة منتج")
        add_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        add_btn.clicked.connect(self.add_new_product)
        
        # زر استيراد
        import_btn = QPushButton("📥 استيراد")
        import_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #0056b3;
            }
        """)
        import_btn.clicked.connect(self.import_products)
        
        # زر تصدير
        export_btn = QPushButton("📤 تصدير")
        export_btn.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        export_btn.clicked.connect(self.export_products)
        
        layout.addWidget(add_btn)
        layout.addWidget(import_btn)
        layout.addWidget(export_btn)
        layout.addStretch()
        
        # البحث والفلتر
        filter_combo = QComboBox()
        filter_combo.addItems(["جميع المنتجات", "إلكترونيات", "ملابس", "أغذية", "أدوات منزلية", "كتب"])
        filter_combo.setStyleSheet("""
            QComboBox {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
                font-size: 12px;
            }
        """)
        
        search_input = QLineEdit()
        search_input.setPlaceholderText("البحث في المنتجات...")
        search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #28a745;
                border-radius: 20px;
                font-size: 12px;
                background: white;
            }
            QLineEdit:focus {
                border-color: #218838;
            }
        """)
        search_input.textChanged.connect(self.search_products)
        
        layout.addWidget(QLabel("الفلتر:"))
        layout.addWidget(filter_combo)
        layout.addWidget(search_input)
        
        return toolbar_frame
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFixedHeight(40)
        status_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 8px 15px;
            }
        """)
        
        layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز - كتالوج المنتجات محدث")
        self.status_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 12px;")
        
        self.products_count_label = QLabel("5 منتجات")
        self.products_count_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        
        self.total_value_label = QLabel("قيمة المخزون: 545,820 ج.م")
        self.total_value_label.setStyleSheet("color: #007bff; font-weight: bold; font-size: 12px;")
        
        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.products_count_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.total_value_label)
        
        return status_frame
    
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_data)
        self.update_timer.start(60000)  # تحديث كل دقيقة
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.catalog_widget.load_catalog()
        self.status_label.setText(f"تم التحديث - {datetime.now().strftime('%H:%M')}")
    
    def add_new_product(self):
        """إضافة منتج جديد"""
        dialog = ProductFormDialog(parent=self)
        if dialog.exec() == QDialog.Accepted:
            self.products_table.load_products_data()
            self.status_label.setText("تم إضافة منتج جديد")
    
    def import_products(self):
        """استيراد المنتجات"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "استيراد المنتجات", "",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;All Files (*)"
        )
        
        if file_path:
            QMessageBox.information(self, "استيراد", f"تم استيراد المنتجات من:\n{file_path}")
    
    def export_products(self):
        """تصدير المنتجات"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير المنتجات", "products.xlsx",
            "Excel Files (*.xlsx);;CSV Files (*.csv);;PDF Files (*.pdf)"
        )
        
        if file_path:
            QMessageBox.information(self, "تصدير", f"تم تصدير المنتجات إلى:\n{file_path}")
    
    def search_products(self, search_text):
        """البحث في المنتجات"""
        if not search_text.strip():
            self.products_table.load_products_data()
            return
        
        # هنا يمكن تطبيق منطق البحث الفعلي
        self.status_label.setText(f"البحث عن: {search_text}")