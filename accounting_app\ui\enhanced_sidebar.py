"""
شريط جانبي محسن مع أيقونات وإشارات تفاعلية ودعم المظاهر
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                              QPushButton, QFrame, QScrollArea, QButtonGroup,
                              QGraphicsDropShadowEffect, QSizePolicy)
from PySide6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QTimer, pyqtSignal
from PySide6.QtGui import QColor, QCursor
from datetime import datetime
import random

from accounting_app.ui.styles import AppStyles


class AnimatedMenuButton(QPushButton):
    """زر قائمة متحرك مع تأثيرات بصرية"""
    
    def __init__(self, text, icon, callback, tooltip_text="", parent=None):
        super().__init__(parent)
        self.text = text
        self.icon = icon
        self.callback = callback
        self.tooltip_text = tooltip_text
        self.is_active = False
        self.hover_animation = None
        
        self.setup_button()
        self.setup_animations()
    
    def setup_button(self):
        """إعداد الزر"""
        self.setText(f"{self.icon}  {self.text}")
        self.setCheckable(True)
        self.setCursor(QCursor(Qt.PointingHandCursor))
        self.setToolTip(self.tooltip_text)
        
        # الحجم الثابت
        self.setFixedHeight(50)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        # الأنماط الأساسية
        self.update_style()
        
        # ربط الأحداث
        self.clicked.connect(self.on_clicked)
    
    def setup_animations(self):
        """إعداد الأنيمشن"""
        self.hover_animation = QPropertyAnimation(self, b"geometry")
        self.hover_animation.setDuration(200)
        self.hover_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def update_style(self, is_dark_mode=False):
        """تحديث الأنماط حسب المظهر"""
        if is_dark_mode:
            # مظهر داكن
            normal_bg = "#2c3e50"
            hover_bg = "#34495e"
            active_bg = "#3498db"
            text_color = "#ecf0f1"
            border_color = "#34495e"
        else:
            # مظهر فاتح
            normal_bg = "#ffffff"
            hover_bg = "#f8f9fa"
            active_bg = "#007bff"
            text_color = "#495057"
            border_color = "#dee2e6"
        
        if self.is_active:
            bg_color = active_bg
            text_color = "#ffffff"
            border_left = "4px solid #ffc107"
        else:
            bg_color = normal_bg
            border_left = "4px solid transparent"
        
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                color: {text_color};
                border: 1px solid {border_color};
                border-left: {border_left};
                border-radius: 8px;
                padding: 12px 15px;
                text-align: left;
                font-size: 14px;
                font-weight: 600;
                margin: 2px 0;
            }}
            QPushButton:hover {{
                background-color: {hover_bg};
                border-left: 4px solid #17a2b8;
                transform: translateX(5px);
            }}
            QPushButton:pressed {{
                background-color: {active_bg};
                color: white;
            }}
            QPushButton:checked {{
                background-color: {active_bg};
                color: white;
                border-left: 4px solid #ffc107;
                font-weight: bold;
            }}
        """)
    
    def set_active(self, active):
        """تعيين حالة النشاط"""
        self.is_active = active
        self.setChecked(active)
        self.update_style()
        
        if active:
            # تأثير بصري عند التفعيل
            effect = QGraphicsDropShadowEffect()
            effect.setBlurRadius(10)
            effect.setColor(QColor("#007bff"))
            effect.setOffset(2, 2)
            self.setGraphicsEffect(effect)
        else:
            self.setGraphicsEffect(None)
    
    def on_clicked(self):
        """معالج النقر"""
        if self.callback:
            self.callback()
    
    def enterEvent(self, event):
        """دخول الماوس"""
        # تأثير الظل عند التمرير
        if not self.is_active:
            effect = QGraphicsDropShadowEffect()
            effect.setBlurRadius(8)
            effect.setColor(QColor("#17a2b8"))
            effect.setOffset(1, 1)
            self.setGraphicsEffect(effect)
        
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """خروج الماوس"""
        if not self.is_active:
            self.setGraphicsEffect(None)
        super().leaveEvent(event)


class NotificationBadge(QLabel):
    """شارة الإشعارات"""
    
    def __init__(self, count=0, parent=None):
        super().__init__(parent)
        self.count = count
        self.setup_badge()
    
    def setup_badge(self):
        """إعداد الشارة"""
        if self.count > 0:
            text = str(self.count) if self.count < 100 else "99+"
            self.setText(text)
            self.setStyleSheet("""
                QLabel {
                    background-color: #dc3545;
                    color: white;
                    border-radius: 10px;
                    font-size: 10px;
                    font-weight: bold;
                    padding: 2px 6px;
                    min-width: 16px;
                    max-height: 20px;
                }
            """)
            self.setAlignment(Qt.AlignCenter)
            self.setVisible(True)
        else:
            self.setVisible(False)
    
    def update_count(self, count):
        """تحديث العدد"""
        self.count = count
        self.setup_badge()


class UserInfoWidget(QWidget):
    """واجهة معلومات المستخدم"""
    
    def __init__(self, user_data, parent=None):
        super().__init__(parent)
        self.user_data = user_data
        self.is_dark_mode = False
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # صورة المستخدم (placeholder)
        avatar_frame = QFrame()
        avatar_frame.setFixedSize(80, 80)
        avatar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 40px;
                border: 3px solid rgba(255, 255, 255, 0.3);
            }
        """)
        
        avatar_layout = QVBoxLayout(avatar_frame)
        avatar_layout.setAlignment(Qt.AlignCenter)
        
        avatar_icon = QLabel("👤")
        avatar_icon.setStyleSheet("""
            color: white;
            font-size: 32px;
            background: transparent;
            border: none;
        """)
        avatar_icon.setAlignment(Qt.AlignCenter)
        avatar_layout.addWidget(avatar_icon)
        
        # معلومات المستخدم
        user_info = QWidget()
        user_info_layout = QVBoxLayout(user_info)
        user_info_layout.setContentsMargins(0, 0, 0, 0)
        user_info_layout.setSpacing(5)
        
        # اسم المستخدم
        username = self.user_data[3] if len(self.user_data) > 3 and self.user_data[3] else "المستخدم"
        username_label = QLabel(username)
        username_label.setStyleSheet("""
            color: #495057;
            font-size: 16px;
            font-weight: bold;
        """)
        username_label.setAlignment(Qt.AlignCenter)
        
        # نوع المستخدم
        user_type = self.user_data[4] if len(self.user_data) > 4 and self.user_data[4] else "مدير"
        role_label = QLabel(f"🔑 {user_type}")
        role_label.setStyleSheet("""
            color: #6c757d;
            font-size: 12px;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 12px;
        """)
        role_label.setAlignment(Qt.AlignCenter)
        
        # آخر دخول
        last_login = QLabel(f"آخر دخول: {datetime.now().strftime('%H:%M')}")
        last_login.setStyleSheet("""
            color: #6c757d;
            font-size: 10px;
        """)
        last_login.setAlignment(Qt.AlignCenter)
        
        user_info_layout.addWidget(username_label)
        user_info_layout.addWidget(role_label)
        user_info_layout.addWidget(last_login)
        
        # إضافة العناصر للتخطيط الرئيسي
        layout.addWidget(avatar_frame, 0, Qt.AlignCenter)
        layout.addWidget(user_info)
        
        # إضافة خط فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet("color: #dee2e6;")
        layout.addWidget(separator)
    
    def update_theme(self, is_dark_mode):
        """تحديث المظهر"""
        self.is_dark_mode = is_dark_mode
        # يمكن إضافة تحديث الألوان هنا


class EnhancedSidebar(QWidget):
    """الشريط الجانبي المحسن"""
    
    # إشارات
    menuItemClicked = pyqtSignal(str)  # إشارة عند النقر على عنصر القائمة
    themeChanged = pyqtSignal(bool)    # إشارة تغيير المظهر
    
    def __init__(self, user_data, parent=None):
        super().__init__(parent)
        self.user_data = user_data
        self.is_dark_mode = False
        self.current_page = "dashboard"
        self.menu_buttons = []
        self.notifications = {
            "المخزون": 5,
            "الفواتير": 12,
            "التقارير": 0
        }
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setFixedWidth(280)
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-right: 1px solid #dee2e6;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # رأس الشريط الجانبي
        header = self.create_header()
        main_layout.addWidget(header)
        
        # معلومات المستخدم
        user_info = UserInfoWidget(self.user_data)
        main_layout.addWidget(user_info)
        
        # منطقة التمرير للقائمة
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: #f8f9fa;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: #ced4da;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #adb5bd;
            }
        """)
        
        # قائمة الأزرار
        menu_widget = self.create_menu()
        scroll_area.setWidget(menu_widget)
        main_layout.addWidget(scroll_area)
        
        # تذييل الشريط الجانبي
        footer = self.create_footer()
        main_layout.addWidget(footer)
    
    def create_header(self):
        """إنشاء رأس الشريط الجانبي المحسن"""
        header_frame = QFrame()
        header_frame.setFixedHeight(90)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.3 #764ba2, stop:0.7 #f093fb, stop:1 #f5576c);
                border: none;
                padding: 15px;
            }
        """)

        layout = QHBoxLayout(header_frame)
        layout.setContentsMargins(15, 10, 15, 10)

        # شعار التطبيق المحسن
        logo_widget = QWidget()
        logo_layout = QHBoxLayout(logo_widget)
        logo_layout.setContentsMargins(0, 0, 0, 0)
        logo_layout.setSpacing(12)

        # أيقونة التطبيق مع تأثيرات
        app_icon = QLabel("💼")
        app_icon.setStyleSheet("""
            font-size: 36px;
            color: white;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(255, 255, 255, 0.25), stop:1 rgba(255, 255, 255, 0.15));
            border-radius: 20px;
            padding: 10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
        """)
        app_icon.setFixedSize(56, 56)
        app_icon.setAlignment(Qt.AlignCenter)

        # معلومات التطبيق المحسنة
        app_info = QWidget()
        app_info_layout = QVBoxLayout(app_info)
        app_info_layout.setContentsMargins(0, 0, 0, 0)
        app_info_layout.setSpacing(3)

        app_name = QLabel("نظام المحاسبة المتقدم")
        app_name.setStyleSheet("""
            color: white;
            font-size: 17px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        """)

        app_version = QLabel("الإصدار 2.1 Pro")
        app_version.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 11px;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.15);
            padding: 2px 8px;
            border-radius: 10px;
        """)

        # إضافة حالة الاتصال
        connection_status = QLabel("🟢 متصل")
        connection_status.setStyleSheet("""
            color: rgba(255, 255, 255, 0.8);
            font-size: 10px;
            font-weight: 500;
        """)

        app_info_layout.addWidget(app_name)
        app_info_layout.addWidget(app_version)
        app_info_layout.addWidget(connection_status)

        logo_layout.addWidget(app_icon)
        logo_layout.addWidget(app_info)

        # زر طي/فتح محسن
        toggle_btn = QPushButton("☰")
        toggle_btn.setFixedSize(36, 36)
        toggle_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.25), stop:1 rgba(255, 255, 255, 0.15));
                color: white;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 18px;
                font-size: 18px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.35), stop:1 rgba(255, 255, 255, 0.25));
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background: rgba(255, 255, 255, 0.4);
            }
        """)
        toggle_btn.setToolTip("طي/فتح الشريط الجانبي")
        toggle_btn.clicked.connect(self.toggle_sidebar)

        layout.addWidget(logo_widget)
        layout.addStretch()
        layout.addWidget(toggle_btn)

        return header_frame
    
    def create_menu(self):
        """إنشاء القائمة الرئيسية"""
        menu_widget = QWidget()
        layout = QVBoxLayout(menu_widget)
        layout.setContentsMargins(10, 15, 10, 15)
        layout.setSpacing(5)
        
        # عناصر القائمة مع الأيقونات والإشعارات
        menu_items = [
            {
                "name": "dashboard",
                "text": "لوحة التحكم",
                "icon": "🏠",
                "tooltip": "الصفحة الرئيسية - نظرة عامة على النظام",
                "notifications": 0
            },
            {
                "name": "sales",
                "text": "المبيعات",
                "icon": "🛒",
                "tooltip": "إدارة المبيعات والفواتير",
                "notifications": 3
            },
            {
                "name": "purchases",
                "text": "المشتريات",
                "icon": "📦",
                "tooltip": "إدارة المشتريات والموردين",
                "notifications": 0
            },
            {
                "name": "inventory",
                "text": "المخزون",
                "icon": "📋",
                "tooltip": "مراقبة المخزون والمنتجات",
                "notifications": self.notifications.get("المخزون", 0)
            },
            {
                "name": "customers",
                "text": "العملاء",
                "icon": "👥",
                "tooltip": "إدارة بيانات العملاء",
                "notifications": 0
            },
            {
                "name": "suppliers",
                "text": "الموردين",
                "icon": "🏢",
                "tooltip": "إدارة بيانات الموردين",
                "notifications": 0
            },
            {
                "name": "reports",
                "text": "التقارير",
                "icon": "📊",
                "tooltip": "التقارير والإحصائيات التفصيلية",
                "notifications": self.notifications.get("التقارير", 0)
            },
            {
                "name": "finances",
                "text": "الشؤون المالية",
                "icon": "💰",
                "tooltip": "إدارة الحسابات والأموال",
                "notifications": self.notifications.get("الفواتير", 0)
            },
            {
                "name": "settings",
                "text": "الإعدادات",
                "icon": "⚙️",
                "tooltip": "إعدادات النظام والتخصيص",
                "notifications": 0
            }
        ]
        
        # مجموعة الأزرار للتأكد من تحديد واحد فقط
        self.button_group = QButtonGroup()
        
        for i, item in enumerate(menu_items):
            # إنشاء حاوية للزر مع الإشعار
            button_container = QWidget()
            button_layout = QHBoxLayout(button_container)
            button_layout.setContentsMargins(0, 0, 0, 0)
            button_layout.setSpacing(0)
            
            # إنشاء الزر
            button = AnimatedMenuButton(
                text=item["text"],
                icon=item["icon"],
                callback=lambda name=item["name"]: self.on_menu_clicked(name),
                tooltip_text=item["tooltip"]
            )
            
            # تعيين النشاط للزر الأول
            if i == 0:
                button.set_active(True)
            
            self.button_group.addButton(button, i)
            self.menu_buttons.append(button)
            
            button_layout.addWidget(button)
            
            # إضافة شارة الإشعار إذا كانت موجودة
            if item["notifications"] > 0:
                badge = NotificationBadge(item["notifications"])
                badge.setParent(button_container)
                badge.move(220, 15)  # موضع ثابت للشارة
            
            layout.addWidget(button_container)
        
        layout.addStretch()
        return menu_widget
    
    def create_footer(self):
        """إنشاء تذييل الشريط الجانبي المحسن"""
        footer_frame = QFrame()
        footer_frame.setFixedHeight(140)
        footer_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-top: 2px solid #dee2e6;
                padding: 15px;
            }
        """)

        layout = QVBoxLayout(footer_frame)
        layout.setSpacing(12)

        # إحصائيات سريعة محسنة
        stats_widget = QWidget()
        stats_layout = QHBoxLayout(stats_widget)
        stats_layout.setSpacing(8)

        # إحصائية المبيعات اليوم
        today_sales = self.create_enhanced_quick_stat("📈", "3,250 ج.م", "مبيعات اليوم", "#28a745")
        stats_layout.addWidget(today_sales)

        # إحصائية الطلبات
        today_orders = self.create_enhanced_quick_stat("🛒", "23", "طلبات جديدة", "#007bff")
        stats_layout.addWidget(today_orders)

        layout.addWidget(stats_widget)

        # أزرار التحكم السريع المحسنة
        controls_widget = QWidget()
        controls_layout = QHBoxLayout(controls_widget)
        controls_layout.setSpacing(6)

        # زر المظهر
        theme_btn = QPushButton("🌙" if not self.is_dark_mode else "☀️")
        theme_btn.setFixedSize(38, 38)
        theme_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #495057, stop:1 #343a40);
                color: white;
                border: 2px solid rgba(255, 255, 255, 0.1);
                border-radius: 19px;
                font-size: 16px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #343a40, stop:1 #23272b);
                transform: scale(1.05);
            }
        """)
        theme_btn.setToolTip("تبديل المظهر الفاتح/الداكن")
        theme_btn.clicked.connect(self.toggle_theme)

        # زر الإشعارات مع شارة
        notifications_container = QWidget()
        notifications_container.setFixedSize(38, 38)
        notifications_btn = QPushButton("🔔")
        notifications_btn.setParent(notifications_container)
        notifications_btn.setFixedSize(38, 38)
        notifications_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #17a2b8, stop:1 #138496);
                color: white;
                border: 2px solid rgba(255, 255, 255, 0.1);
                border-radius: 19px;
                font-size: 16px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #138496, stop:1 #0f6674);
                transform: scale(1.05);
            }
        """)
        notifications_btn.setToolTip("الإشعارات والتنبيهات")
        notifications_btn.clicked.connect(self.show_notifications)

        # شارة الإشعارات
        if sum(self.notifications.values()) > 0:
            badge = QLabel(str(sum(self.notifications.values())))
            badge.setParent(notifications_container)
            badge.setFixedSize(16, 16)
            badge.move(26, 2)
            badge.setStyleSheet("""
                background: #dc3545;
                color: white;
                border-radius: 8px;
                font-size: 10px;
                font-weight: bold;
                text-align: center;
            """)
            badge.setAlignment(Qt.AlignCenter)

        # زر المساعدة
        help_btn = QPushButton("❓")
        help_btn.setFixedSize(38, 38)
        help_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #28a745, stop:1 #218838);
                color: white;
                border: 2px solid rgba(255, 255, 255, 0.1);
                border-radius: 19px;
                font-size: 16px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #218838, stop:1 #1e7e34);
                transform: scale(1.05);
            }
        """)
        help_btn.setToolTip("المساعدة والدعم")
        help_btn.clicked.connect(self.show_help)

        # زر تسجيل الخروج
        logout_btn = QPushButton("🚪")
        logout_btn.setFixedSize(38, 38)
        logout_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #dc3545, stop:1 #c82333);
                color: white;
                border: 2px solid rgba(255, 255, 255, 0.1);
                border-radius: 19px;
                font-size: 16px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #c82333, stop:1 #bd2130);
                transform: scale(1.05);
            }
        """)
        logout_btn.setToolTip("تسجيل الخروج")
        logout_btn.clicked.connect(self.logout)

        controls_layout.addWidget(theme_btn)
        controls_layout.addWidget(notifications_container)
        controls_layout.addWidget(help_btn)
        controls_layout.addStretch()
        controls_layout.addWidget(logout_btn)

        layout.addWidget(controls_widget)

        # معلومات النظام
        system_info = QLabel(f"🕒 {datetime.now().strftime('%H:%M')} • 💾 {random.randint(60, 95)}% ذاكرة")
        system_info.setStyleSheet("""
            color: #6c757d;
            font-size: 10px;
            font-weight: 500;
            padding: 5px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 4px;
            text-align: center;
        """)
        system_info.setAlignment(Qt.AlignCenter)
        layout.addWidget(system_info)

        return footer_frame

    def create_enhanced_quick_stat(self, icon, value, label, color):
        """إنشاء إحصائية سريعة محسنة"""
        stat_widget = QWidget()
        stat_layout = QVBoxLayout(stat_widget)
        stat_layout.setContentsMargins(8, 6, 8, 6)
        stat_layout.setSpacing(2)

        # الأيقونة والقيمة
        top_row = QWidget()
        top_layout = QHBoxLayout(top_row)
        top_layout.setContentsMargins(0, 0, 0, 0)
        top_layout.setSpacing(4)

        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            font-size: 14px;
            color: {color};
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 2px;
            min-width: 16px;
            max-width: 16px;
        """)
        icon_label.setAlignment(Qt.AlignCenter)

        value_label = QLabel(value)
        value_label.setStyleSheet("""
            color: #495057;
            font-size: 11px;
            font-weight: bold;
        """)

        top_layout.addWidget(icon_label)
        top_layout.addWidget(value_label)

        # التسمية
        label_widget = QLabel(label)
        label_widget.setStyleSheet("""
            color: #6c757d;
            font-size: 9px;
            font-weight: 500;
        """)
        label_widget.setWordWrap(True)

        stat_layout.addWidget(top_row)
        stat_layout.addWidget(label_widget)

        # تأثير بصري محسن
        stat_widget.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9), stop:1 rgba(248, 249, 250, 0.9));
                border: 1px solid {color};
                border-radius: 8px;
                padding: 4px;
            }}
            QWidget:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0), stop:1 rgba(248, 249, 250, 1.0));
                border-color: {self.darken_color_simple(color)};
            }}
        """)

        return stat_widget

    def darken_color_simple(self, color, factor=0.2):
        """تظليل اللون بطريقة بسيطة"""
        color_map = {
            "#28a745": "#1e7e34",
            "#007bff": "#0056b3",
            "#dc3545": "#c82333",
            "#ffc107": "#e0a800"
        }
        return color_map.get(color, "#333333")

    def create_quick_stat(self, icon, value, label):
        """إنشاء إحصائية سريعة"""
        stat_widget = QWidget()
        stat_layout = QVBoxLayout(stat_widget)
        stat_layout.setContentsMargins(8, 5, 8, 5)
        stat_layout.setSpacing(2)
        
        # الأيقونة والقيمة
        top_row = QWidget()
        top_layout = QHBoxLayout(top_row)
        top_layout.setContentsMargins(0, 0, 0, 0)
        top_layout.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 14px;")
        
        value_label = QLabel(value)
        value_label.setStyleSheet("""
            color: #495057;
            font-size: 12px;
            font-weight: bold;
        """)
        
        top_layout.addWidget(icon_label)
        top_layout.addWidget(value_label)
        
        # التسمية
        label_widget = QLabel(label)
        label_widget.setStyleSheet("""
            color: #6c757d;
            font-size: 9px;
        """)
        label_widget.setWordWrap(True)
        
        stat_layout.addWidget(top_row)
        stat_layout.addWidget(label_widget)
        
        # تأثير بصري
        stat_widget.setStyleSheet("""
            QWidget {
                background: rgba(255, 255, 255, 0.7);
                border-radius: 6px;
                padding: 2px;
            }
            QWidget:hover {
                background: rgba(255, 255, 255, 0.9);
            }
        """)
        
        return stat_widget
    
    def setup_timer(self):
        """إعداد مؤقت لتحديث الإحصائيات"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_notifications)
        self.update_timer.start(30000)  # تحديث كل 30 ثانية
    
    def on_menu_clicked(self, page_name):
        """معالج النقر على عنصر القائمة"""
        # تحديث الحالة النشطة
        for i, button in enumerate(self.menu_buttons):
            button.set_active(False)
        
        # تفعيل الزر المنقور عليه
        for i, button in enumerate(self.menu_buttons):
            if button.callback.__name__ == "on_menu_clicked":
                # هذا حل مؤقت - يمكن تحسينه
                pass
        
        # العثور على الزر المناسب وتفعيله
        current_button = None
        for button in self.menu_buttons:
            if page_name in button.text.lower():
                button.set_active(True)
                current_button = button
                break
        
        self.current_page = page_name
        
        # إرسال الإشارة
        self.menuItemClicked.emit(page_name)
    
    def set_active_page(self, page_name):
        """تعيين الصفحة النشطة من الخارج"""
        for button in self.menu_buttons:
            button.set_active(False)
        
        # تفعيل الزر المناسب
        page_mapping = {
            "dashboard": "لوحة التحكم",
            "sales": "المبيعات",
            "purchases": "المشتريات",
            "inventory": "المخزون",
            "customers": "العملاء",
            "suppliers": "الموردين",
            "reports": "التقارير",
            "finances": "الشؤون المالية",
            "settings": "الإعدادات"
        }
        
        target_text = page_mapping.get(page_name, "")
        for button in self.menu_buttons:
            if target_text in button.text:
                button.set_active(True)
                break
        
        self.current_page = page_name
    
    def toggle_theme(self):
        """تبديل المظهر"""
        self.is_dark_mode = not self.is_dark_mode
        
        # تحديث أنماط الأزرار
        for button in self.menu_buttons:
            button.update_style(self.is_dark_mode)
        
        # تحديث الأنماط العامة
        if self.is_dark_mode:
            self.setStyleSheet("""
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2c3e50, stop:1 #34495e);
                    border-right: 1px solid #34495e;
                }
            """)
        else:
            self.setStyleSheet("""
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #f8f9fa, stop:1 #e9ecef);
                    border-right: 1px solid #dee2e6;
                }
            """)
        
        # تحديث أيقونة زر المظهر
        theme_buttons = self.findChildren(QPushButton)
        for btn in theme_buttons:
            if btn.toolTip() == "تبديل المظهر الفاتح/الداكن":
                btn.setText("☀️" if self.is_dark_mode else "🌙")
                break
        
        # إرسال إشارة تغيير المظهر
        self.themeChanged.emit(self.is_dark_mode)
    
    def toggle_sidebar(self):
        """طي/فتح الشريط الجانبي"""
        current_width = self.width()
        target_width = 60 if current_width > 100 else 280
        
        # أنيمشن تغيير العرض
        self.width_animation = QPropertyAnimation(self, b"maximumWidth")
        self.width_animation.setDuration(300)
        self.width_animation.setStartValue(current_width)
        self.width_animation.setEndValue(target_width)
        self.width_animation.setEasingCurve(QEasingCurve.OutCubic)
        self.width_animation.start()
        
        # تحديث العرض الثابت أيضاً
        QTimer.singleShot(300, lambda: self.setFixedWidth(target_width))
    
    def update_notifications(self):
        """تحديث عدد الإشعارات"""
        # محاكاة تحديث الإشعارات
        import random
        
        # تحديث عشوائي للإشعارات
        if random.choice([True, False]):
            self.notifications["المخزون"] = random.randint(0, 10)
            self.notifications["الفواتير"] = random.randint(0, 20)
            
            # يمكن إضافة تحديث فعلي للشارات هنا
    
    def show_notifications(self):
        """عرض الإشعارات"""
        from PySide6.QtWidgets import QMessageBox
        
        notifications_text = "🔔 الإشعارات الحالية:\n\n"
        if self.notifications["المخزون"] > 0:
            notifications_text += f"📦 {self.notifications['المخزون']} منتج بحاجة إعادة تموين\n"
        if self.notifications["الفواتير"] > 0:
            notifications_text += f"🧾 {self.notifications['الفواتير']} فاتورة معلقة\n"
        
        if not any(self.notifications.values()):
            notifications_text += "✅ لا توجد إشعارات جديدة"
        
        QMessageBox.information(self, "الإشعارات", notifications_text)
    
    def show_help(self):
        """عرض المساعدة"""
        from PySide6.QtWidgets import QMessageBox
        
        help_text = """
        🔍 مساعدة سريعة:
        
        • استخدم القائمة الجانبية للتنقل بين الأقسام
        • اضغط على الأيقونات للحصول على معلومات إضافية
        • يمكنك تبديل المظهر باستخدام زر القمر/الشمس
        • الإشعارات تظهر عند وجود تحديثات مهمة
        
        للمزيد من المساعدة، راجع دليل المستخدم.
        """
        
        QMessageBox.information(self, "المساعدة", help_text)
    
    def logout(self):
        """تسجيل الخروج"""
        from PySide6.QtWidgets import QMessageBox
        
        reply = QMessageBox.question(
            self, "تسجيل الخروج",
            "هل تريد تسجيل الخروج من النظام؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # إرسال إشارة تسجيل الخروج
            self.menuItemClicked.emit("logout")