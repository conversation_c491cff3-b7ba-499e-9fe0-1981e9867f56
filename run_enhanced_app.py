#!/usr/bin/env python3
"""
تشغيل نظام المحاسبة المحسن
Enhanced Accounting System Launcher
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QSplashScreen, QMainWindow, QStackedWidget,
                              QMessageBox, QWidget, QHBoxLayout)
from PySide6.QtCore import Qt, QTimer, QThread, Signal
from PySide6.QtGui import QPixmap, QFont, QPainter, QColor, QBrush, QLinearGradient

# استيراد الواجهات المحسنة
from accounting_app.ui.enhanced_sidebar import EnhancedSidebar
from accounting_app.ui.enhanced_dashboard import EnhancedDashboard
from accounting_app.ui.enhanced_products_window import EnhancedProductsWindow
from accounting_app.ui.enhanced_reports_window import EnhancedReportsWindow
from accounting_app.ui.enhanced_settings_window import EnhancedSettingsWindow
from accounting_app.ui.login_window import LoginWindow
from accounting_app.dal.database import DatabaseManager


class SystemInitializer(QThread):
    """مهيئ النظام في خيط منفصل"""
    progressUpdate = Signal(int, str)
    initializationComplete = Signal(bool, str)
    
    def run(self):
        """تهيئة النظام"""
        try:
            steps = [
                ("تهيئة قاعدة البيانات...", 20),
                ("تحميل الإعدادات...", 40),
                ("فحص التحديثات...", 60),
                ("تحضير الواجهات...", 80),
                ("اكتمال التحميل...", 100)
            ]
            
            for message, progress in steps:
                self.progressUpdate.emit(progress, message)
                self.msleep(500)  # محاكاة وقت التحميل
            
            # تهيئة قاعدة البيانات فعلياً
            db = DatabaseManager()
            db.connect()
            db.create_tables()
            db.close()
            
            self.initializationComplete.emit(True, "تم تحميل النظام بنجاح!")
            
        except Exception as e:
            self.initializationComplete.emit(False, f"خطأ في تحميل النظام: {str(e)}")


class EnhancedSplashScreen(QSplashScreen):
    """شاشة البداية المحسنة"""
    
    def __init__(self):
        # إنشاء صورة مخصصة للشاشة
        pixmap = QPixmap(600, 400)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # خلفية متدرجة
        gradient = QLinearGradient(0, 0, 600, 400)
        gradient.setColorAt(0, QColor("#667eea"))
        gradient.setColorAt(0.5, QColor("#764ba2"))
        gradient.setColorAt(1, QColor("#f093fb"))
        
        painter.fillRect(pixmap.rect(), QBrush(gradient))
        
        # النص الرئيسي
        painter.setPen(QColor("white"))
        painter.setFont(QFont("Arial", 28, QFont.Bold))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "💼\nنظام المحاسبة المتقدم\nAdvanced Accounting System")
        
        painter.end()
        
        super().__init__(pixmap)
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        
        # إعداد مهيئ النظام
        self.initializer = SystemInitializer()
        self.initializer.progressUpdate.connect(self.update_progress)
        self.initializer.initializationComplete.connect(self.on_initialization_complete)
        
        # بدء التهيئة
        self.initializer.start()
    
    def update_progress(self, progress, message):
        """تحديث رسالة التقدم"""
        self.showMessage(
            f"{message}\n{progress}%",
            Qt.AlignBottom | Qt.AlignCenter,
            QColor("white")
        )
    
    def on_initialization_complete(self, success, message):
        """اكتمال التهيئة"""
        if success:
            QTimer.singleShot(1000, self.close)
        else:
            QMessageBox.critical(None, "خطأ في التحميل", message)
            sys.exit(1)


class EnhancedMainWindow(QMainWindow):
    """النافذة الرئيسية المحسنة"""
    
    def __init__(self, user_data):
        super().__init__()
        self.user_data = user_data
        self.current_page = "dashboard"
        self.init_ui()
    
    def init_ui(self):
        """إعداد الواجهة الرئيسية"""
        self.setWindowTitle("💼 نظام المحاسبة المتقدم - Advanced Accounting System")
        self.setMinimumSize(1400, 900)
        self.showMaximized()
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # الشريط الجانبي المحسن
        self.sidebar = EnhancedSidebar(self.user_data)
        self.sidebar.menuItemClicked.connect(self.on_menu_item_clicked)
        main_layout.addWidget(self.sidebar)
        
        # منطقة المحتوى
        self.content_stack = QStackedWidget()
        self.content_stack.setStyleSheet("""
            QStackedWidget {
                background: #f8f9fa;
                border: none;
            }
        """)
        main_layout.addWidget(self.content_stack)
        
        # إنشاء الصفحات
        self.create_pages()
        
        # عرض لوحة التحكم افتراضياً
        self.show_page("dashboard")
    
    def create_pages(self):
        """إنشاء جميع صفحات التطبيق"""
        # لوحة التحكم
        self.dashboard = EnhancedDashboard(self.user_data)
        self.content_stack.addWidget(self.dashboard)
        
        # صفحة المنتجات
        self.products_page = EnhancedProductsWindow()
        self.content_stack.addWidget(self.products_page)
        
        # صفحة التقارير
        self.reports_page = EnhancedReportsWindow()
        self.content_stack.addWidget(self.reports_page)
        
        # صفحة الإعدادات
        self.settings_page = EnhancedSettingsWindow()
        self.content_stack.addWidget(self.settings_page)
        
        # يمكن إضافة المزيد من الصفحات هنا
        
        # خريطة الصفحات
        self.pages_map = {
            "dashboard": self.dashboard,
            "inventory": self.products_page,
            "reports": self.reports_page,
            "settings": self.settings_page
        }
    
    def on_menu_item_clicked(self, page_name):
        """معالج النقر على عناصر القائمة"""
        if page_name == "logout":
            self.logout()
        else:
            self.show_page(page_name)
    
    def show_page(self, page_name):
        """عرض صفحة معينة"""
        if page_name in self.pages_map:
            page_widget = self.pages_map[page_name]
            self.content_stack.setCurrentWidget(page_widget)
            self.current_page = page_name
            
            # تحديث الشريط الجانبي
            self.sidebar.set_active_page(page_name)
        else:
            # صفحة غير مطورة بعد
            QMessageBox.information(
                self, "قريباً", 
                f"صفحة {page_name} قيد التطوير وستكون متاحة قريباً!"
            )
    
    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(
            self, "تسجيل الخروج",
            "هل تريد تسجيل الخروج من النظام؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.close()
            # إعادة فتح نافذة تسجيل الدخول
            login_window = LoginWindow()
            login_window.show()


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setApplicationName("نظام المحاسبة المتقدم")
    app.setApplicationVersion("2.1")
    app.setOrganizationName("شركة التطوير المتقدم")
    
    # تعيين خط افتراضي يدعم العربية
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    
    # شاشة البداية
    splash = EnhancedSplashScreen()
    splash.show()
    
    # معالجة الأحداث أثناء التحميل
    app.processEvents()
    
    # انتظار اكتمال التحميل
    while splash.isVisible():
        app.processEvents()
    
    # نافذة تسجيل الدخول
    login_window = LoginWindow()
    
    def on_login_success(user_data):
        """عند نجاح تسجيل الدخول"""
        login_window.close()
        
        # فتح النافذة الرئيسية
        main_window = EnhancedMainWindow(user_data)
        main_window.show()
    
    login_window.loginSuccessful.connect(on_login_success)
    login_window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
