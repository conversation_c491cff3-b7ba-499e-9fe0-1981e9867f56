"""
مدير المستخدمين - يتعامل مع عمليات المستخدمين مثل المصادقة وإدارة الحسابات
"""
from accounting_app.dal.database import DatabaseManager


class UserManager:
    """مدير المستخدمين - يتعامل مع عمليات المستخدمين"""
    
    def __init__(self):
        """تهيئة مدير المستخدمين"""
        self.db = DatabaseManager()
    
    def authenticate(self, username, password):
        """
        التحقق من صحة بيانات المستخدم
        :param username: اسم المستخدم
        :param password: كلمة المرور
        :return: بيانات المستخدم إذا كانت المصادقة ناجحة، وإلا None
        """
        self.db.connect()
        query = "SELECT * FROM users WHERE username = ? AND password = ?"
        user = self.db.fetch_one(query, (username, password))
        self.db.close()
        return user
    
    def get_user_by_id(self, user_id):
        """
        الحصول على بيانات المستخدم بواسطة المعرف
        :param user_id: معرف المستخدم
        :return: بيانات المستخدم
        """
        self.db.connect()
        query = "SELECT * FROM users WHERE id = ?"
        user = self.db.fetch_one(query, (user_id,))
        self.db.close()
        return user
    
    def get_user_by_username(self, username):
        """
        الحصول على بيانات المستخدم بواسطة اسم المستخدم
        :param username: اسم المستخدم
        :return: بيانات المستخدم
        """
        self.db.connect()
        query = "SELECT * FROM users WHERE username = ?"
        user = self.db.fetch_one(query, (username,))
        self.db.close()
        return user
    
    def create_user(self, username, password, full_name, email, phone, role):
        """
        إنشاء مستخدم جديد
        :param username: اسم المستخدم
        :param password: كلمة المرور
        :param full_name: الاسم الكامل
        :param email: البريد الإلكتروني
        :param phone: رقم الهاتف
        :param role: دور المستخدم
        :return: معرف المستخدم الجديد
        """
        self.db.connect()
        query = """
        INSERT INTO users (username, password, full_name, email, phone, role)
        VALUES (?, ?, ?, ?, ?, ?)
        """
        cursor = self.db.execute_query(query, (username, password, full_name, email, phone, role))
        user_id = cursor.lastrowid if cursor else None
        self.db.close()
        return user_id
    
    def update_user(self, user_id, password=None, full_name=None, email=None, phone=None, role=None):
        """
        تحديث بيانات المستخدم
        :param user_id: معرف المستخدم
        :param password: كلمة المرور الجديدة (اختياري)
        :param full_name: الاسم الكامل الجديد (اختياري)
        :param email: البريد الإلكتروني الجديد (اختياري)
        :param phone: رقم الهاتف الجديد (اختياري)
        :param role: دور المستخدم الجديد (اختياري)
        :return: True إذا تم التحديث بنجاح، وإلا False
        """
        self.db.connect()
        
        # بناء استعلام التحديث ديناميكيًا
        update_fields = []
        params = []
        
        if password:
            update_fields.append("password = ?")
            params.append(password)
        
        if full_name:
            update_fields.append("full_name = ?")
            params.append(full_name)
        
        if email:
            update_fields.append("email = ?")
            params.append(email)
        
        if phone:
            update_fields.append("phone = ?")
            params.append(phone)
        
        if role:
            update_fields.append("role = ?")
            params.append(role)
        
        if not update_fields:
            self.db.close()
            return False
        
        query = f"UPDATE users SET {', '.join(update_fields)} WHERE id = ?"
        params.append(user_id)
        
        cursor = self.db.execute_query(query, tuple(params))
        success = cursor is not None and cursor.rowcount > 0
        self.db.close()
        return success
    
    def delete_user(self, user_id):
        """
        حذف مستخدم
        :param user_id: معرف المستخدم
        :return: True إذا تم الحذف بنجاح، وإلا False
        """
        self.db.connect()
        query = "DELETE FROM users WHERE id = ?"
        cursor = self.db.execute_query(query, (user_id,))
        success = cursor is not None and cursor.rowcount > 0
        self.db.close()
        return success
    
    def get_all_users(self):
        """
        الحصول على جميع المستخدمين
        :return: قائمة بجميع المستخدمين
        """
        self.db.connect()
        query = "SELECT * FROM users"
        users = self.db.fetch_all(query)
        self.db.close()
        return users