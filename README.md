# 💼 نظام المحاسبة المتقدم - Advanced Accounting System

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![PySide6](https://img.shields.io/badge/PySide6-6.5+-green.svg)](https://pypi.org/project/PySide6/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Status](https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg)]()

## 🌟 نظرة عامة

نظام محاسبة شامل ومتقدم مصمم خصيصاً للشركات الصغيرة والمتوسطة. يوفر النظام واجهة مستخدم حديثة وسهلة الاستخدام مع دعم كامل للغة العربية والإنجليزية.

## ✨ المميزات الرئيسية

### 🎨 تصميم عصري ومتطور
- **واجهة مستخدم حديثة**: تصميم عصري بألوان متدرجة وأنماط جذابة
- **شريط جانبي متدرج**: تصميم بخلفية متدرجة مع أيقونات وأزرار تفاعلية
- **بطاقات إحصائية متطورة**: بطاقات بتدرجات لونية وأيقونات تعبيرية
- **أنماط متسقة**: نظام ألوان موحد في جميع أنحاء التطبيق

### 📊 الرسوم البيانية والإحصائيات
- **رسوم بيانية تفاعلية**: رسوم دائرية وخطية وعمودية
- **لوحة تحكم شاملة**: عرض الإحصائيات الرئيسية والرسوم البيانية
- **تحديث تلقائي**: الرسوم البيانية تتحدث تلقائياً كل 5 ثوان
- **تقارير متقدمة**: تقارير مفصلة مع رسوم بيانية لكل قسم

### 🏪 إدارة المنتجات
- **إضافة/تعديل/حذف المنتجات**
- **إدارة الفئات والأسعار**
- **تتبع مستويات المخزون**
- **تنبيهات المخزون المنخفض**
- **بحث وفلترة متقدمة**

### 💰 إدارة فواتير البيع
- **إنشاء فواتير بيع جديدة**
- **تتبع حالة الدفع**
- **طباعة الفواتير**
- **إدارة تفاصيل المنتجات**
- **حساب الضرائب والخصومات**

### 📦 إدارة المخزون
- **مراقبة مستويات المخزون**
- **حركات الإضافة والخصم**
- **تقارير حركة المخزون**
- **إنذارات المخزون المنخفض**
- **شرائط تقدم بصرية لمستويات المخزون**

### 👥 إدارة العملاء
- **إضافة وإدارة بيانات العملاء**
- **تتبع أرصدة العملاء**
- **سجل تاريخ المعاملات**
- **تقارير أفضل العملاء**

### 📈 التقارير المتقدمة
- **تقرير المبيعات**: مع رسوم بيانية للمبيعات الشهرية
- **تقرير المخزون**: رسوم دائرية لتوزيع المخزون
- **تقرير الأرباح**: مقارنة الإيرادات والتكاليف
- **تقرير العملاء**: أفضل العملاء وتحليل السلوك

## 🛠️ التقنيات المستخدمة

- **Python 3.8+**: لغة البرمجة الأساسية
- **PySide6**: إطار عمل واجهة المستخدم
- **SQLite**: قاعدة البيانات المدمجة
- **Qt Designer**: تصميم الواجهات
- **Custom Charts**: رسوم بيانية مخصصة

## 🚀 التثبيت والتشغيل

### المتطلبات
```bash
pip install PySide6
```

### التشغيل

**🚀 الطريقة الموصى بها (الإصدار النهائي):**
```bash
python final_app.py
```

**طرق أخرى:**
```bash
# التطبيق المحسن مع شاشة البداية
python run_enhanced_app.py

# التطبيق المحسن البسيط
python run_app.py

# اختبار سريع
python simple_test.py

# الطريقة التقليدية
python -m accounting_app.main
```

### 🎯 ميزات الإصدار النهائي:
- ✨ شاشة بداية جذابة مع تدرجات لونية
- 🔍 فحص تلقائي لمتطلبات النظام
- 🎨 واجهة مستخدم محسنة ومتطورة
- 🌐 دعم كامل للغتين العربية والإنجليزية
- ⚡ تحسينات في الأداء والاستقرار

### بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: admin

## 📁 هيكل المشروع

```
accounting_app/
├── main.py                 # نقطة البداية
├── bll/                    # طبقة منطق الأعمال
│   ├── user_manager.py     # إدارة المستخدمين
│   ├── translator.py       # نظام الترجمة
│   └── settings_manager.py # إدارة الإعدادات
├── dal/                    # طبقة الوصول للبيانات
│   └── database.py         # إدارة قاعدة البيانات
└── ui/                     # واجهات المستخدم
    ├── styles.py           # أنماط التطبيق
    ├── login_window.py     # نافذة تسجيل الدخول
    ├── dashboard_window.py # لوحة التحكم الرئيسية
    ├── products_window.py  # إدارة المنتجات
    ├── sales_window.py     # فواتير البيع
    ├── customers_window.py # إدارة العملاء
    ├── inventory_window.py # إدارة المخزون
    ├── reports_window.py   # التقارير
    └── charts_widget.py    # الرسوم البيانية
```

## 🎯 الميزات المتقدمة

### تصميم responsive
- التطبيق يتكيف مع أحجام الشاشات المختلفة
- أزرار وعناصر تفاعلية مع تأثيرات الماوس

### نظام الترجمة
- دعم للغة العربية والإنجليزية
- واجهة RTL للعربية

### الأمان
- تشفير كلمات المرور
- جلسات المستخدمين
- صلاحيات متدرجة

### قاعدة البيانات
- قاعدة بيانات SQLite مدمجة
- نسخ احتياطية تلقائية
- هيكل بيانات محسن

## 🔄 التحديثات المستقبلية

- [ ] إضافة المزيد من أنواع التقارير
- [ ] تكامل مع الطابعات
- [ ] نظام النسخ الاحتياطي المتقدم
- [ ] واجهة ويب للوصول عن بُعد
- [ ] تطبيق موبايل مصاحب

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

---

**ملاحظة**: هذا نظام تجريبي للأغراض التعليمية والتطويرية. يُنصح بإجراء اختبارات شاملة قبل الاستخدام في بيئة الإنتاج.